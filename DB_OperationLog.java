package com.epoint.ztbbasetemp;

import java.util.Date;
import java.util.List;
import java.util.UUID;

import com.epoint.core.grammar.Record;
import com.epoint.core.utils.config.BaseConfigUtil;
import com.epoint.core.utils.container.ContainerFactory;
import com.epoint.database.jdbc.connection.DataSourceConfig;
import com.epoint.frame.service.organ.ou.api.IFrameOuService;
import com.epoint.frame.service.organ.ou.entity.FrameOu;
import com.epoint.frame.service.organ.user.api.IFrameUserService;
import com.epoint.frame.service.organ.user.entity.FrameUser;
import com.epoint.ztb.bidencrypt.EpZtbEncrypt;
import com.epoint.ztb.common.database.ZtbCommonDao;
import com.epoint.ztb.common.util.Functions;
import com.epoint.ztb.entity.SysOperationLog;
import com.epoint.ztb.enums.SysEnums;
import com.epoint.ztb.session.ContextHelper;
import com.epoint.ztb.session.MemberSession;

public class DB_OperationLog
{
    private ZtbCommonDao service;

    public DB_OperationLog() {
        service = ZtbCommonDao.getInstance();
    }

    public DB_OperationLog(DataSourceConfig dataSourceConfig) {
        service = ZtbCommonDao.getInstance(dataSourceConfig);
    }

    public DB_OperationLog(ZtbCommonDao service) {
        this.service = service;
    }

    /**
     * 记录日志
     *
     * @param ProjectGuid
     * @param OrgGuid
     * @param SubSystem
     * @param FromIP
     * @param OperateType
     * @param LuRuType
     * @param OperateContent
     * @param UserGuid
     * @param XiaQuCode
     * @param ProjectName
     * @param OperateStep
     * @param safeLevel
     */
    public void addOperationLog(String ProjectGuid, String OrgGuid, SysEnums.SubSysName SubSystem, String FromIP,
            SysEnums.OperName OperateType, SysEnums.LuRuType LuRuType, String OperateContent, String UserGuid,
            String XiaQuCode, String ProjectName, String OperateStep, String safeLevel) {
        SysOperationLog bean = new SysOperationLog();
        bean.setProjectGuid(ProjectGuid);
        bean.setXiaQuCode(XiaQuCode);
        bean.setSubSystemCode((SubSystem == null) ? null : SubSystem.getValue());
        bean.setFromIP(FromIP);
        bean.setUserType((LuRuType == null) ? null : LuRuType.getValue());
        bean.setOperateType((OperateType == null) ? null : OperateType.getValue());
        bean.setOperateContent(OperateContent);
        bean.setUserGuid(UserGuid);
        // bean.setOperateUserName(OperateUserName);
        // bean.setOperateUnit(OperateUnit);
        bean.setOperateTime(new Date());
        bean.setRowGuid(UUID.randomUUID().toString());
        bean.setOperateStep(OperateStep);
        bean.setProjectName(ProjectName);
        bean.setSafeLevel(safeLevel);
        bean.setOrgGuid(OrgGuid);
        service.insert(bean);
    }

    /**
     * mongodb中Sys_OperationLog日志集合名称
     * TODO: MongoDB记录日志的未处理
     */
    public static final String MongoDBCollectionName_Sys_OperationLog = "Sys_OperationLog";
    // private static boolean IsUseNewLog = true;// 新日志操作处理

    public static void Sys_OperationLog_Add_NoTran(String projectGuid, SysEnums.SubSysName subSystem,
            SysEnums.OperName operateType, String operateContent, String operateStep) {
        Sys_OperationLog_Add_NoTran(projectGuid, subSystem.getValue(), operateType.getValue(), operateContent,
                operateStep);
    }

    @Deprecated
    public static void Sys_OperationLog_Add_NoTran(String projectGuid, String subSystemCode, String operateType,
            String operateContent, String operateStep) {
        if (isUseMongoDB()) {
            // LogEntity.Entity_Sys_OperationLog _Entity_Sys_OperationLog = new
            // LogEntity.Entity_Sys_OperationLog();
            // _Entity_Sys_OperationLog.ProjectGuid = ProjectGuid;
            // _Entity_Sys_OperationLog.SubSystemCode = SubSystemCode;
            // _Entity_Sys_OperationLog.OperateType = OperateType;
            // _Entity_Sys_OperationLog.OperateContent = OperateContent;
            // _Entity_Sys_OperationLog.OperateStep = OperateStep;
            // Epoint.Frame.Common.MongoDBOperate.InsertOne<LogEntity.Entity_Sys_OperationLog>(MongoDBCollectionName_Sys_OperationLog,
            // _Entity_Sys_OperationLog);
        }
        else {
            String operateUnit = "";
            String operateUserName = ContextHelper.getDisplayName();
            String fromIP = Functions.getClientIP();
            String userType = ContextHelper.isZBB() ? SysEnums.LuRuType.中心人员.getValue() + ""
                    : SysEnums.LuRuType.外部会员.getValue() + "";
            String userGuid = ContextHelper.getUserguid();
            String xiaQuCode = ContextHelper.getOuCode();
            // if (userType.equals(LuRuType.外部会员.getValue() + "")) {
            // operateUnit = ContextHelper.getdanweiname();
            // }
            // else if (userType.equals(LuRuType.中心人员.getValue() + "")) {
            // operateUnit = ContextHelper.getdanweiname();
            // }
            // 代码块逻辑相同，故合并
            if (userType.equals(SysEnums.LuRuType.外部会员.getValue() + "")
                    || userType.equals(SysEnums.LuRuType.中心人员.getValue() + "")) {
                operateUnit = ContextHelper.getdanweiname();
            }

            ZtbCommonDao db = ZtbCommonDao.getInstance();

            SysOperationLog bean = new SysOperationLog();
            bean.setProjectGuid(projectGuid);
            bean.setXiaQuCode(xiaQuCode);
            bean.setSubSystemCode(subSystemCode);
            bean.setFromIP(fromIP);
            bean.setUserType(userType);
            bean.setOperateType(operateType);
            bean.setOperateContent(operateContent);
            bean.setUserGuid(userGuid);
            bean.setOperateUserName(operateUserName);
            bean.setOperateUnit(operateUnit);
            bean.setOperateTime(new Date());
            bean.setRowGuid(UUID.randomUUID().toString());
            bean.setOperateStep(operateStep);
            // bean.setProjectName(_Entity_Sys_OperationLog.get_ProjectName());
            // bean.setSafeLevel(safeLevel);
            // bean.setOrgGuid(_Entity_Sys_OperationLog.get_OrgGuid());
            db.insert(bean);
        }
    }

    /**
     * edit 增加mongodb日志的存入类型，通过web.config应用程序配置LogType=mongodb判断
     */
    public static void addNewOperLog_NoTran(String projectGuid, String orgGuid, String subSystemCode, String fromIP,
            String operateType, String userType, String operateContent, String operateUserName, String xiaQuCode,
            String projectName) {
        if (isUseMongoDB()) {
            // LogEntity.Entity_Sys_OperationLog _Entity_Sys_OperationLog = new
            // LogEntity.Entity_Sys_OperationLog(false);
            // _Entity_Sys_OperationLog.ProjectGuid = ProjectGuid;
            // _Entity_Sys_OperationLog.SubSystemCode = SubSystemCode;
            // _Entity_Sys_OperationLog.OperateType = OperateType;
            // _Entity_Sys_OperationLog.OperateContent = OperateContent;
            // _Entity_Sys_OperationLog.XiaQuCode = XiaQuCode;
            // _Entity_Sys_OperationLog.ProjectName = ProjectName;
            // _Entity_Sys_OperationLog.UserType = UserType;
            // _Entity_Sys_OperationLog.FromIP = FromIP;
            // _Entity_Sys_OperationLog.OrgGuid = OrgGuid;
            // _Entity_Sys_OperationLog.OperateUserName = OperateUserName;
            // Epoint.Frame.Common.MongoDBOperate.InsertOne<LogEntity.Entity_Sys_OperationLog>(MongoDBCollectionName_Sys_OperationLog,
            // _Entity_Sys_OperationLog);
        }
        else {
            ZtbCommonDao db = ZtbCommonDao.getInstance();

            SysOperationLog bean = new SysOperationLog();
            bean.setProjectGuid(projectGuid);
            bean.setXiaQuCode(xiaQuCode);
            bean.setSubSystemCode(subSystemCode);
            bean.setFromIP(fromIP);
            bean.setUserType(userType);
            bean.setOperateType(operateType);
            bean.setOperateContent(operateContent);
            // bean.setUserGuid(userGuid);
            bean.setOperateUserName(operateUserName);
            // bean.setOperateUnit(operateUnit);
            bean.setOperateTime(new Date());
            bean.setRowGuid(UUID.randomUUID().toString());
            // bean.setOperateStep(operateStep);
            bean.setProjectName(projectName.length() > 500 ? projectName.substring(0, 500) : projectName);
            // bean.setSafeLevel(safeLevel);
            bean.setOrgGuid(orgGuid);
            db.insert(bean);
        }
    }

    /**
     *
     * 日志添加(不带事务) add by feizy 2014-03-13
     * 操作人类型,Ip地址，UserGuid和XiaQuCode无需传入,通过Session获取。
     * 增加mongodb日志的存入类型，通过web.config应用程序配置LogType=mongodb判断 添加重要字段安全日志等级参数
     *
     * @param ProjectGuid
     *            事项的GUID，比如标段GUID，公告GUID，预留字段扩展功能用
     * @param SubSystemCode
     *            子系统编码
     * @param OperateType
     *            操作类型
     * @param OperateContent
     *            操作内容
     * @param OperateStep
     *            操作步骤
     * @param SafeLevel
     *            日志安全等级
     * @param NeedEncrypt
     *            如果需要加密，传true，并且ProjectGuid需赋值为相应的BiaoDuanGuid，
     *            SubSystemCode为相应的项目类型
     */
    public static void Sys_OperationLog_Add_NoTran(String ProjectGuid, String SubSystemCode, String OperateType,
            String OperateContent, String OperateStep, String SafeLevel, boolean NeedEncrypt) {
        ZtbCommonDao service = ZtbCommonDao.getInstance();
        if (isUseMongoDB()) {
            // LogEntity.Entity_Sys_OperationLog _Entity_Sys_OperationLog = new
            // LogEntity.Entity_Sys_OperationLog();
            // _Entity_Sys_OperationLog.ProjectGuid = ProjectGuid;
            // _Entity_Sys_OperationLog.SubSystemCode = SubSystemCode;
            // _Entity_Sys_OperationLog.OperateType = OperateType;
            // _Entity_Sys_OperationLog.OperateContent = OperateContent;
            // _Entity_Sys_OperationLog.OperateStep = OperateStep;
            // _Entity_Sys_OperationLog.SafeLevel = SafeLevel;
            // if (NeedEncrypt)
            // {
            // Epoint.ZtbMis.Bizlogic.Common.EpZtbEncrypt.Instance.EncryEntity<LogEntity.Entity_Sys_OperationLog>(
            // "Sys_OperationLog",
            // _Entity_Sys_OperationLog,
            // ProjectGuid,
            // (EpointBid_Constant.Enums.ClsEnum.SubSysName)Enum.Parse(typeof(EpointBid_Constant.Enums.ClsEnum.SubSysName),
            // SubSystemCode)
            // );
            // }
            // Epoint.Frame.Common.MongoDBOperate.InsertOne<LogEntity.Entity_Sys_OperationLog>(MongoDBCollectionName_Sys_OperationLog,
            // _Entity_Sys_OperationLog);
        }
        else {
            String OperateUnit = "";
            String OperateUserName = ContextHelper.getDisplayName();
            String FromIP = Functions.getClientIP();
            String UserType = ContextHelper.isZBB() ? SysEnums.LuRuType.中心人员.getValue()
                    : SysEnums.LuRuType.外部会员.getValue();
            String UserGuid = MemberSession.getInstance().getUserGuid();
            String XiaQuCode = ContextHelper.getOuCode();
            if ((SysEnums.LuRuType.外部会员.getValue()).equals(UserType)) {
                OperateUnit = ContextHelper.getdanweiname();
            }
            else if ((SysEnums.LuRuType.中心人员.getValue()).equals(UserType)) {
                OperateUnit = MemberSession.getInstance().getOuName();
            }

            SysOperationLog bean = new SysOperationLog();
            bean.setProjectGuid(ProjectGuid);
            bean.setXiaQuCode(XiaQuCode);
            bean.setSubSystemCode(SubSystemCode);
            bean.setFromIP(FromIP);
            bean.setUserType(UserType);
            bean.setOperateType(OperateType);
            bean.setOperateContent(OperateContent);
            bean.setUserGuid(UserGuid);
            bean.setOperateUserName(OperateUserName);
            bean.setOperateUnit(OperateUnit);
            bean.setOperateTime(new Date());
            bean.setRowGuid(UUID.randomUUID().toString());
            bean.setOperateStep(OperateStep);
            // bean.setProjectName(_Entity_Sys_OperationLog.get_ProjectName());
            bean.setSafeLevel(SafeLevel);
            // bean.setOrgGuid(_Entity_Sys_OperationLog.get_OrgGuid());

            if (NeedEncrypt) {
                EpZtbEncrypt.getInstance().EncryoRow(bean, ProjectGuid, SysEnums.SubSysName.getEnum(SubSystemCode));
            }
            service.insert(bean);
        }
    }

    /**
     * 根据web.config判断是否需要使用mongodb记录日志
     */
    public static boolean isUseMongoDB() {
        return "mongodb".equals(BaseConfigUtil.getConfigValue("LogType"));
    }

    /**
     * 对于无权限访问的页面，在跳转到haveNoRights.aspx页面前先记录日志
     */
    public static void logPageNoRights() {
        Entity_Sys_OperationLog _Entity_Sys_OperationLog = new Entity_Sys_OperationLog();
        _Entity_Sys_OperationLog.set_ProjectGuid("");
        _Entity_Sys_OperationLog.set_ProjectName(Functions.getRequestURL());
        _Entity_Sys_OperationLog.set_SubSystemCode("");
        _Entity_Sys_OperationLog.set_OperateType(SysEnums.OperName.查看.getValue());
        _Entity_Sys_OperationLog.OperateContent = "尝试访问无权限页面" + Functions.getRequestURL();
        _Entity_Sys_OperationLog.set_OperateStep("页面无权限访问");
        DB_OperationLog.Sys_OperationLog_Add_NoTran(_Entity_Sys_OperationLog);
    }

    public static void Sys_OperationLog_Add_NoTran(Entity_Sys_OperationLog _Entity_Sys_OperationLog) {
        Sys_OperationLog_Add_NoTran(_Entity_Sys_OperationLog, false);
    }

    /**
     * 用实体类实现日志记录，可以统一使用 add by 张佩龙 2014-05-21
     *
     * @param _Entity_Sys_OperationLog
     *            Epoint.ZtbMis.Bizlogic.LogEntity.Entity_Sys_OperationLog实体
     * @param NeedEncrypt
     *            如果需要加密，传true，并且实体类的ProjectGuid需赋值为相应的BiaoDuanGuid，
     *            SubSystemCode为相应的项目类型
     */
    public static void Sys_OperationLog_Add_NoTran(Entity_Sys_OperationLog _Entity_Sys_OperationLog,
            boolean NeedEncrypt) {
        ZtbCommonDao service = ZtbCommonDao.getInstance();

        SysOperationLog bean = new SysOperationLog();
        bean.setProjectGuid(_Entity_Sys_OperationLog.get_ProjectGuid());
        bean.setXiaQuCode(_Entity_Sys_OperationLog.get_XiaQuCode());
        bean.setSubSystemCode(_Entity_Sys_OperationLog.get_SubSystemCode());
        bean.setFromIP(_Entity_Sys_OperationLog.get_FromIP());
        bean.setUserType(_Entity_Sys_OperationLog.get_UserType());
        bean.setOperateType(_Entity_Sys_OperationLog.get_OperateType());
        bean.setOperateContent(_Entity_Sys_OperationLog.OperateContent);
        bean.setUserGuid(_Entity_Sys_OperationLog.get_UserGuid());
        bean.setOperateUserName(_Entity_Sys_OperationLog.get_OperateUserName());
        bean.setOperateUnit(_Entity_Sys_OperationLog.get_OperateUnit());
        bean.setOperateTime(new Date());
        bean.setRowGuid(UUID.randomUUID().toString());
        bean.setOperateStep(_Entity_Sys_OperationLog.get_OperateStep());
        bean.setProjectName(_Entity_Sys_OperationLog.get_ProjectName().length() > 500
                ? _Entity_Sys_OperationLog.get_ProjectName().substring(0, 500)
                : _Entity_Sys_OperationLog.get_ProjectName());
        bean.setSafeLevel(_Entity_Sys_OperationLog.get_SafeLevel());
        bean.setOrgGuid(_Entity_Sys_OperationLog.get_OrgGuid());

        if (NeedEncrypt) {
            EpZtbEncrypt.getInstance().EncryoRow(bean, bean.getProjectGuid(),
                    SysEnums.SubSysName.getEnum(bean.getSubSystemCode()));
        }

        if (isUseMongoDB()) {
            // Epoint.Frame.Common.MongoDBOperate.InsertOne<LogEntity.Entity_Sys_OperationLog>(MongoDBCollectionName_Sys_OperationLog,
            // _Entity_Sys_OperationLog);
        }
        else {
            service.insert(bean);
        }
    }

    /**
     * 用实体类实现日志记录
     *
     * @param _Entity_Sys_OperationLog
     */
    public void sys_OperationLog_Add_NoTran(Entity_Sys_OperationLog _Entity_Sys_OperationLog) {
        Sys_OperationLog_Add_NoTran(_Entity_Sys_OperationLog, false);
    }

    /**
     * 用实体类实现日志记录，可以统一使用
     *
     * @param _Entity_Sys_OperationLog
     *            Epoint.ZtbMis.Bizlogic.LogEntity.Entity_Sys_OperationLog实体
     * @param NeedEncrypt
     *            如果需要加密，传true，并且实体类的ProjectGuid需赋值为相应的BiaoDuanGuid，
     *            SubSystemCode为相应的项目类型
     */
    public void sys_OperationLog_Add_NoTran(Entity_Sys_OperationLog _Entity_Sys_OperationLog, boolean NeedEncrypt) {
        SysOperationLog bean = new SysOperationLog();
        bean.setProjectGuid(_Entity_Sys_OperationLog.get_ProjectGuid());
        bean.setXiaQuCode(_Entity_Sys_OperationLog.get_XiaQuCode());
        bean.setSubSystemCode(_Entity_Sys_OperationLog.get_SubSystemCode());
        bean.setFromIP(_Entity_Sys_OperationLog.get_FromIP());
        bean.setUserType(_Entity_Sys_OperationLog.get_UserType());
        bean.setOperateType(_Entity_Sys_OperationLog.get_OperateType());
        bean.setOperateContent(_Entity_Sys_OperationLog.OperateContent);
        bean.setUserGuid(_Entity_Sys_OperationLog.get_UserGuid());
        bean.setOperateUserName(_Entity_Sys_OperationLog.get_OperateUserName());
        bean.setOperateUnit(_Entity_Sys_OperationLog.get_OperateUnit());
        bean.setOperateTime(new Date());
        bean.setRowGuid(UUID.randomUUID().toString());
        bean.setOperateStep(_Entity_Sys_OperationLog.get_OperateStep());
        bean.setProjectName(_Entity_Sys_OperationLog.get_ProjectName().length() > 500
                ? _Entity_Sys_OperationLog.get_ProjectName().substring(0, 500)
                : _Entity_Sys_OperationLog.get_ProjectName());
        bean.setSafeLevel(_Entity_Sys_OperationLog.get_SafeLevel());
        bean.setOrgGuid(_Entity_Sys_OperationLog.get_OrgGuid());

        if (NeedEncrypt) {
            EpZtbEncrypt.getInstance(service).EncryoRow(bean, bean.getProjectGuid(),
                    SysEnums.SubSysName.getEnum(bean.getSubSystemCode()));
        }

        if (isUseMongoDB()) {
            // Epoint.Frame.Common.MongoDBOperate.InsertOne<LogEntity.Entity_Sys_OperationLog>(MongoDBCollectionName_Sys_OperationLog,
            // _Entity_Sys_OperationLog);
        }
        else {
            service.insert(bean);
        }
    }

    /**
     * edit 张佩龙 增加mongodb日志的存入类型，通过web.config应用程序配置LogType=mongodb判断
     *
     * @param ProjectGuid
     *            当前标段或项目的唯一标识
     * @param OrgGuid
     *            原始标段或项目的唯一标识
     * @param SubSystemCode
     *            子系统编码
     * @param FromIP
     *            ip来源
     * @param OperateType
     *            使用者类型
     * @param UserType
     *            操作类型
     * @param OperateContent
     *            操作内容
     * @param UserGuid
     *            操作者
     * @param XiaQuCode
     *            辖区代码
     * @param ProjectName
     * @param OperateStep
     *
     *
     */
    public static void AddNewOperNewLog_NoTran(String ProjectGuid, String OrgGuid, String SubSystemCode, String FromIP,
            String OperateType, String UserType, String OperateContent, String UserGuid, String XiaQuCode,
            String ProjectName, String OperateStep) {
        if (isUseMongoDB()) {
            // LogEntity.Entity_Sys_OperationLog _Entity_Sys_OperationLog = new
            // LogEntity.Entity_Sys_OperationLog(UserType, UserGuid);
            // _Entity_Sys_OperationLog.ProjectGuid = ProjectGuid;
            // _Entity_Sys_OperationLog.SubSystemCode = SubSystemCode;
            // _Entity_Sys_OperationLog.OperateType = OperateType;
            // _Entity_Sys_OperationLog.OperateContent = OperateContent;
            // _Entity_Sys_OperationLog.XiaQuCode = XiaQuCode;
            // _Entity_Sys_OperationLog.ProjectName = ProjectName;
            // _Entity_Sys_OperationLog.FromIP = FromIP;
            // _Entity_Sys_OperationLog.OrgGuid = OrgGuid;
            // _Entity_Sys_OperationLog.OperateStep = OperateStep;
            // Epoint.Frame.Common.MongoDBOperate.InsertOne<LogEntity.Entity_Sys_OperationLog>(MongoDBCollectionName_Sys_OperationLog,
            // _Entity_Sys_OperationLog);
        }
        else {
            String operateUnit = "";
            String operateUserName = "";
            List<Record> dv;
            ZtbCommonDao service = ZtbCommonDao.getInstance();
            if (UserType.equals(SysEnums.LuRuType.外部会员.getValue())) {
                dv = service.findList(" select sbr_unitname,displayname from huiyuan_user where UserGuid = ?",
                        Record.class, UserGuid);
                if (!dv.isEmpty()) {
                    operateUserName = dv.get(0).getStr("displayname");
                    operateUnit = dv.get(0).getStr("sbr_unitname");
                }
            }
            else if (UserType.equals(SysEnums.LuRuType.中心人员.getValue())) {
                // 不允许直接查框架的表，修改为调用框架方法查询
                FrameUser frameuser = ContainerFactory.getContainInfo().getComponent(IFrameUserService.class)
                        .getUserByUserField("userguid", UserGuid);
                if (frameuser != null) {
                    FrameOu frameou = ContainerFactory.getContainInfo().getComponent(IFrameOuService.class)
                            .getFrameOu(frameuser.getOuGuid());
                    if (frameou != null) {
                        operateUserName = frameuser.getDisplayName();
                        operateUnit = frameou.getOuname();
                    }
                }

            }

            SysOperationLog bean = new SysOperationLog();
            bean.setProjectGuid(ProjectGuid);
            bean.setXiaQuCode(XiaQuCode);
            bean.setSubSystemCode(SubSystemCode);
            bean.setFromIP(FromIP);
            bean.setUserType(UserType);
            bean.setOperateType(OperateType);
            bean.setOperateContent(OperateContent);
            bean.setUserGuid(UserGuid);
            bean.setOperateUserName(operateUserName);
            bean.setOperateUnit(operateUnit);
            bean.setOperateTime(new Date());
            bean.setRowGuid(UUID.randomUUID().toString());
            bean.setOperateStep(OperateStep);
            bean.setProjectName(ProjectName.length() > 500 ? ProjectName.substring(0, 500) : ProjectName);
            // bean.setSafeLevel(SafeLevel);
            bean.setOrgGuid(OrgGuid);
            service.insert(bean);
        }
    }

    /**
     * frame_logerror表新增记录
     *
     * @param SourceGuid
     * @param ErrorMessage
     * @param note
     */
    public static void InsertFrameLogError(String SourceGuid, String ErrorMessage, String note) {
        String strSql = "insert into frame_logerror (rowguid,sourceguid,errormessage,note,adddatetime) values (?,?,?,?,?)";
        ZtbCommonDao service = ZtbCommonDao.getInstance();
        service.execute(strSql, UUID.randomUUID().toString(), SourceGuid, ErrorMessage, note, new Date());
    }
}
