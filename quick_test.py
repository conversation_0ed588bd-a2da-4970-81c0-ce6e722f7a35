#!/usr/bin/env python3
"""
快速Python环境测试
"""

import sys
import os
from datetime import datetime

def main():
    print("🚀 快速Python环境测试")
    print("=" * 40)
    
    # 基本信息
    print(f"Python版本: {sys.version.split()[0]}")
    print(f"当前时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"工作目录: {os.getcwd()}")
    
    # 简单计算
    result = sum(range(1, 101))  # 1到100的和
    print(f"1到100的和: {result}")
    
    # 字符串处理
    text = "Hello Python!"
    print(f"字符串测试: {text.upper()}")
    
    # 列表操作
    numbers = [1, 2, 3, 4, 5]
    squared = [x**2 for x in numbers]
    print(f"平方数列表: {squared}")
    
    # 文件测试
    test_file = "quick_test.tmp"
    try:
        with open(test_file, 'w') as f:
            f.write("测试文件内容")
        with open(test_file, 'r') as f:
            content = f.read()
        os.remove(test_file)
        print(f"文件操作: 成功 (内容: {content})")
    except Exception as e:
        print(f"文件操作: 失败 ({e})")
    
    print("=" * 40)
    print("✅ 快速测试完成！Python环境正常工作！")

if __name__ == "__main__":
    main()
