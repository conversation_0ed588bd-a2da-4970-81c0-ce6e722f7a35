package com.epoint.ztbtoerp.angangrestservice;

import java.util.Date;
import java.util.UUID;

import com.epoint.ag.qyztbmis.baojiacanyu.action.TrustDeedInfoUtil;
import com.epoint.ag.qyztbmis.proxy.entity.CgTrustdeedinfoplus;
import com.epoint.ztbtoerp.util.CommonUtil;
import org.apache.log4j.Logger;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.epoint.Jsgc.domain.CgYichangbdhistory;
import com.epoint.Jsgc.domain.CgZhongbiaogs;
import com.epoint.Jsgc.domain.CgZigeyushen;
import com.epoint.Jsgc.domain.ViewCgYchistoryandbd;
import com.epoint.ZtbCommon.SqlBuilder;
import com.epoint.ZtbCommon.ZtbCommonDao;
import com.epoint.ag.qyztbmis.bizlogic.AGDB_CG_TouBiaoDanWei;
import com.epoint.ag.qyztbmis.bizlogic.erp.angang.AGDB_AnGangInternaldockinginfo;
import com.epoint.ag.qyztbmis.bizlogic.erp.angang.AGDB_AnGangJJInternaldockinginfo;
import com.epoint.ag.qyztbmis.bizlogic.erp.util.ERPCommon;
import com.epoint.ag.qyztbmis.docking.common.LogLevelEnum;
import com.epoint.ag.qyztbmis.docking.common.StatusEnum;
import com.epoint.ag.qyztbmis.docking.common.SystemEnum;
import com.epoint.ag.qyztbmis.docking.common.WriteLogHelper;
import com.epoint.ag.qyztbmis.domain.AGCgBiaoduaninfo;
import com.epoint.ag.qyztbmis.domain.AGCgDanYiLaiYuanLuruInfo;
import com.epoint.ag.qyztbmis.domain.AGCgZhaobiaogginfo;
import com.epoint.ag.qyztbmis.domain.AGCgZhongbiaojieguo;
import com.epoint.ag.qyztbmis.domain.CgTrustdeedchangeinfo;
import com.epoint.ag.qyztbmis.domain.ZhglDataexchangeERP;
import com.epoint.ag.qyztbmis.internaldocking.organizational.StaffQueryServiceByCode;
import com.epoint.ag.qyztbmis.proxy.entity.CgTrustdeedinfo;
import com.epoint.core.grammar.Record;
import com.epoint.core.utils.container.ContainerFactory;
import com.epoint.core.utils.string.StringUtil;
import com.epoint.frame.service.metadata.systemparameters.api.IConfigService;
import com.epoint.frame.service.organ.user.entity.FrameUser;
import com.epoint.lock.ILock;
import com.epoint.lock.LockHelper;
import com.epoint.qyztbmis.domain.CgShoubiaotuijian;
import com.epoint.ztbtoerp.util.ERPFrameSysUtil;
import com.esotericsoftware.minlog.Log;

@RestController
@RequestMapping("/angangerp")
public class AnGangService
{
    private static final Logger logger = Logger.getLogger(AnGangService.class);
    private static final String SYSTEMNAME = SystemEnum.鞍钢ERP.getValue();

    /**
     * 接受采购预告接口
     * E1CG00
     */
    @RequestMapping(value = "/commitCGYGInfo", method = RequestMethod.POST)
    public String reciveCGYGInfo(@RequestBody String receivejson) {
        // 采购预告在委托单之前，为了符合规范，弄一个假的
        /*
         * 暂时不回调
         * CgTrustdeedinfo cgtrustdeedinfo = new CgTrustdeedinfo();
         * cgtrustdeedinfo.setRowguid(UUID.randomUUID().toString());
         * cgtrustdeedinfo.setTrustguid(cgtrustdeedinfo.getRowguid());
         * cgtrustdeedinfo.setFromPlatNo("");
         * cgtrustdeedinfo.setFromPlatNo(modifInfo.getString("sysName"));
         * cgtrustdeedinfo.setERPTrustDeedNo("");// ERP招标委托号
         * cgtrustdeedinfo.setTrustdeedno("");// ERP招标委托号
         */
        ZtbCommonDao service = ZtbCommonDao.getInstance();
        if (StringUtil.isBlank(receivejson)) {
            WriteLogHelper.writeLog(logger, LogLevelEnum.警告, SYSTEMNAME, AnGangApiEnum.采购预告信息.toString(), "接受到的传输信息为空");
            return returnMsg(AnGangApiEnum.采购预告信息, "", StatusEnum.接受失败.getValue(), "传输信息为空!");
        }
        else {
            try {
                WriteLogHelper.writeLog(logger, LogLevelEnum.记录, SYSTEMNAME, AnGangApiEnum.采购预告信息.toString(),
                        "reciveCGYGInfo()接受到的传输信息为:" + receivejson);
                JSONObject modifInfo = JSON.parseObject(receivejson);
                if (modifInfo.getJSONObject("consignation") == null) {
                    return returnMsg(AnGangApiEnum.采购预告信息, "", StatusEnum.接受失败.getValue(), "传输信息为空!");
                }
                ZhglDataexchangeERP dataexchangeerp = new ZhglDataexchangeERP();
                if (StringUtil.isBlank(modifInfo.getString("consignation"))) {
                    return returnMsg(AnGangApiEnum.采购预告信息, "", StatusEnum.接受失败.getValue(), "字段consignation传输信息为空!");
                }
                else {
                    dataexchangeerp.setRowguid(UUID.randomUUID().toString());
                    dataexchangeerp.setDatano(AnGangApiEnum.采购预告信息.getValue());// 委托单
                    dataexchangeerp.setDatacontent(receivejson);
                    dataexchangeerp.setFromplat(SYSTEMNAME);
                    dataexchangeerp.setInserttime(new Date());
                    dataexchangeerp.setDatatype("json");
                    dataexchangeerp.setOperatedate(new Date());
                    dataexchangeerp
                            .setFullclassname("com.epoint.ztbtoerp.angangrestservice.AnGangService@reciveCGYGInfo");
                }

                // 解析数据
                String msg = new AnGangDataUtil().cgyg(modifInfo, service);
                if (StringUtil.isNotBlank(msg)) {
                    WriteLogHelper.writeLog(logger, LogLevelEnum.错误, SYSTEMNAME, AnGangApiEnum.采购预告信息.toString(),
                            "接受采购预告传输信息发生异常,异常原因为:" + msg);
                    // 暂时不回调
                    /*
                     * new AGDB_AnGangInternaldockinginfo().
                     * insertInternaldockingInfoWithBackCount(cgtrustdeedinfo,
                     * "cgyg",
                     * "接受采购预告传输信息发生异常,异常原因为:" + msg, 0, 0);
                     */
                    return returnMsg(AnGangApiEnum.采购预告信息, "", StatusEnum.接受失败.getValue(), "接受传输信息失败,失败原因为:" + msg);
                }
                // 暂时不回调
                /*
                 * new AGDB_AnGangJJInternaldockinginfo().
                 * insertInternaldockingInfoWithBackCount(cgtrustdeedinfo,
                 * "cgyg",
                 * "成功");
                 */
            }
            catch (Exception e) {
                logger.error(e.getMessage(), e);
                WriteLogHelper.writeLog(logger, LogLevelEnum.错误, SYSTEMNAME, AnGangApiEnum.采购预告信息.toString(),
                        "接受采购预告传输信息发生异常,异常原因为:" + e.getMessage());
                // 暂时不回调
                /*
                 * new AGDB_AnGangInternaldockinginfo().
                 * insertInternaldockingInfoWithBackCount(cgtrustdeedinfo,
                 * "cgyg",
                 * "接受采购预告传输信息发生异常,异常原因为:" + e.getMessage(), 0, 0);
                 */
                return returnMsg(AnGangApiEnum.采购预告信息, "", StatusEnum.接受失败.getValue(),
                        "接受采购预告传输信息发生异常,失败原因为:" + e.getMessage());
            }
            finally {
                if (null != service) {
                    service.close();
                }
            }
            return returnMsg(AnGangApiEnum.采购预告信息, "", StatusEnum.接受成功.getValue(), StatusEnum.接受成功.toString());
        }
    }

    /**
     * 接受招标委托方案信息
     * E1CG01
     */
    @RequestMapping(value = "/submitConsignation", method = RequestMethod.POST)
    public String reciveConsignation(@RequestBody String receivejson) {
        ZtbCommonDao service = ZtbCommonDao.getInstance();
        if (StringUtil.isBlank(receivejson)) {
            WriteLogHelper.writeLog(logger, LogLevelEnum.警告, SYSTEMNAME, AnGangApiEnum.招标委托方案信息.toString(),
                    "接受到的传输信息为空");
            return returnMsg(AnGangApiEnum.招标委托方案信息, "", StatusEnum.接受失败.getValue(), "传输信息为空!");
        }
        else {
            try {
                WriteLogHelper.writeLog(logger, LogLevelEnum.记录, SYSTEMNAME, AnGangApiEnum.招标委托方案信息.toString(),
                        "reciveConsignation()接受到的传输信息为:" + receivejson);
                JSONObject json = JSON.parseObject(receivejson);
                if (json.getJSONObject("msgBody") == null) {
                    return returnMsg(AnGangApiEnum.招标委托方案信息, "", StatusEnum.接受失败.getValue(), "传输信息为空!");
                }
                JSONObject modifInfo = json.getJSONObject("msgBody").getJSONObject("data");
                ZhglDataexchangeERP dataexchangeerp = new ZhglDataexchangeERP();
                if (StringUtil.isBlank(modifInfo.getJSONObject("consignation").getString("bidEntrNo"))) {
                    CgTrustdeedinfo cgTrustdeedinfo = new CgTrustdeedinfo();
                    cgTrustdeedinfo.setRowguid(UUID.randomUUID().toString());
                    cgTrustdeedinfo.setTrustguid(cgTrustdeedinfo.getRowguid());
                    cgTrustdeedinfo.setERPTrustDeedNo(modifInfo.getString("bidEntrNo"));// ERP招标委托号
                    cgTrustdeedinfo.setFromPlatNo(modifInfo.getString("sysName"));
                    new AGDB_AnGangInternaldockinginfo().insertInternaldockingInfoWithBackCount(cgTrustdeedinfo,
                            "uninqusending", "字段委托单号bidEntrNo传输信息为空!", 0, 0);
                    return returnMsg(AnGangApiEnum.招标委托方案信息, "", StatusEnum.接受失败.getValue(), "字段委托单号bidEntrNo传输信息为空!");
                }
                else {
                    if (StringUtil.isNotBlank(modifInfo.getString("enterpriseCode"))) {
                        FrameUser wtrMan = new ERPFrameSysUtil()
                                .getFrameUser(modifInfo.getString("enterpriseCode").trim(), service);
                        if (wtrMan == null || wtrMan.isEmpty()) {// 没有获取到的，去掉主数据获取
                            JSONArray codeValue = new JSONArray();
                            codeValue.add(modifInfo.getString("enterpriseCode"));
                            Record errorMsg = new StaffQueryServiceByCode().executeMethod(codeValue);
                            if ("0".equals(errorMsg.getStr("status"))) {// 还没有同步到的，问题提示给攀钢
                                CgTrustdeedinfo cgTrustdeedinfo = new CgTrustdeedinfo();
                                cgTrustdeedinfo.setRowguid(UUID.randomUUID().toString());
                                cgTrustdeedinfo.setTrustguid(cgTrustdeedinfo.getRowguid());
                                cgTrustdeedinfo.setERPTrustDeedNo(modifInfo.getString("bidEntrNo"));// ERP招标委托号
                                cgTrustdeedinfo.setFromPlatNo(modifInfo.getString("sysName"));
                                new AGDB_AnGangInternaldockinginfo().insertInternaldockingInfoWithBackCount(
                                        cgTrustdeedinfo, "uninqusending", "委托人账号同步主数据失败：" + errorMsg.getStr("msg"), 0,
                                        0);
                                return returnMsg(AnGangApiEnum.招标委托方案信息, "", StatusEnum.接受失败.getValue(),
                                        "委托人账号同步主数据失败：" + errorMsg.getStr("msg"));
                            }
                        }
                    }
                    else {
                        CgTrustdeedinfo cgTrustdeedinfo = new CgTrustdeedinfo();
                        cgTrustdeedinfo.setRowguid(UUID.randomUUID().toString());
                        cgTrustdeedinfo.setTrustguid(cgTrustdeedinfo.getRowguid());
                        cgTrustdeedinfo.setERPTrustDeedNo(modifInfo.getString("bidEntrNo"));// ERP招标委托号
                        cgTrustdeedinfo.setFromPlatNo(modifInfo.getString("sysName"));
                        new AGDB_AnGangInternaldockinginfo().insertInternaldockingInfoWithBackCount(cgTrustdeedinfo,
                                "uninqusending", "字段enterpriseCode传输信息为空!", 0, 0);
                        return returnMsg(AnGangApiEnum.招标委托方案信息, "", StatusEnum.接受失败.getValue(),
                                "字段enterpriseCode传输信息为空!");
                    }
                    dataexchangeerp.setRowguid(UUID.randomUUID().toString());
                    dataexchangeerp.setDatano(AnGangApiEnum.招标委托方案信息.getValue());// 委托单
                    dataexchangeerp.setDatacontent(receivejson);
                    dataexchangeerp.setFromplat(SYSTEMNAME);
                    dataexchangeerp.setInfoguid(modifInfo.getJSONObject("consignation").getString("bidEntrNo").trim());
                    dataexchangeerp.setInserttime(new Date());
                    dataexchangeerp.setDatatype("json");
                    dataexchangeerp.setOperatedate(new Date());
                    dataexchangeerp
                            .setFullclassname("com.epoint.ztbtoerp.angangrestservice.AnGangService@reciveConsignation");
                }

                CgTrustdeedinfo cgTrustdeedinfo = getCgTrustDeedInfo(
                        modifInfo.getJSONObject("consignation").getString("bidEntrNo").trim(),
                        modifInfo.getString("sysName").trim(), "", "0", service);
                if (cgTrustdeedinfo == null || cgTrustdeedinfo.isEmpty()) {
                    String trustGuid = UUID.randomUUID().toString();
                    dataexchangeerp.setDatastatus("1");// 新增
                    dataexchangeerp.setDatakey(trustGuid);
                    service.insert(dataexchangeerp);
                    cgTrustdeedinfo = new CgTrustdeedinfo();
                    cgTrustdeedinfo.setRowguid(trustGuid);
                    cgTrustdeedinfo.setTrustguid(trustGuid);
                    cgTrustdeedinfo.setFromplat(SYSTEMNAME);
                    cgTrustdeedinfo.setFromPlatNo(modifInfo.getString("sysName"));
                    JSONObject consignation = modifInfo.getJSONObject("consignation");
                    cgTrustdeedinfo.setERPTrustDeedNo(consignation.getString("bidEntrNo"));
                    String msg = "";
                    try {

                        JSONArray publicAttachment = modifInfo.getJSONObject("consignation")
                                .getJSONObject("publicAttachment").getJSONArray("data");
                        ERPCommon.hasEndFix(publicAttachment);

                        JSONArray noPublicAttachment = modifInfo.getJSONObject("consignation")
                                .getJSONObject("noPublicAttachment").getJSONArray("data");
                        ERPCommon.hasEndFix(noPublicAttachment);
                        msg = new AnGangDataUtil().analysisInfo(true, cgTrustdeedinfo, modifInfo, service);
                    }
                    catch (Exception e) {
                        // TODO: handle exception
                        msg = e.getMessage();
                        WriteLogHelper.writeLog(logger, LogLevelEnum.错误, SYSTEMNAME, AnGangApiEnum.招标委托方案信息.toString(),
                                "接受传输信息发生异常,异常原因为:" + msg);
                        new AGDB_AnGangInternaldockinginfo().insertInternaldockingInfoWithBackCount(cgTrustdeedinfo,
                                "uninqusending", "接受招标委托方案信息发生异常,异常原因为:" + msg, 0, 0);
                        return returnMsg(AnGangApiEnum.招标委托方案信息, "", StatusEnum.接受失败.getValue(),
                                "接受传输信息失败,失败原因为:" + msg);
                    }
                    if (StringUtil.isNotBlank(msg)) {
                        WriteLogHelper.writeLog(logger, LogLevelEnum.错误, SYSTEMNAME, AnGangApiEnum.招标委托方案信息.toString(),
                                "接受传输信息发生异常,异常原因为:" + msg);
                        new AGDB_AnGangInternaldockinginfo().insertInternaldockingInfoWithBackCount(cgTrustdeedinfo,
                                "uninqusending", "接受招标委托方案信息发生异常,异常原因为:" + msg, 0, 0);
                        return returnMsg(AnGangApiEnum.招标委托方案信息, "", StatusEnum.接受失败.getValue(),
                                "接受传输信息失败,失败原因为:" + msg);
                    }
                    cgTrustdeedinfo.setAuditstatus("1");
                    new AGDB_AnGangInternaldockinginfo().insertInternaldockingInfoNull(cgTrustdeedinfo, "inqusending",
                            0, 0);
                    service.insert(cgTrustdeedinfo);
                }
                else if ("2".equals(cgTrustdeedinfo.getAuditstatus())) {
                    dataexchangeerp.setDatastatus("1");// 新增
                    dataexchangeerp.setDatakey(cgTrustdeedinfo.getTrustguid());
                    service.insert(dataexchangeerp);
                    new AGDB_AnGangInternaldockinginfo().insertInternaldockingInfoWithBackCount(cgTrustdeedinfo,
                            "uninqusending", "接受招标委托方案信息发生异常,异常原因为:存在待审核的委托申请，无法进行修改，如需修改请联系管理员退回！", 0, 0);
                    return returnMsg(AnGangApiEnum.招标委托方案信息, "", StatusEnum.接受失败.getValue(),
                            "存在待审核的委托申请，无法进行修改，如需修改请联系管理员退回！");
                }
                else if ("3".equals(cgTrustdeedinfo.getAuditstatus())) {// 审核通过的委托单，如果走过异常需要重新招标的重新生成erp委托单号相同的委托单
                    String ycSql = "select * from VIEW_CG_YCHISTORYANDBD where biaoduanguid=? and AuditStatus='3'";
                    ViewCgYchistoryandbd ycInfo = service.find(ycSql, ViewCgYchistoryandbd.class,
                            cgTrustdeedinfo.getBiaoDuanGuid());
                    if (ycInfo == null || ycInfo.isEmpty()) {// 没有走过招标异常的不认为可以重新招标（不重新生成新的委托单）
                        dataexchangeerp.setDatastatus("1");// 新增
                        dataexchangeerp.setDatakey(cgTrustdeedinfo.getTrustguid());
                        service.insert(dataexchangeerp);
                        new AGDB_AnGangInternaldockinginfo().insertInternaldockingInfoWithBackCount(cgTrustdeedinfo,
                                "uninqusending", "接受招标委托方案信息发生异常,异常原因为:存在进行中的委托申请，无法进行修改，如需修改请走异常再发起重新招标！", 0, 0);
                        return returnMsg(AnGangApiEnum.招标委托方案信息, "", StatusEnum.接受失败.getValue(),
                                "存在进行中的委托申请，无法进行修改，如需修改请走异常再发起重新招标");
                    }
                    else {
                        String trustGuid = UUID.randomUUID().toString();
                        dataexchangeerp.setDatastatus("1");// 新增
                        dataexchangeerp.setDatakey(trustGuid);
                        service.insert(dataexchangeerp);
                        cgTrustdeedinfo = new CgTrustdeedinfo();
                        cgTrustdeedinfo.setRowguid(trustGuid);
                        cgTrustdeedinfo.setTrustguid(trustGuid);
                        cgTrustdeedinfo.setFromplat(SYSTEMNAME);
                        cgTrustdeedinfo.setFromPlatNo(modifInfo.getString("sysName"));
                        JSONObject consignation = modifInfo.getJSONObject("consignation");
                        cgTrustdeedinfo.setERPTrustDeedNo(consignation.getString("bidEntrNo"));
                        String msg = "";
                        try {
                            // 添加判断是否是重新招标的，如果不是抛出异常
                            String string = consignation.getString("IsLastPurchaseSolutionNo");
                            if (!"1".equals(string)) {
                                modifInfo.put("error", "重新招标上次采购方案号填写错误");
                            }
                            JSONArray publicAttachment = modifInfo.getJSONObject("consignation")
                                    .getJSONObject("publicAttachment").getJSONArray("data");
                            ERPCommon.hasEndFix(publicAttachment);

                            JSONArray noPublicAttachment = modifInfo.getJSONObject("consignation")
                                    .getJSONObject("noPublicAttachment").getJSONArray("data");
                            ERPCommon.hasEndFix(noPublicAttachment);
                            msg = new AnGangDataUtil().analysisInfo(true, cgTrustdeedinfo, modifInfo, service);
                        }
                        catch (Exception e) {
                            // TODO: handle exception
                            msg = e.getMessage();
                            WriteLogHelper.writeLog(logger, LogLevelEnum.错误, SYSTEMNAME,
                                    AnGangApiEnum.招标委托方案信息.toString(), "接受传输信息发生异常,异常原因为:" + msg);
                            new AGDB_AnGangInternaldockinginfo().insertInternaldockingInfoWithBackCount(cgTrustdeedinfo,
                                    "uninqusending", "接受招标委托方案信息发生异常,异常原因为:" + msg, 0, 0);
                            return returnMsg(AnGangApiEnum.招标委托方案信息, "", StatusEnum.接受失败.getValue(),
                                    "接受传输信息失败,失败原因为:" + msg);
                        }

                        if (StringUtil.isNotBlank(msg)) {
                            WriteLogHelper.writeLog(logger, LogLevelEnum.错误, SYSTEMNAME,
                                    AnGangApiEnum.招标委托方案信息.toString(), "接受传输信息发生异常,异常原因为:" + msg);
                            new AGDB_AnGangInternaldockinginfo().insertInternaldockingInfoWithBackCount(cgTrustdeedinfo,
                                    "uninqusending", "接受招标委托方案信息发生异常,异常原因为:" + msg, 0, 0);
                            return returnMsg(AnGangApiEnum.招标委托方案信息, "", StatusEnum.接受失败.getValue(),
                                    "接受传输信息失败,失败原因为:" + msg);
                        }
                        cgTrustdeedinfo.setAuditstatus("1");
                        new AGDB_AnGangInternaldockinginfo().insertInternaldockingInfoNull(cgTrustdeedinfo,
                                "inqusending", 0, 0);
                        service.insert(cgTrustdeedinfo);
                    }
                }
                else {
                    dataexchangeerp.setDatakey(cgTrustdeedinfo.getTrustguid());
                    dataexchangeerp.setDatastatus("2");// 修改
                    service.insert(dataexchangeerp);
                    String msg = "";
                    try {
                        JSONArray publicAttachment = modifInfo.getJSONObject("consignation")
                                .getJSONObject("publicAttachment").getJSONArray("data");
                        ERPCommon.hasEndFix(publicAttachment);

                        JSONArray noPublicAttachment = modifInfo.getJSONObject("consignation")
                                .getJSONObject("noPublicAttachment").getJSONArray("data");
                        ERPCommon.hasEndFix(noPublicAttachment);
                        msg = new AnGangDataUtil().analysisInfo(false, cgTrustdeedinfo, modifInfo, service);
                    }
                    catch (Exception e) {
                        // TODO: handle exception
                        msg = e.getMessage();
                        WriteLogHelper.writeLog(logger, LogLevelEnum.错误, SYSTEMNAME, AnGangApiEnum.招标委托方案信息.toString(),
                                "接受传输信息发生异常,异常原因为:" + msg);
                        new AGDB_AnGangInternaldockinginfo().insertInternaldockingInfoWithBackCount(cgTrustdeedinfo,
                                "uninqusending", "接受招标委托方案信息发生异常,异常原因为:" + msg, 0, 0);
                        return returnMsg(AnGangApiEnum.招标委托方案信息, "", StatusEnum.接受失败.getValue(),
                                "接受传输信息失败,失败原因为:" + msg);
                    }
                    if (StringUtil.isNotBlank(msg)) {
                        WriteLogHelper.writeLog(logger, LogLevelEnum.错误, SYSTEMNAME, AnGangApiEnum.招标委托方案信息.toString(),
                                "接受传输信息发生异常,异常原因为:" + msg);
                        new AGDB_AnGangInternaldockinginfo().insertInternaldockingInfoWithBackCount(cgTrustdeedinfo,
                                "uninqusending", "接受招标委托方案信息发生异常,异常原因为:" + msg, 0, 0);
                        return returnMsg(AnGangApiEnum.招标委托方案信息, "", StatusEnum.接受失败.getValue(),
                                "接受传输信息失败,失败原因为:" + msg);
                    }
                    new AGDB_AnGangInternaldockinginfo().insertInternaldockingInfoNull(cgTrustdeedinfo, "inqusending",
                            0, 0);
                    service.update(cgTrustdeedinfo);
                }
                // 自动提交委托单

                IConfigService configService = ContainerFactory.getContainInfo().getComponent(IConfigService.class);
                String frameConfigValue = configService.getFrameConfigValue("自动提交委托单的ERP");
                if (StringUtil.isNotBlank(frameConfigValue) && frameConfigValue.contains("鞍钢ERP")) {
                    ERPCommon.autoCommitWtd(cgTrustdeedinfo.getRowguid(), service);
                }

                logger.info("trustguid=" + cgTrustdeedinfo.getTrustguid());
            }
            catch (Exception e) {
                logger.error(e.getMessage(), e);
                WriteLogHelper.writeLog(logger, LogLevelEnum.错误, SYSTEMNAME, AnGangApiEnum.招标委托方案信息.toString(),
                        "接受传输信息发生异常,异常原因为:" + e.getMessage());
                JSONObject json = JSONObject.parseObject(receivejson);
                JSONObject modifInfo = json.getJSONObject("msgBody");
                CgTrustdeedinfo cgTrustdeedinfo = new CgTrustdeedinfo();
                cgTrustdeedinfo.setRowguid(UUID.randomUUID().toString());
                cgTrustdeedinfo.setTrustguid(cgTrustdeedinfo.getRowguid());
                cgTrustdeedinfo.setERPTrustDeedNo(modifInfo.getString("bidEntrNo"));// ERP招标委托号
                cgTrustdeedinfo.setFromPlatNo(modifInfo.getString("sysName"));
                new AGDB_AnGangInternaldockinginfo().insertInternaldockingInfoWithBackCount(cgTrustdeedinfo,
                        "uninqusending", "接受传输信息发生异常,异常原因为:" + e.getMessage(), 0, 0);
                return returnMsg(AnGangApiEnum.招标委托方案信息, "", StatusEnum.接受失败.getValue(),
                        "接受传输信息失败,失败原因为:" + e.getMessage());
            }
            finally {
                if (null != service) {
                    service.close();
                }
            }

            return returnMsg(AnGangApiEnum.招标委托方案信息, "", StatusEnum.接受成功.getValue(), StatusEnum.接受成功.toString());
        }
    }

    /**
     * 接受新增竞价委托接口
     * E1CG23
     */
    @RequestMapping(value = "/submitJJConsignation", method = RequestMethod.POST)
    public String reciveJJConsignation(@RequestBody String receivejson) {
        ZtbCommonDao service = ZtbCommonDao.getInstance();
        if (StringUtil.isBlank(receivejson)) {
            WriteLogHelper.writeLog(logger, LogLevelEnum.警告, SYSTEMNAME, AnGangApiEnum.新增竞价委托接口.toString(),
                    "接受到的传输信息为空");
            return returnMsg(AnGangApiEnum.新增竞价委托接口, "", StatusEnum.接受失败.getValue(), "传输信息为空!");
        }
        else {
            try {
                WriteLogHelper.writeLog(logger, LogLevelEnum.记录, SYSTEMNAME, AnGangApiEnum.新增竞价委托接口.toString(),
                        "reciveJJConsignation()接受到的传输信息为:" + receivejson);
                JSONObject json = JSON.parseObject(receivejson);
                if (json.getJSONObject("msgBody") == null) {
                    return returnMsg(AnGangApiEnum.新增竞价委托接口, "", StatusEnum.接受失败.getValue(), "传输信息为空!");
                }
                JSONObject modifInfo = json.getJSONObject("msgBody").getJSONObject("data");
                ZhglDataexchangeERP dataexchangeerp = new ZhglDataexchangeERP();
                if (StringUtil.isBlank(modifInfo.getJSONObject("consignation").getString("bidEntrNo"))) {
                    return returnMsg(AnGangApiEnum.新增竞价委托接口, "", StatusEnum.接受失败.getValue(), "字段委托单号bidEntrNo传输信息为空!");
                }
                else {
                    if (StringUtil.isNotBlank(modifInfo.getString("enterpriseCode"))) {
                        FrameUser wtrMan = new ERPFrameSysUtil()
                                .getFrameUser(modifInfo.getString("enterpriseCode").trim(), service);
                        if (wtrMan == null || wtrMan.isEmpty()) {// 没有获取到的，去掉主数据获取
                            JSONArray codeValue = new JSONArray();
                            codeValue.add(modifInfo.getString("enterpriseCode"));
                            Record errorMsg = new StaffQueryServiceByCode().executeMethod(codeValue);
                            if ("0".equals(errorMsg.getStr("status"))) {// 还没有同步到的，问题提示给攀钢
                                CgTrustdeedinfo cgTrustdeedinfo = new CgTrustdeedinfo();
                                cgTrustdeedinfo.setRowguid(UUID.randomUUID().toString());
                                cgTrustdeedinfo.setTrustguid(cgTrustdeedinfo.getRowguid());
                                cgTrustdeedinfo.setERPTrustDeedNo(modifInfo.getString("bidEntrNo"));// ERP招标委托号
                                cgTrustdeedinfo.setFromPlatNo(modifInfo.getString("sysName"));
                                new AGDB_AnGangJJInternaldockinginfo().insertInternaldockingInfoWithBackCount(
                                        cgTrustdeedinfo, "jjwtderr", "委托人账号同步主数据失败：" + errorMsg.getStr("msg"));
                                return returnMsg(AnGangApiEnum.新增竞价委托接口, "", StatusEnum.接受失败.getValue(),
                                        "委托人账号同步主数据失败：" + errorMsg.getStr("msg"));
                            }
                        }
                    }
                    else {
                        CgTrustdeedinfo cgTrustdeedinfo = new CgTrustdeedinfo();
                        cgTrustdeedinfo.setRowguid(UUID.randomUUID().toString());
                        cgTrustdeedinfo.setTrustguid(cgTrustdeedinfo.getRowguid());
                        cgTrustdeedinfo.setERPTrustDeedNo(modifInfo.getString("bidEntrNo"));// ERP招标委托号
                        cgTrustdeedinfo.setFromPlatNo(modifInfo.getString("sysName"));
                        new AGDB_AnGangJJInternaldockinginfo().insertInternaldockingInfoWithBackCount(cgTrustdeedinfo,
                                "jjwtderr", "字段enterpriseCode传输信息为空!");
                        return returnMsg(AnGangApiEnum.新增竞价委托接口, "", StatusEnum.接受失败.getValue(),
                                "字段enterpriseCode传输信息为空!");
                    }
                    dataexchangeerp.setRowguid(UUID.randomUUID().toString());
                    dataexchangeerp.setDatano(AnGangApiEnum.新增竞价委托接口.getValue());// 委托单
                    dataexchangeerp.setDatacontent(receivejson);
                    dataexchangeerp.setFromplat(SYSTEMNAME);
                    dataexchangeerp.setInfoguid(modifInfo.getJSONObject("consignation").getString("bidEntrNo").trim());
                    dataexchangeerp.setInserttime(new Date());
                    dataexchangeerp.setDatatype("json");
                    dataexchangeerp.setOperatedate(new Date());
                    dataexchangeerp.setFullclassname(
                            "com.epoint.ztbtoerp.angangrestservice.AnGangService@reciveJJConsignation");
                }

                CgTrustdeedinfo cgTrustdeedinfo = getCgTrustDeedInfo(
                        modifInfo.getJSONObject("consignation").getString("bidEntrNo").trim(),
                        modifInfo.getString("sysName").trim(), "", "1", service);
                if (cgTrustdeedinfo == null || cgTrustdeedinfo.isEmpty()) {
                    String trustGuid = UUID.randomUUID().toString();
                    dataexchangeerp.setDatastatus("1");// 新增
                    dataexchangeerp.setDatakey(trustGuid);
                    service.insert(dataexchangeerp);
                    cgTrustdeedinfo = new CgTrustdeedinfo();
                    cgTrustdeedinfo.setRowguid(trustGuid);
                    cgTrustdeedinfo.setTrustguid(trustGuid);
                    cgTrustdeedinfo.setFromplat(SYSTEMNAME);
                    cgTrustdeedinfo.setIsErpJingJia("1");
                    String msg = "";
                    try {
                        msg = new AnGangDataUtil().analysisInfo(true, cgTrustdeedinfo, modifInfo, service);
                    }
                    catch (Exception e) {
                        // TODO: handle exception
                        msg = e.getMessage();
                    }
                    if (StringUtil.isNotBlank(msg)) {
                        WriteLogHelper.writeLog(logger, LogLevelEnum.错误, SYSTEMNAME, AnGangApiEnum.新增竞价委托接口.toString(),
                                "接受传输信息发生异常,异常原因为:" + msg);
                        new AGDB_AnGangJJInternaldockinginfo().insertInternaldockingInfoWithBackCount(cgTrustdeedinfo,
                                "jjwtderr", "接受新增竞价委托接口发生异常,异常原因为:" + msg);
                        return returnMsg(AnGangApiEnum.新增竞价委托接口, "", StatusEnum.接受失败.getValue(),
                                "接受传输信息失败,失败原因为:" + msg);
                    }
                    cgTrustdeedinfo.setAuditstatus("3");
                    new AGDB_AnGangJJInternaldockinginfo().insertInternaldockingInfoWithBackCount(cgTrustdeedinfo,
                            "jjwtdok", "成功");
                    service.insert(cgTrustdeedinfo);
                }
                else {
                    String ycSql = "select * from VIEW_CG_YCHISTORYANDBD where biaoduanguid=? and AuditStatus='3'";
                    ViewCgYchistoryandbd ycInfo = service.find(ycSql, ViewCgYchistoryandbd.class,
                            cgTrustdeedinfo.getBiaoDuanGuid());
                    if (ycInfo == null || ycInfo.isEmpty()) {// 没有走过招标异常的不认为可以重新招标（不重新生成新的委托单）
                        dataexchangeerp.setDatastatus("2");// 新增
                        dataexchangeerp.setInfoguid(cgTrustdeedinfo.getERPTrustDeedNo());
                        dataexchangeerp.setDatakey(cgTrustdeedinfo.getRowguid());
                        service.insert(dataexchangeerp);
                        String bjSql = "select count(*) from cg_toubiaobaojia where biaoduanguid=? ";
                        if (service.queryInt(bjSql, cgTrustdeedinfo.getBiaoDuanGuid()) > 0) {
                            WriteLogHelper.writeLog(logger, LogLevelEnum.错误, SYSTEMNAME,
                                    AnGangApiEnum.新增竞价委托接口.toString(), "接受传输信息发生异常,重复接收，已存在报价，不接收");
                            new AGDB_AnGangJJInternaldockinginfo().insertInternaldockingInfoWithBackCount(
                                    cgTrustdeedinfo, "jjwtderr", "接受传输信息发生异常,重复接收，已存在报价，不接收");
                            return returnMsg(AnGangApiEnum.新增竞价委托接口, "", StatusEnum.接受失败.getValue(),
                                    "接受传输信息发生异常,重复接收，已存在报价，不接收");
                        }
                        String msg = "";
                        try {
                            msg = new AnGangDataUtil().analysisInfo(false, cgTrustdeedinfo, modifInfo, service);
                        }
                        catch (Exception e) {
                            // TODO: handle exception
                            msg = e.getMessage();
                        }
                        if (StringUtil.isNotBlank(msg)) {
                            WriteLogHelper.writeLog(logger, LogLevelEnum.错误, SYSTEMNAME,
                                    AnGangApiEnum.新增竞价委托接口.toString(), "接受传输信息发生异常,异常原因为:" + msg);
                            new AGDB_AnGangJJInternaldockinginfo().insertInternaldockingInfoWithBackCount(
                                    cgTrustdeedinfo, "jjwtderr", "接受传输信息失败,失败原因为:" + msg);
                            return returnMsg(AnGangApiEnum.新增竞价委托接口, "", StatusEnum.接受失败.getValue(),
                                    "接受传输信息失败,失败原因为:" + msg);
                        }
                        new AGDB_AnGangJJInternaldockinginfo().insertInternaldockingInfoWithBackCount(cgTrustdeedinfo,
                                "jjwtdok", "成功");
                        service.update(cgTrustdeedinfo);
                    }
                    else {
                        String trustGuid = UUID.randomUUID().toString();
                        dataexchangeerp.setDatastatus("1");// 新增
                        dataexchangeerp.setDatakey(trustGuid);
                        service.insert(dataexchangeerp);
                        cgTrustdeedinfo = new CgTrustdeedinfo();
                        cgTrustdeedinfo.setRowguid(trustGuid);
                        cgTrustdeedinfo.setTrustguid(trustGuid);
                        cgTrustdeedinfo.setFromplat(SYSTEMNAME);
                        cgTrustdeedinfo.setIsErpJingJia("1");
                        String msg = "";
                        try {
                            msg = new AnGangDataUtil().analysisInfo(true, cgTrustdeedinfo, modifInfo, service);
                        }
                        catch (Exception e) {
                            // TODO: handle exception
                            msg = e.getMessage();
                        }
                        if (StringUtil.isNotBlank(msg)) {
                            WriteLogHelper.writeLog(logger, LogLevelEnum.错误, SYSTEMNAME,
                                    AnGangApiEnum.新增竞价委托接口.toString(), "接受传输信息发生异常,异常原因为:" + msg);
                            new AGDB_AnGangJJInternaldockinginfo().insertInternaldockingInfoWithBackCount(
                                    cgTrustdeedinfo, "jjwtderr", "接受新增竞价委托接口发生异常,异常原因为:" + msg);
                            return returnMsg(AnGangApiEnum.新增竞价委托接口, "", StatusEnum.接受失败.getValue(),
                                    "接受传输信息失败,失败原因为:" + msg);
                        }
                        cgTrustdeedinfo.setAuditstatus("3");
                        new AGDB_AnGangJJInternaldockinginfo().insertInternaldockingInfoWithBackCount(cgTrustdeedinfo,
                                "jjwtdok", "成功");
                        service.insert(cgTrustdeedinfo);
                    }

                }
                logger.info("trustguid=" + cgTrustdeedinfo.getTrustguid());
            }
            catch (Exception e) {
                JSONObject json = JSONObject.parseObject(receivejson);
                JSONObject modifInfo = json.getJSONObject("msgBody").getJSONObject("data");
                CgTrustdeedinfo cgTrustdeedinfo = new CgTrustdeedinfo();
                cgTrustdeedinfo.setRowguid(UUID.randomUUID().toString());
                cgTrustdeedinfo.setTrustguid(cgTrustdeedinfo.getRowguid());
                cgTrustdeedinfo.setERPTrustDeedNo(modifInfo.getString("bidEntrNo"));// ERP招标委托号
                cgTrustdeedinfo.setFromPlatNo(modifInfo.getString("sysName"));
                new AGDB_AnGangJJInternaldockinginfo().insertInternaldockingInfoWithBackCount(cgTrustdeedinfo,
                        "jjwtderr", "接受传输信息发生异常," + cgTrustdeedinfo.getRowguid() + "异常原因为:" + e.getMessage());
                logger.error(e.getMessage(), e);
                WriteLogHelper.writeLog(logger, LogLevelEnum.错误, SYSTEMNAME, AnGangApiEnum.新增竞价委托接口.toString(),
                        "接受传输信息发生异常," + cgTrustdeedinfo.getRowguid() + "异常原因为:" + e.getMessage());
                return returnMsg(AnGangApiEnum.新增竞价委托接口, "", StatusEnum.接受失败.getValue(),
                        "接受传输信息失败," + cgTrustdeedinfo.getRowguid() + "失败原因为:" + e.getMessage());
            }
            finally {
                if (null != service) {
                    service.close();
                }
            }
            return returnMsg(AnGangApiEnum.新增竞价委托接口, "", StatusEnum.接受成功.getValue(), StatusEnum.接受成功.toString());
        }
    }

    /**
     * 接受资格预审公告/竞价采购公告接口
     * E1CG21
     */
    @RequestMapping(value = "/commitJJGGInfo", method = RequestMethod.POST)
    public String reciveJJGongGaoInfo(@RequestBody String receivejson) {
        ZtbCommonDao service = ZtbCommonDao.getInstance();
        if (StringUtil.isBlank(receivejson)) {
            WriteLogHelper.writeLog(logger, LogLevelEnum.警告, SYSTEMNAME, AnGangApiEnum.资格预审公告_竞价采购公告接口.toString(),
                    "接受到的传输信息为空");
            return returnMsg(AnGangApiEnum.资格预审公告_竞价采购公告接口, "", StatusEnum.接受失败.getValue(), "传输信息为空!");
        }
        else {
            try {
                WriteLogHelper.writeLog(logger, LogLevelEnum.记录, SYSTEMNAME, AnGangApiEnum.资格预审公告_竞价采购公告接口.toString(),
                        "reciveJJGongGaoInfo()接受到的传输信息为:" + receivejson);
                JSONObject json = JSON.parseObject(receivejson);
                if (json.getJSONObject("msgBody") == null) {
                    return returnMsg(AnGangApiEnum.资格预审公告_竞价采购公告接口, "", StatusEnum.接受失败.getValue(), "传输信息为空!");
                }
                JSONObject modifInfo = json.getJSONObject("msgBody");
                ZhglDataexchangeERP dataexchangeerp = new ZhglDataexchangeERP();
                if (StringUtil.isBlank(modifInfo.getString("bidSectionCode"))) {
                    return returnMsg(AnGangApiEnum.资格预审公告_竞价采购公告接口, "", StatusEnum.接受失败.getValue(),
                            "字段bidSectionCode传输信息为空!");
                }
                else {
                    dataexchangeerp.setRowguid(UUID.randomUUID().toString());
                    dataexchangeerp.setDatano(AnGangApiEnum.资格预审公告_竞价采购公告接口.getValue());// 委托单
                    dataexchangeerp.setDatacontent(receivejson);
                    dataexchangeerp.setFromplat(SYSTEMNAME);
                    dataexchangeerp.setInserttime(new Date());
                    dataexchangeerp.setDatatype("json");
                    dataexchangeerp.setOperatedate(new Date());
                    dataexchangeerp.setFullclassname(
                            "com.epoint.ztbtoerp.angangrestservice.AnGangService@reciveJJGongGaoInfo");
                }
                CgTrustdeedinfo cgTrustdeedinfo = getCgTrustDeedInfoByBiaoBao(
                        modifInfo.getString("bidSectionCode").trim(), modifInfo.getString("sysName").trim(), "3", "1",
                        service);
                if (cgTrustdeedinfo == null || cgTrustdeedinfo.isEmpty()) {
                    dataexchangeerp.setDatastatus("1");// 新增
                    dataexchangeerp.setDatakey(modifInfo.getString("bidSectionCode").trim());
                    service.insert(dataexchangeerp);
                    return returnMsg(AnGangApiEnum.资格预审公告_竞价采购公告接口, "", StatusEnum.接受失败.getValue(),
                            "bidSectionCode数据异常，未找到对应委托!");
                }
                else {
                    dataexchangeerp.setDatastatus("1");// 新增
                    dataexchangeerp.setInfoguid(cgTrustdeedinfo.getERPTrustDeedNo());
                    dataexchangeerp.setDatakey(cgTrustdeedinfo.getRowguid());
                    service.insert(dataexchangeerp);
                    // String sql_change = "select * from CG_zhaobiaogginfo
                    // where 1=1 and zishentype=? "
                    // + "and gonggaoguid in(select gonggaoguid from
                    // CG_ZBGGANDBD where biaoduanguid=? )";
                    // AGCgZhaobiaogginfo zsGGInfo = service.find(sql_change,
                    // AGCgZhaobiaogginfo.class,
                    // cgTrustdeedinfo.getVerifymode(),
                    // "1".equals(cgTrustdeedinfo.getVerifymode()) ? "ZS_" +
                    // cgTrustdeedinfo.getBiaoDuanGuid()
                    // : cgTrustdeedinfo.getBiaoDuanGuid());
                    // if (zsGGInfo == null || zsGGInfo.isEmpty()) {
                    String msg = new AnGangDataUtil().zsGGorJJCgGG(cgTrustdeedinfo, modifInfo, service);
                    if (StringUtil.isNotBlank(msg)) {
                        WriteLogHelper.writeLog(logger, LogLevelEnum.错误, SYSTEMNAME,
                                AnGangApiEnum.资格预审公告_竞价采购公告接口.toString(), "接受传输信息发生异常,异常原因为:" + msg);
                        new AGDB_AnGangJJInternaldockinginfo().insertInternaldockingInfoWithBackCount(cgTrustdeedinfo,
                                "jjzsggerr", "接受传输信息发生异常,异常原因为:" + msg);
                        return returnMsg(AnGangApiEnum.资格预审公告_竞价采购公告接口, "", StatusEnum.接受失败.getValue(),
                                "接受传输信息失败,失败原因为:" + msg);
                    }
                    new AGDB_AnGangJJInternaldockinginfo().insertInternaldockingInfoWithBackCount(cgTrustdeedinfo,
                            "jjzsggok", "成功");
                    // }
                    // else {
                    // new
                    // AGDB_AnGangJJInternaldockinginfo().insertInternaldockingInfoWithBackCount(cgTrustdeedinfo,
                    // "jjzsggerr", "数据异常，已发布过招标公告文件!");
                    // return returnMsg(AnGangApiEnum.资格预审公告_竞价采购公告接口, "",
                    // StatusEnum.接受失败.getValue(),
                    // "数据异常，已发布过招标公告文件!");
                    // }

                }
            }
            catch (Exception e) {
                logger.error(e.getMessage(), e);
                WriteLogHelper.writeLog(logger, LogLevelEnum.错误, SYSTEMNAME, AnGangApiEnum.资格预审公告_竞价采购公告接口.toString(),
                        "接受传输信息发生异常,异常原因为:" + e.getMessage());
                return returnMsg(AnGangApiEnum.资格预审公告_竞价采购公告接口, "", StatusEnum.接受失败.getValue(),
                        "接受传输信息失败,失败原因为:" + e.getMessage());
            }
            finally {
                if (null != service) {
                    service.close();
                }
            }
            return returnMsg(AnGangApiEnum.资格预审公告_竞价采购公告接口, "", StatusEnum.接受失败.getValue(), StatusEnum.接受成功.toString());
        }
    }

    /**
     * 接受资格预审公告/竞价采购公告变更接口
     * E1CG22
     */
    @RequestMapping(value = "/commitJJBGGGInfo", method = RequestMethod.POST)
    public String reciveJJBianGengGongGaoInfo(@RequestBody String receivejson) {
        ZtbCommonDao service = ZtbCommonDao.getInstance();
        if (StringUtil.isBlank(receivejson)) {
            WriteLogHelper.writeLog(logger, LogLevelEnum.警告, SYSTEMNAME, AnGangApiEnum.资格预审_招标变更公告.toString(),
                    "接受到的传输信息为空");
            return returnMsg(AnGangApiEnum.资格预审_招标变更公告, "", StatusEnum.接受失败.getValue(), "传输信息为空!");
        }
        else {
            try {
                WriteLogHelper.writeLog(logger, LogLevelEnum.记录, SYSTEMNAME, AnGangApiEnum.资格预审_招标变更公告.toString(),
                        "reciveJJBianGengGongGaoInfo()接受到的传输信息为:" + receivejson);
                JSONObject json = JSON.parseObject(receivejson);
                if (json.getJSONObject("msgBody") == null) {
                    return returnMsg(AnGangApiEnum.资格预审_招标变更公告, "", StatusEnum.接受失败.getValue(), "传输信息为空!");
                }
                JSONObject modifInfo = json.getJSONObject("msgBody");
                ZhglDataexchangeERP dataexchangeerp = new ZhglDataexchangeERP();
                if (StringUtil.isBlank(modifInfo.getString("bidSectionCode"))) {
                    return returnMsg(AnGangApiEnum.资格预审_招标变更公告, "", StatusEnum.接受失败.getValue(),
                            "字段bidSectionCode传输信息为空!");
                }
                else {
                    dataexchangeerp.setRowguid(UUID.randomUUID().toString());
                    dataexchangeerp.setDatano(AnGangApiEnum.资格预审_招标变更公告.getValue());// 委托单
                    dataexchangeerp.setDatacontent(receivejson);
                    dataexchangeerp.setFromplat(SYSTEMNAME);
                    dataexchangeerp.setInserttime(new Date());
                    dataexchangeerp.setDatatype("json");
                    dataexchangeerp.setOperatedate(new Date());
                    dataexchangeerp.setFullclassname(
                            "com.epoint.ztbtoerp.angangrestservice.AnGangService@reciveJJBianGengGongGaoInfo");
                }
                CgTrustdeedinfo cgTrustdeedinfo = getCgTrustDeedInfoByBiaoBao(
                        modifInfo.getString("bidSectionCode").trim(), modifInfo.getString("sysName").trim(), "3", "1",
                        service);

                if (cgTrustdeedinfo == null || cgTrustdeedinfo.isEmpty()) {
                    dataexchangeerp.setDatastatus("1");// 新增
                    dataexchangeerp.setInfoguid(modifInfo.getString("bidSectionCode").trim());
                    dataexchangeerp.setDatakey(modifInfo.getString("bidSectionCode").trim());
                    service.insert(dataexchangeerp);
                    return returnMsg(AnGangApiEnum.资格预审_招标变更公告, "", StatusEnum.接受失败.getValue(),
                            "bidSectionCode数据异常，未找到对应委托!");
                }
                else {
                    dataexchangeerp.setDatastatus("1");// 新增
                    dataexchangeerp.setInfoguid(cgTrustdeedinfo.getERPTrustDeedNo());
                    dataexchangeerp.setDatakey(cgTrustdeedinfo.getRowguid());
                    service.insert(dataexchangeerp);
                    // String sql_ggandbd = "select * from CG_ZBGGANDBD where
                    // biaoduanguid=?";
                    // CgZbggandbd zbggandbd = service.find(sql_ggandbd,
                    // CgZbggandbd.class,
                    // cgTrustdeedinfo.getBiaoDuanGuid());
                    // if (zbggandbd != null && !zbggandbd.isEmpty()) {
                    // String sql_change = "select * from CG_zhaobiaogginfo
                    // where gonggaoguid=? and ifnull(auditstatus,'') = '3' ";
                    // AGCgZhaobiaogginfo zsGGInfo = service.find(sql_change,
                    // AGCgZhaobiaogginfo.class,
                    // zbggandbd.getGonggaoguid());
                    String msg = new AnGangDataUtil().zsGGorJJCgGGBG(cgTrustdeedinfo, modifInfo, service);
                    if (StringUtil.isNotBlank(msg)) {
                        WriteLogHelper.writeLog(logger, LogLevelEnum.错误, SYSTEMNAME,
                                AnGangApiEnum.资格预审_招标变更公告.toString(), "接受传输信息发生异常,异常原因为:" + msg);
                        new AGDB_AnGangJJInternaldockinginfo().insertInternaldockingInfoWithBackCount(cgTrustdeedinfo,
                                "jjzsggbgerr", "接受传输信息失败,失败原因为:" + msg);
                        return returnMsg(AnGangApiEnum.资格预审_招标变更公告, "", StatusEnum.接受失败.getValue(),
                                "接受传输信息失败,失败原因为:" + msg);
                    }
                    new AGDB_AnGangJJInternaldockinginfo().insertInternaldockingInfoWithBackCount(cgTrustdeedinfo,
                            "jjzsggbgok", "成功");
                    // }
                    // else {
                    // new
                    // AGDB_AnGangJJInternaldockinginfo().insertInternaldockingInfoWithBackCount(cgTrustdeedinfo,
                    // "jjzsggbgerr", "数据异常，没有找到公告内容，无法变更!");
                    // return returnMsg(AnGangApiEnum.资格预审_招标变更公告, "",
                    // StatusEnum.接受失败.getValue(), "数据异常，存在未完成的委托变更!");
                    // }
                }
            }
            catch (Exception e) {
                logger.error(e.getMessage(), e);
                WriteLogHelper.writeLog(logger, LogLevelEnum.错误, SYSTEMNAME, AnGangApiEnum.资格预审_招标变更公告.toString(),
                        "接受传输信息发生异常,异常原因为:" + e.getMessage());
                return returnMsg(AnGangApiEnum.资格预审_招标变更公告, "", StatusEnum.接受失败.getValue(),
                        "接受传输信息失败,失败原因为:" + e.getMessage());
            }
            finally {
                if (null != service) {
                    service.close();
                }
            }
            return returnMsg(AnGangApiEnum.资格预审_招标变更公告, "", StatusEnum.接受成功.getValue(), StatusEnum.接受成功.toString());
        }
    }

    /**
     * 接受招标委托书订正信息
     * E1CG02
     */
    @RequestMapping(value = "/modifyInfo", method = RequestMethod.POST)
    public String reciveModifyInfo(@RequestBody String receivejson) {
        ZtbCommonDao service = ZtbCommonDao.getInstance();
        if (StringUtil.isBlank(receivejson)) {
            WriteLogHelper.writeLog(logger, LogLevelEnum.警告, SYSTEMNAME, AnGangApiEnum.招标委托方案订正信息.toString(),
                    "接受到的传输信息为空");
            return returnMsg(AnGangApiEnum.招标委托方案订正信息, "", StatusEnum.接受失败.getValue(), "传输信息为空!");
        }
        else {
            try {
                WriteLogHelper.writeLog(logger, LogLevelEnum.记录, SYSTEMNAME, AnGangApiEnum.招标委托方案订正信息.toString(),
                        "本钢reciveModifyInfo()接受到的传输信息为:" + receivejson);
                JSONObject json = JSON.parseObject(receivejson);
                if (json.getJSONObject("msgBody") == null) {
                    return returnMsg(AnGangApiEnum.招标委托方案订正信息, "", StatusEnum.接受失败.getValue(), "传输信息为空!");
                }
                JSONObject modifInfo = json.getJSONObject("msgBody").getJSONObject("data");
                ZhglDataexchangeERP dataexchangeerp = new ZhglDataexchangeERP();
                if (StringUtil.isBlank(modifInfo.getString("bidSectionCode"))
                        || StringUtil.isBlank(modifInfo.getString("nodeType"))) {
                    return returnMsg(AnGangApiEnum.招标委托方案订正信息, "", StatusEnum.接受失败.getValue(),
                            "字段bidSectionCode或nodeType传输信息为空!");
                }
                else {
                    dataexchangeerp.setRowguid(UUID.randomUUID().toString());
                    dataexchangeerp.setDatano(AnGangApiEnum.招标委托方案订正信息.getValue());// 委托单
                    dataexchangeerp.setDatacontent(receivejson);
                    dataexchangeerp.setFromplat(SYSTEMNAME);
                    dataexchangeerp.setInserttime(new Date());
                    dataexchangeerp.setDatatype("json");
                    dataexchangeerp.setOperatedate(new Date());
                    dataexchangeerp
                            .setFullclassname("com.epoint.ztbtoerp.angangrestservice.AnGangService@reciveModifyInfo");
                }
                CgTrustdeedinfo cgTrustdeedinfo = getCgTrustDeedInfoByBiaoBao(
                        modifInfo.getString("bidSectionCode").trim(), modifInfo.getString("sysName").trim(), "3", "0",
                        service);
                if (cgTrustdeedinfo == null || cgTrustdeedinfo.isEmpty()) {
                    dataexchangeerp.setDatastatus("1");// 新增
                    dataexchangeerp.setInfoguid(modifInfo.getString("bidSectionCode").trim());
                    dataexchangeerp.setDatakey(modifInfo.getString("bidSectionCode").trim());
                    service.insert(dataexchangeerp);
                    return returnMsg(AnGangApiEnum.招标委托方案订正信息, "", StatusEnum.接受失败.getValue(),
                            "bidSectionCode数据异常，未找到对应委托!");
                }
                else {
                    dataexchangeerp.setDatastatus("1");// 新增
                    dataexchangeerp.setInfoguid(cgTrustdeedinfo.getERPTrustDeedNo());
                    dataexchangeerp.setDatakey(cgTrustdeedinfo.getRowguid());
                    if ("1".equals(modifInfo.getString("nodeType"))) {// 委托订正
                        String sql_change = "select * from Cg_Trustdeedchangeinfo where trustdeedguid=? and ifnull(auditstatus,'') != '3' ";
                        CgTrustdeedchangeinfo changeInfo = service.find(sql_change, CgTrustdeedchangeinfo.class,
                                cgTrustdeedinfo.getTrustguid());
                        if (changeInfo == null || changeInfo.isEmpty()) {
                            changeInfo = new CgTrustdeedchangeinfo();
                            String msg = null;
                            msg = new AnGangDataUtil().gengZhengCgTrustdeed(changeInfo, cgTrustdeedinfo, modifInfo,
                                    service);

                            if (StringUtil.isNotBlank(msg)) {
                                dataexchangeerp.setDatastatus(StatusEnum.接受失败.getValue());
                                service.insert(dataexchangeerp);
                                WriteLogHelper.writeLog(logger, LogLevelEnum.错误, SYSTEMNAME,
                                        AnGangApiEnum.招标委托方案订正信息.toString(), "接受传输信息发生异常,异常原因为:" + msg);
                                return returnMsg(AnGangApiEnum.招标委托方案订正信息, "", StatusEnum.接受失败.getValue(),
                                        "接受传输信息失败,失败原因为:" + msg);
                            }
                            dataexchangeerp.setDatastatus(StatusEnum.接受成功.getValue());
                            service.insert(dataexchangeerp);
                            service.insert(changeInfo);
                            ERPCommon.autoCommitWtdbg(changeInfo.getRowguid(), service);

                            //
                        }
                        else {
                            dataexchangeerp.setDatastatus(StatusEnum.接受失败.getValue());
                            service.insert(dataexchangeerp);
                            return returnMsg(AnGangApiEnum.招标委托方案订正信息, "", StatusEnum.接受失败.getValue(),
                                    "数据异常，存在未完成的委托变更!");
                        }
                    }
                    else {// 招标文件审核状态
                        AGCgZhaobiaogginfo cgZhaobiaogginfo = service.find("select * from CG_ZhaoBiaoGGInfo where "
                                + "auditstatus ='2' and ZiShenType ='2' and gonggaoguid in (select gonggaoguid from cg_zbggandbd where biaoduanguid=?)",
                                AGCgZhaobiaogginfo.class, cgTrustdeedinfo.getBiaoDuanGuid());
                        if (cgZhaobiaogginfo == null || cgZhaobiaogginfo.isEmpty()) {
                            dataexchangeerp.setDatastatus(StatusEnum.接受失败.getValue());
                            service.insert(dataexchangeerp);
                            return returnMsg(AnGangApiEnum.招标委托方案订正信息, "", StatusEnum.接受失败.getValue(),
                                    "招标文件审核异常，未找到CG_ZhaoBiaoGGInfo信息!");
                        }
                        if ("2".equals(modifInfo.getString("nodeType"))) {// 同意
                            ERPCommon.autoCommitZhaoBiaoGG(cgTrustdeedinfo, cgZhaobiaogginfo,
                                    modifInfo.getString("noticeContent"), service);
                            cgZhaobiaogginfo.set("iserpwtrshwj", "2");
                        }
                        else if ("3".equals(modifInfo.getString("nodeType"))) {// 不同意
                            ERPCommon.autoBackZhaoBiaoGG(cgTrustdeedinfo, cgZhaobiaogginfo,
                                    modifInfo.getString("noticeContent"), service);
                            cgZhaobiaogginfo.set("iserpwtrshwj", "");// 置空才能再次推送
                        }
                        service.update(cgZhaobiaogginfo);

                        dataexchangeerp.setDatastatus(StatusEnum.接受成功.getValue());
                        service.insert(dataexchangeerp);
                        return returnMsg(AnGangApiEnum.招标委托方案订正信息, "", StatusEnum.接受成功.getValue(), "招标文件审核接收成功");
                    }

                }
            }
            catch (Exception e) {
                logger.error(e.getMessage(), e);
                WriteLogHelper.writeLog(logger, LogLevelEnum.错误, SYSTEMNAME, AnGangApiEnum.招标委托方案订正信息.toString(),
                        "接受传输信息发生异常,异常原因为:" + e.getMessage());
                return returnMsg(AnGangApiEnum.招标委托方案订正信息, "", StatusEnum.接受失败.getValue(),
                        "接受传输信息失败,失败原因为:" + e.getMessage());
            }
            finally {
                if (null != service) {
                    service.close();
                }
            }
            return returnMsg(AnGangApiEnum.招标委托方案订正信息, "", StatusEnum.接受失败.getValue(), StatusEnum.接受成功.toString());
        }
    }

    /**
     * 接受标底价信息
     * E1CG03
     */
    @RequestMapping(value = "/modifyPrice", method = RequestMethod.POST)
    public String reciveModifyPrice(@RequestBody String receivejson) {
        ZtbCommonDao service = ZtbCommonDao.getInstance();
        if (StringUtil.isBlank(receivejson)) {
            WriteLogHelper.writeLog(logger, LogLevelEnum.警告, SYSTEMNAME, AnGangApiEnum.标底价信息.toString(), "接受到的传输信息为空");
            return returnMsg(AnGangApiEnum.标底价信息, "", StatusEnum.接受失败.getValue(), "传输信息为空!");
        }
        else {
            try {
                WriteLogHelper.writeLog(logger, LogLevelEnum.记录, SYSTEMNAME, AnGangApiEnum.标底价信息.toString(),
                        "本钢reciveModifyPrice()接受到的传输信息为:" + receivejson);
                JSONObject json = JSON.parseObject(receivejson);
                if (json.getJSONObject("msgBody") == null) {
                    return returnMsg(AnGangApiEnum.标底价信息, "", StatusEnum.接受失败.getValue(), "传输信息为空!");
                }
                JSONObject modifInfo = json.getJSONObject("msgBody").getJSONObject("data");
                ZhglDataexchangeERP dataexchangeerp = new ZhglDataexchangeERP();
                if (StringUtil.isBlank(modifInfo.getString("bidSectionCode"))) {
                    return returnMsg(AnGangApiEnum.标底价信息, "", StatusEnum.接受失败.getValue(), "字段bidSectionCode传输信息为空!");
                }
                else {
                    dataexchangeerp.setRowguid(UUID.randomUUID().toString());
                    dataexchangeerp.setDatano(AnGangApiEnum.标底价信息.getValue());// 标底价记录
                    dataexchangeerp.setDatacontent(receivejson);
                    dataexchangeerp.setFromplat(SYSTEMNAME);
                    dataexchangeerp.setInserttime(new Date());
                    dataexchangeerp.setDatatype("json");
                    dataexchangeerp.setOperatedate(new Date());
                    dataexchangeerp
                            .setFullclassname("com.epoint.ztbtoerp.angangrestservice.AnGangService@reciveModifyPrice");
                }
                CgTrustdeedinfo decodetrustdeedinfo = service.find("select * from CG_TrustDeedInfo where TrustDeedNo=?",CgTrustdeedinfo.class,modifInfo.getString("bidSectionCode").trim());
                TrustDeedInfoUtil.decode(decodetrustdeedinfo,service);
                String plussql = "select RowGuid,encryptfield from cg_trustdeedinfoplus where TrustGuid = ?";
                CgTrustdeedinfoplus plus = service.find(plussql, CgTrustdeedinfoplus.class, decodetrustdeedinfo.getRowguid());
                CgTrustdeedinfo cgTrustdeedinfo = getCgTrustDeedInfoByBiaoBao(
                        modifInfo.getString("bidSectionCode").trim(), modifInfo.getString("sysName").trim(), "3", "0",
                        service);
                if (cgTrustdeedinfo == null || cgTrustdeedinfo.isEmpty()) {
                    dataexchangeerp.setDatastatus("1");// 新增
                    dataexchangeerp.setInfoguid(modifInfo.getString("bidSectionCode").trim());
                    dataexchangeerp.setDatakey(modifInfo.getString("bidSectionCode").trim());
                    service.insert(dataexchangeerp);
                    TrustDeedInfoUtil.encryptField(decodetrustdeedinfo, plus);
                    return returnMsg(AnGangApiEnum.标底价信息, "", StatusEnum.接受失败.getValue(),
                            "bidSectionCode未找到符合条件的委托单!暂无法接收标底价！");
                }
                else {
                    dataexchangeerp.setDatastatus("1");// 新增
                    dataexchangeerp.setInfoguid(cgTrustdeedinfo.getERPTrustDeedNo());
                    dataexchangeerp.setDatakey(cgTrustdeedinfo.getRowguid());
                    service.insert(dataexchangeerp);
                    String msg = new AnGangDataUtil().biaodijiaInfo(cgTrustdeedinfo, modifInfo, service);
                    if (StringUtil.isNotBlank(msg)) {
                        WriteLogHelper.writeLog(logger, LogLevelEnum.错误, SYSTEMNAME, AnGangApiEnum.标底价信息.toString(),
                                "接受传输信息发生异常,异常原因为:" + msg);
                        return returnMsg(AnGangApiEnum.标底价信息, "", StatusEnum.接受失败.getValue(), "接受传输信息失败,失败原因为:" + msg);
                    }
                    cgTrustdeedinfo.setIsLuRuBiaoDi("1");// 是否已录入标底价预测价
                    service.update(cgTrustdeedinfo);
                }
                logger.info("trustguid=" + cgTrustdeedinfo.getTrustguid());
            }
            catch (Exception e) {
                logger.error(e.getMessage(), e);
                WriteLogHelper.writeLog(logger, LogLevelEnum.错误, SYSTEMNAME, AnGangApiEnum.标底价信息.toString(),
                        "接受传输信息发生异常,异常原因为:" + e.getMessage());
                return returnMsg(AnGangApiEnum.标底价信息, "", StatusEnum.接受失败.getValue(),
                        "接受传输信息失败,失败原因为:" + e.getMessage());
            }
            finally {
                if (null != service) {
                    service.close();
                }
            }
            return returnMsg(AnGangApiEnum.标底价信息, "", StatusEnum.接受成功.getValue(), StatusEnum.接受成功.toString());
        }
    }

    /**
     * 接受技术交流结果信息
     * E1CG04
     */
    @RequestMapping(value = "/commitOfflineResult", method = RequestMethod.POST)
    public String reciveOfflineResult(@RequestBody String receivejson) {
        ZtbCommonDao service = ZtbCommonDao.getInstance();
        if (StringUtil.isBlank(receivejson)) {
            WriteLogHelper.writeLog(logger, LogLevelEnum.警告, SYSTEMNAME, AnGangApiEnum.技术交流结果信息.toString(),
                    "接受到的传输信息为空");
            return returnMsg(AnGangApiEnum.技术交流结果信息, "", StatusEnum.接受失败.getValue(), "传输信息为空!");
        }
        else {
            try {
                WriteLogHelper.writeLog(logger, LogLevelEnum.记录, SYSTEMNAME, AnGangApiEnum.技术交流结果信息.toString(),
                        "本钢reciveOfflineResult()接受到的传输信息为:" + receivejson);
                JSONObject json = JSON.parseObject(receivejson);
                if (json.getJSONObject("msgBody") == null) {
                    return returnMsg(AnGangApiEnum.技术交流结果信息, "", StatusEnum.接受失败.getValue(), "传输信息为空!");
                }
                JSONObject offlineResult = json.getJSONObject("msgBody").getJSONArray("data").getJSONObject(0);
                ZhglDataexchangeERP dataexchangeerp = new ZhglDataexchangeERP();
                if (StringUtil.isBlank(offlineResult.getString("bidSectionCode").trim())) {
                    return returnMsg(AnGangApiEnum.技术交流结果信息, "", StatusEnum.接受失败.getValue(), "字段bidSectionCode传输信息为空!");
                }
                else {
                    dataexchangeerp.setRowguid(UUID.randomUUID().toString());
                    dataexchangeerp.setDatano(AnGangApiEnum.技术交流结果信息.getValue());// 技术交流结果信息
                    dataexchangeerp.setDatacontent(receivejson);
                    dataexchangeerp.setFromplat(SYSTEMNAME);
                    dataexchangeerp.setInserttime(new Date());
                    dataexchangeerp.setDatatype("json");
                    dataexchangeerp.setOperatedate(new Date());
                    dataexchangeerp.setFullclassname(
                            "com.epoint.ztbtoerp.angangrestservice.AnGangService@reciveOfflineResult");
                }
                CgTrustdeedinfo cgTrustdeedinfo = getCgTrustDeedInfoByBiaoBao(
                        offlineResult.getString("bidSectionCode").trim(), "", "3", "0", service);
                if (cgTrustdeedinfo == null || cgTrustdeedinfo.isEmpty()) {
                    dataexchangeerp.setDatastatus("1");// 新增
                    dataexchangeerp.setInfoguid(offlineResult.getString("bidSectionCode").trim());
                    dataexchangeerp.setDatakey(offlineResult.getString("bidSectionCode").trim());
                    service.insert(dataexchangeerp);
                    return returnMsg(AnGangApiEnum.技术交流结果信息, "", StatusEnum.接受失败.getValue(), "未找到符合条件的委托单!");
                }
                else {
                    dataexchangeerp.setDatakey(cgTrustdeedinfo.getTrustguid());
                    dataexchangeerp.setInfoguid(cgTrustdeedinfo.getERPTrustDeedNo());
                    String zSGGSql = "select * from CG_ZiGeYUShen where biaoduanguid =?";
                    CgZigeyushen cgzigeyushen = service.find(zSGGSql, CgZigeyushen.class,
                            cgTrustdeedinfo.getBiaoDuanGuid());
                    String msg = new AnGangDataUtil().jiShuJiaoLiuInfo(cgzigeyushen, json);
                    if (StringUtil.isNotBlank(msg)) {
                        dataexchangeerp.setDatastatus(StatusEnum.接受失败.getValue());
                        service.insert(dataexchangeerp);
                        WriteLogHelper.writeLog(logger, LogLevelEnum.错误, SYSTEMNAME, AnGangApiEnum.技术交流结果信息.toString(),
                                "接受传输信息发生异常,异常原因为:" + msg);
                        return returnMsg(AnGangApiEnum.技术交流结果信息, "", StatusEnum.接受失败.getValue(),
                                "接受传输信息失败,失败原因为:" + msg);
                    }
                    dataexchangeerp.setDatastatus(StatusEnum.接受成功.getValue());
                    service.insert(dataexchangeerp);
                    if (ERPCommon.isErpNewZiShenJieGuo(cgTrustdeedinfo.getFromplat(), cgTrustdeedinfo.getFromPlatNo(),
                            "")) {
                        ERPCommon.autoCommitZiGeYuShenJieGuo(cgTrustdeedinfo, cgzigeyushen, "erp技术交流", service);
                        cgzigeyushen.setAuditstatus("3");
                        service.update(cgzigeyushen);
                    }
                }
                logger.info("trustguid=" + cgTrustdeedinfo.getTrustguid());
            }
            catch (Exception e) {
                logger.error(e.getMessage(), e);
                WriteLogHelper.writeLog(logger, LogLevelEnum.错误, SYSTEMNAME, AnGangApiEnum.技术交流结果信息.toString(),
                        "接受传输信息发生异常,异常原因为:" + e.getMessage());
                return returnMsg(AnGangApiEnum.技术交流结果信息, "", StatusEnum.接受失败.getValue(),
                        "接受传输信息失败,失败原因为:" + e.getMessage());
            }
            finally {
                if (null != service) {
                    service.close();
                }
            }
            return returnMsg(AnGangApiEnum.技术交流结果信息, "", StatusEnum.接受成功.getValue(), StatusEnum.接受成功.toString());
        }
    }

    /**
     * 定标意见及结构化信息
     * E1CG06
     */
    @RequestMapping(value = "/commitDecision", method = RequestMethod.POST)
    public String reciveDecision(@RequestBody String receivejson) {
        ZtbCommonDao service = ZtbCommonDao.getInstance();
        if (StringUtil.isBlank(receivejson)) {
            WriteLogHelper.writeLog(logger, LogLevelEnum.警告, SYSTEMNAME, AnGangApiEnum.定标意见及结构化信息.toString(),
                    "接受到的传输信息为空");
            return returnMsg(AnGangApiEnum.定标意见及结构化信息, "", StatusEnum.接受失败.getValue(), "传输信息为空!");
        }
        else {
            ILock lock = null;
            try {
                WriteLogHelper.writeLog(logger, LogLevelEnum.记录, SYSTEMNAME, AnGangApiEnum.定标意见及结构化信息.toString(),
                        "本钢reciveDecision()接受到的传输信息为:" + receivejson);
                JSONObject json = JSON.parseObject(receivejson);
                if (json.getJSONObject("msgBody") == null) {
                    return returnMsg(AnGangApiEnum.定标意见及结构化信息, "", StatusEnum.接受失败.getValue(), "传输信息为空!");
                }
                JSONObject offlineResult = json.getJSONObject("msgBody");
                ZhglDataexchangeERP dataexchangeerp = new ZhglDataexchangeERP();
                if (StringUtil.isBlank(offlineResult.getString("bidSectionCode"))) {
                    return returnMsg(AnGangApiEnum.定标意见及结构化信息, "", StatusEnum.接受失败.getValue(),
                            "字段bidSectionCode传输信息为空!");
                }
                else {
                    dataexchangeerp.setRowguid(UUID.randomUUID().toString());
                    dataexchangeerp.setDatano(AnGangApiEnum.定标意见及结构化信息.getValue());// 定标结果
                    dataexchangeerp.setDatacontent(receivejson);
                    dataexchangeerp.setFromplat(SYSTEMNAME);
                    dataexchangeerp.setInserttime(new Date());
                    dataexchangeerp.setDatatype("json");
                    dataexchangeerp.setOperatedate(new Date());
                    dataexchangeerp
                            .setFullclassname("com.epoint.ztbtoerp.angangrestservice.AnGangService@reciveDecision");
                }
                CgTrustdeedinfo cgTrustdeedinfo = getCgTrustDeedInfoByBiaoBao(
                        offlineResult.getString("bidSectionCode").trim(), "", "3", "0", service);
                if (cgTrustdeedinfo == null || cgTrustdeedinfo.isEmpty()) {
                    dataexchangeerp.setDatakey(offlineResult.getString("bidSectionCode").trim());
                    dataexchangeerp.setInfoguid(offlineResult.getString("bidSectionCode").trim());
                    dataexchangeerp.setDatastatus("1");// 修改
                    service.insert(dataexchangeerp);
                    return returnMsg(AnGangApiEnum.定标意见及结构化信息, "", StatusEnum.接受失败.getValue(),
                            "bidSectionCode未找到符合条件的委托单!");
                }
                else {
                    dataexchangeerp.setDatakey(cgTrustdeedinfo.getTrustguid());
                    dataexchangeerp.setInfoguid(cgTrustdeedinfo.getERPTrustDeedNo());
                    dataexchangeerp.setDatastatus("1");// 修改
                    service.insert(dataexchangeerp);
                    CgShoubiaotuijian cgShoubiaotuijian = service.find(
                            "select * from CG_ShouBiaoTuiJian where biaoduanguid=? and auditstatus != '3' ",
                            CgShoubiaotuijian.class, cgTrustdeedinfo.getBiaoDuanGuid());
                    if (cgShoubiaotuijian == null || cgShoubiaotuijian.isEmpty()) {
                        return returnMsg(AnGangApiEnum.定标意见及结构化信息, "", StatusEnum.接受失败.getValue(), "未找到符合条件的评标结果信息!");
                    }
                    String msg = null;
                    lock = LockHelper.getLock("AnGang_" + cgTrustdeedinfo.getBiaoDuanGuid(), null);
                    if (lock.lock()) {
                        msg = new AnGangDataUtil().DingBiaoJieGuoInfo(cgTrustdeedinfo, cgShoubiaotuijian, offlineResult,
                                service);
                        lock.unlock();
                    }
                    else {
                        msg = "接收定标结果失败，获取锁失败！";
                    }
                    if (StringUtil.isNotBlank(msg)) {
                        WriteLogHelper.writeLog(logger, LogLevelEnum.错误, SYSTEMNAME,
                                AnGangApiEnum.定标意见及结构化信息.toString(), "接受传输信息发生异常,异常原因为:" + msg);
                        return returnMsg(AnGangApiEnum.定标意见及结构化信息, "", StatusEnum.接受失败.getValue(), msg);
                    }
                    service.update(cgTrustdeedinfo);
                    // 自动下一步审核流程--自动推送到项目经理定标的ERP
                    IConfigService configService = ContainerFactory.getContainInfo().getComponent(IConfigService.class);
                    String kaiQiDingBiaoValue = configService.getFrameConfigValue("开启新工作流定标流程V5");
                    if (StringUtil.isNotBlank(kaiQiDingBiaoValue) && kaiQiDingBiaoValue.equals("1")) {
                        String frameConfigValue = configService.getFrameConfigValue("接收定标意见的ERP");
                        if (StringUtil.isNotBlank(frameConfigValue) && frameConfigValue.contains(SYSTEMNAME)) {
                            ERPCommon.autoCommitDingBiao(cgTrustdeedinfo.getRowguid(), cgShoubiaotuijian.getRowguid(),
                                    service);
                        }
                    }
                }
                logger.info("trustguid=" + cgTrustdeedinfo.getTrustguid());
            }
            catch (Exception e) {
                logger.error(e.getMessage(), e);
                WriteLogHelper.writeLog(logger, LogLevelEnum.错误, SYSTEMNAME, AnGangApiEnum.定标意见及结构化信息.toString(),
                        "接受传输信息发生异常,异常原因为:" + e.getMessage());
                return returnMsg(AnGangApiEnum.定标意见及结构化信息, "", StatusEnum.接受失败.getValue(),
                        "接受传输信息失败,失败原因为:" + e.getMessage());
            }
            finally {
                if (null != service) {
                    service.close();
                }
                if (null != lock) {
                    lock.unlock();
                }
            }
            return returnMsg(AnGangApiEnum.定标意见及结构化信息, "", StatusEnum.接受成功.getValue(), StatusEnum.接受成功.toString());
        }
    }

    /**
     * 单一来源补录（直接拟签）信息
     * E1CG07
     */
    @RequestMapping(value = "/commitSingle", method = RequestMethod.POST)
    public String reciveSingle(@RequestBody String receivejson) {
        ZtbCommonDao service = ZtbCommonDao.getInstance();
        if (StringUtil.isBlank(receivejson)) {
            WriteLogHelper.writeLog(logger, LogLevelEnum.警告, SYSTEMNAME, AnGangApiEnum.单一来源补录信息.toString(),
                    "接受到的传输信息为空");
            return returnSingleMsg("", StatusEnum.接受失败.getValue(), "传输信息为空!", "");
        }
        else {
            try {
                WriteLogHelper.writeLog(logger, LogLevelEnum.记录, SYSTEMNAME, AnGangApiEnum.单一来源补录信息.toString(),
                        "本钢reciveSingle()接受到的传输信息为:" + receivejson);
                JSONObject json = JSON.parseObject(receivejson);
                if (json.getJSONObject("msgBody") == null) {
                    return returnSingleMsg(AnGangApiEnum.单一来源补录信息.getValue(), StatusEnum.接受失败.getValue(), "传输信息为空!",
                            "");
                }
                JSONObject offlineResult = json.getJSONObject("msgBody");
                ZhglDataexchangeERP dataexchangeerp = new ZhglDataexchangeERP();
                if (StringUtil.isBlank(offlineResult.getString("projectCode"))) {
                    return returnSingleMsg(AnGangApiEnum.单一来源补录信息.getValue(), StatusEnum.接受失败.getValue(),
                            "字段projectCode传输信息为空!", "");
                }
                else {
                    dataexchangeerp.setRowguid(UUID.randomUUID().toString());
                    dataexchangeerp.setDatano(AnGangApiEnum.单一来源补录信息.getValue());// 单一来源补录信息
                    dataexchangeerp.setDatacontent(receivejson);
                    dataexchangeerp.setFromplat(SYSTEMNAME);
                    dataexchangeerp.setInfoguid(offlineResult.getString("projectCode"));
                    dataexchangeerp.setInserttime(new Date());
                    dataexchangeerp.setDatatype("json");
                    dataexchangeerp.setOperatedate(new Date());
                    dataexchangeerp
                            .setFullclassname("com.epoint.ztbtoerp.angangrestservice.AnGangService@reciveSingle");
                }
                dataexchangeerp.setDatakey(offlineResult.getString("projectCode"));
                dataexchangeerp.setInfoguid(offlineResult.getString("projectCode"));
                dataexchangeerp.setDatastatus("1");// 修改
                service.insert(dataexchangeerp);

                service.close();
                service = ZtbCommonDao.getInstance();

                // 判断新增/修改/删除
                String bidPackageStatus = new CommonUtil(service).formatString(offlineResult.getString("bidPackageStatus"));
                String fromplatno = new CommonUtil(service).formatString(offlineResult.getString("sysName"));
                String projectno = new CommonUtil(service).formatString(offlineResult.getString("projectCode"));
                String purchasesolutionno = new CommonUtil(service).formatString(offlineResult.getString("purchaseSolutionNo"));
                String bulucz = "";
                SqlBuilder sqlBuilder = new SqlBuilder();
                if(StringUtil.isNotBlank(fromplatno)){
                    sqlBuilder.append("and fromplatno = ? ", fromplatno);
                }
                if(StringUtil.isNotBlank(projectno)){
                    sqlBuilder.append("and projectno = ? ", projectno);
                }
                if(StringUtil.isNotBlank(purchasesolutionno)){
                    sqlBuilder.append("and purchasesolutionno = ? ", purchasesolutionno);
                }
                String sql = "select * from Cg_DanYiLaiYuanLuruInfo where 1=1 " + sqlBuilder.getSql() + " order by OperateDate desc limit 1";
                AGCgDanYiLaiYuanLuruInfo cgDanYiLaiYuanLuruInfo = service.find(sql, AGCgDanYiLaiYuanLuruInfo.class, sqlBuilder.getParams());

                service.close();
                service = ZtbCommonDao.getInstance();

                if(cgDanYiLaiYuanLuruInfo == null){
                    if(StringUtil.isBlank(bidPackageStatus) || "1".equals(bidPackageStatus)){
                        bulucz = "1";
                    }
                }
                else{
                    if("2".equals(bidPackageStatus)){
                        bulucz = "2";
                    }
                }

                String msg = null;

                Record retInfo = new CommonUtil(service).operationSingle(offlineResult, cgDanYiLaiYuanLuruInfo, service, bulucz);

                msg = retInfo.get("msg");
                cgDanYiLaiYuanLuruInfo = retInfo.get("cgDanYiLaiYuanLuruInfo");
                Record record = new Record();
                if (cgDanYiLaiYuanLuruInfo != null && !cgDanYiLaiYuanLuruInfo.isEmpty()) {
                    record = new Record();
                    record.set("rowguid", cgDanYiLaiYuanLuruInfo.getRowguid());
                    record.set("status", "0");
                    //record.set("backSugg", "已存在该单一来源补录信息!");
                    record.set("backSugg", msg);
                    record.set("fromplatno", cgDanYiLaiYuanLuruInfo.getFromPlatno());
                    new AGDB_AnGangInternaldockinginfo()
                            .insertInternaldockingInfoWithMatchRemark("isdanyilaiyuanhuitui", record, 0, 0);
                    return returnSingleMsg(offlineResult.getString("projectCode"), StatusEnum.接受失败.getValue(),
                            "已存在该单一来源补录信息!", offlineResult.getString("systemId"));
                }
                else {
                    cgDanYiLaiYuanLuruInfo = new AGCgDanYiLaiYuanLuruInfo();
                    msg = new AnGangDataUtil().insertSingle(offlineResult, cgDanYiLaiYuanLuruInfo, service);

                    if (StringUtil.isNotBlank(msg)) {
                        // 推送接收失败消息
                        record = new Record();
                        record.set("rowguid", cgDanYiLaiYuanLuruInfo.getRowguid());
                        record.set("status", "0");
                        record.set("backSugg", msg);
                        record.set("fromplatno", cgDanYiLaiYuanLuruInfo.getFromPlatno());
                        new AGDB_AnGangInternaldockinginfo()
                                .insertInternaldockingInfoWithMatchRemark("isdanyilaiyuanhuitui", record, 0, 0);
                        WriteLogHelper.writeLog(logger, LogLevelEnum.错误, SYSTEMNAME, AnGangApiEnum.单一来源补录信息.toString(),
                                "接受传输信息发生异常,异常原因为:" + msg);
                        return returnSingleMsg(offlineResult.getString("projectCode"), StatusEnum.接受失败.getValue(), msg,
                                offlineResult.getString("systemId"));
                    }
                    else {
                        // 推送接收成功消息
                        record = new Record();
                        record.set("rowguid", cgDanYiLaiYuanLuruInfo.getRowguid());
                        record.set("status", "1");
                        record.set("backSugg", "");
                        record.set("fromplatno", cgDanYiLaiYuanLuruInfo.getFromPlatno());
                        new AGDB_AnGangInternaldockinginfo()
                                .insertInternaldockingInfoWithMatchRemark("isdanyilaiyuanhuitui", record, 0, 0);
                        return returnSingleMsg(offlineResult.getString("projectCode"), StatusEnum.接受成功.getValue(),
                                StatusEnum.接受成功.toString(), offlineResult.getString("systemId"));
                    }
                }
            }
            catch (Exception e) {
                logger.error(e.getMessage(), e);
                WriteLogHelper.writeLog(logger, LogLevelEnum.错误, SYSTEMNAME, AnGangApiEnum.单一来源补录信息.toString(),
                        "接受传输信息发生异常,异常原因为:" + e.getMessage());
                return returnSingleMsg("", StatusEnum.接受失败.getValue(), "接受传输信息失败,失败原因为:" + e.getMessage(), "");
            }
            finally {
                if (null != service) {
                    service.close();
                }
            }
        }
    }

    /**
     * 查询邀买投供应商个数
     * E1CG001
     */
    @RequestMapping(value = "/reciveGysNum", method = RequestMethod.POST)
    public String reciveGysNum(@RequestBody String receivejson) {
        ZtbCommonDao service = ZtbCommonDao.getInstance();
        if (StringUtil.isBlank(receivejson)) {
            WriteLogHelper.writeLog(logger, LogLevelEnum.警告, SYSTEMNAME, "", "接受到的传输信息为空");
            return returnGysnumMsg("", 0, 0);
        }
        else {
            try {
                WriteLogHelper.writeLog(logger, LogLevelEnum.记录, SYSTEMNAME, "实时查询邀买投供应商个数的服务",
                        "本钢reciveDecision()接受到的传输信息为:" + receivejson);
                JSONObject json1 = JSON.parseObject(receivejson);
                String string = json1.getString("messageBody");
                if (StringUtil.isBlank(string)) {
                    return returnGysnumMsg("messageBody 不存在", 0, 0);
                }
                JSONObject json = JSONObject.parseObject(string);
                if (json.getJSONObject("msgBody") == null) {
                    return returnGysnumMsg("msgBody 为空", 0, 0);
                }
                JSONObject offlineResult = json.getJSONObject("msgBody").getJSONObject("data");
                ZhglDataexchangeERP dataexchangeerp = new ZhglDataexchangeERP();
                if (StringUtil.isBlank(offlineResult.getString("bidSectionCode"))
                        || StringUtil.isBlank(offlineResult.getString("sysName"))) {
                    return returnGysnumMsg("bidSectionCode或sysName为空", 0, 0);
                }
                else {
                    dataexchangeerp.setRowguid(UUID.randomUUID().toString());
                    dataexchangeerp.setDatano(AnGangApiEnum.实时查询邀买投供应商个数.getValue());
                    dataexchangeerp.setDatacontent(receivejson);
                    dataexchangeerp.setFromplat(SYSTEMNAME);
                    dataexchangeerp.setInserttime(new Date());
                    dataexchangeerp.setDatatype("json");
                    dataexchangeerp.setOperatedate(new Date());
                    dataexchangeerp
                            .setFullclassname("com.epoint.ztbtoerp.angangrestservice.AnGangService@reciveGysNum");
                }
                CgTrustdeedinfo cgTrustdeedinfo = getCgTrustDeedInfoByBiaoBao(
                        offlineResult.getString("bidSectionCode").trim(), offlineResult.getString("sysName"), "3", null,
                        service);
                Log.info("cgTru=" + cgTrustdeedinfo);
                if (cgTrustdeedinfo == null || cgTrustdeedinfo.isEmpty()) {
                    dataexchangeerp.setDatakey(offlineResult.getString("bidSectionCode").trim());
                    dataexchangeerp.setInfoguid(offlineResult.getString("bidSectionCode").trim());
                    dataexchangeerp.setDatastatus("1");// 修改
                    service.insert(dataexchangeerp);
                    return returnGysnumMsg(offlineResult.getString("sysName"), 0, 0);
                }
                else {
                    String str = cgTrustdeedinfo.getStr("isErpJingJia");
                    if (!"1".equals(str)) {
                        return returnGysnumMsg("接口未开放", 0, 0);
                    }
                    dataexchangeerp.setInfoguid(cgTrustdeedinfo.getERPTrustDeedNo());
                    dataexchangeerp.setDatakey(cgTrustdeedinfo.getTrustguid());
                    dataexchangeerp.setDatastatus("1");// 修改
                    service.insert(dataexchangeerp);
                    // isbid的定义：1、有报名，2、有报价，3、无供应商报名，4、无供应商报价，5、报名获取报错，6、报价获取报错
                    // 预审项目：预审阶段=报名；后审阶段=报价；123456
                    // 后审项目：=直接报价 246
                    AGCgBiaoduaninfo bdinfo = service.find(AGCgBiaoduaninfo.class, cgTrustdeedinfo.getBiaoDuanGuid());
                    if ("1".equals(cgTrustdeedinfo.getVerifymode())) {
                        if (bdinfo.getZishenkaibiaodate() == null || bdinfo.getZishenkaibiaodate().after(new Date())) {
                            return returnGysnumMsg(offlineResult.getString("sysName"),
                                    AGDB_CG_TouBiaoDanWei.getBMDWZSIsZhiFuCount(cgTrustdeedinfo.getBiaoDuanGuid()),
                                    AGDB_CG_TouBiaoDanWei.getTBFileCount("ZS_" + cgTrustdeedinfo.getBiaoDuanGuid()));
                        }
                        else {
                            if (bdinfo.getKaibiaodate() == null) {
                                return returnGysnumMsg(offlineResult.getString("sysName"),
                                        AGDB_CG_TouBiaoDanWei.getBMDWZSIsZhiFuCount(cgTrustdeedinfo.getBiaoDuanGuid()),
                                        AGDB_CG_TouBiaoDanWei
                                                .getTBFileCount("ZS_" + cgTrustdeedinfo.getBiaoDuanGuid()));
                            }
                            else {
                                return returnGysnumMsg(offlineResult.getString("sysName"),
                                        AGDB_CG_TouBiaoDanWei.getBMDWIsZhiFuCount(cgTrustdeedinfo.getBiaoDuanGuid()),
                                        AGDB_CG_TouBiaoDanWei.getTBFileCount(cgTrustdeedinfo.getBiaoDuanGuid()));
                            }
                        }
                    }
                    else {
                        return returnGysnumMsg(offlineResult.getString("sysName"),
                                AGDB_CG_TouBiaoDanWei.getBMDWIsZhiFuCount(cgTrustdeedinfo.getBiaoDuanGuid()),
                                AGDB_CG_TouBiaoDanWei.getTBFileCount(cgTrustdeedinfo.getBiaoDuanGuid()));
                    }
                }
            }
            catch (Exception e) {
                logger.error(e.getMessage(), e);
                WriteLogHelper.writeLog(logger, LogLevelEnum.错误, SYSTEMNAME, AnGangApiEnum.实时查询邀买投供应商个数.toString(),
                        "接受传输信息发生异常,异常原因为:" + e.getMessage());
                return returnGysnumMsg("接受传输信息发生异常,异常原因为:" + e.getMessage(), 0, 0);
            }
            finally {
                if (null != service) {
                    service.close();
                }
            }
        }
    }

    /**
     * 获取报名相关信息
     * E1CG08
     */
    @RequestMapping(value = "/getBaoMingInfo", method = RequestMethod.POST)
    public String getBaoMingInfo(@RequestBody String receivejson) {
        ZtbCommonDao service = ZtbCommonDao.getInstance();
        if (StringUtil.isBlank(receivejson)) {
            WriteLogHelper.writeLog(logger, LogLevelEnum.警告, SYSTEMNAME, AnGangApiEnum.获取全部供应商报名或者报价信息.toString(),
                    "接受到的传输信息为空");
            return returnMsg(AnGangApiEnum.获取全部供应商报名或者报价信息, "", StatusEnum.接受失败.getValue(), "传输信息为空!");
        }
        else {
            try {
                JSONObject json = JSON.parseObject(receivejson);
                if (json.getJSONObject("msgBody") == null) {
                    return returnMsg(AnGangApiEnum.获取全部供应商报名或者报价信息, "", StatusEnum.接受失败.getValue(),
                            "接受传输信息失败,失败原因为:msgBody为空！");
                }
                JSONObject offlineResult = json.getJSONObject("msgBody").getJSONObject("data");
                ZhglDataexchangeERP dataexchangeerp = new ZhglDataexchangeERP();
                if (StringUtil.isBlank(offlineResult.getString("bidSectionCode"))
                        || StringUtil.isBlank(offlineResult.getString("sysName"))) {
                    return returnMsg(AnGangApiEnum.获取全部供应商报名或者报价信息, "", StatusEnum.接受失败.getValue(),
                            "接受传输信息失败,失败原因为:bidSectionCode为空或sysName为空！");
                }
                else {
                    dataexchangeerp.setRowguid(UUID.randomUUID().toString());
                    dataexchangeerp.setDatano(AnGangApiEnum.获取全部供应商报名或者报价信息.getValue());
                    dataexchangeerp.setDatacontent(receivejson);
                    dataexchangeerp.setFromplat(SYSTEMNAME);
                    dataexchangeerp.setInserttime(new Date());
                    dataexchangeerp.setDatatype("json");
                    dataexchangeerp.setOperatedate(new Date());
                    dataexchangeerp
                            .setFullclassname("com.epoint.ztbtoerp.angangrestservice.AnGangService@getBaoMingInfo");
                    CgTrustdeedinfo trustdeedinfo = getCgTrustDeedInfoByBiaoBao(
                            offlineResult.getString("bidSectionCode").trim(), offlineResult.getString("sysName").trim(),
                            "3", "", service);
                    dataexchangeerp.setDatakey(trustdeedinfo != null ? trustdeedinfo.getTrustguid()
                            : offlineResult.getString("bidSectionCode").trim());
                    dataexchangeerp.setInfoguid(trustdeedinfo != null ? trustdeedinfo.getERPTrustDeedNo()
                            : offlineResult.getString("bidSectionCode").trim());
                    dataexchangeerp.setDatastatus("1");
                    service.insert(dataexchangeerp);
                    // 收到请求后，整理相关的数据，插入待推送表中，等待接口扫描服务扫描后将对应结果推送给erp
                    if (trustdeedinfo != null) {
                        new AGDB_AnGangJJInternaldockinginfo().insertInternaldockingInfoWithBackCount(trustdeedinfo,
                                "jjbminfo", "成功");
                    }
                    else {
                        return returnMsg(AnGangApiEnum.获取全部供应商报名或者报价信息, "", StatusEnum.接受失败.getValue(),
                                "接受传输信息失败,失败原因为:未找到委托单！");
                    }
                }
            }
            catch (Exception e) {
                logger.error(e.getMessage(), e);
                WriteLogHelper.writeLog(logger, LogLevelEnum.错误, SYSTEMNAME, AnGangApiEnum.获取全部供应商报名或者报价信息.toString(),
                        "接受传输信息发生异常,异常原因为:" + e.getMessage());
                return returnMsg(AnGangApiEnum.获取全部供应商报名或者报价信息, "", StatusEnum.接受失败.getValue(),
                        "接受传输信息失败,失败原因为:" + e.getMessage());
            }
            finally {
                if (null != service) {
                    service.close();
                }
            }
            return returnMsg(AnGangApiEnum.获取全部供应商报名或者报价信息, "", StatusEnum.接受成功.getValue(), StatusEnum.接受成功.toString());
        }
    }

    /**
     * 公开竞价资格预审结束后发标接口(二次发标)信息（E1CG25）
     * E1CG25
     */
    @RequestMapping(value = "/reciveJJZiShenResult", method = RequestMethod.POST)
    public String reciveJJZiShenResult(@RequestBody String receivejson) {
        ZtbCommonDao service = ZtbCommonDao.getInstance();
        if (StringUtil.isBlank(receivejson)) {
            WriteLogHelper.writeLog(logger, LogLevelEnum.警告, SYSTEMNAME, AnGangApiEnum.公开竞价资格预审结束后发标接口_二次发标.toString(),
                    "接受到的传输信息为空");
            return returnMsg(AnGangApiEnum.公开竞价资格预审结束后发标接口_二次发标, "", StatusEnum.接受失败.getValue(), "传输信息为空!");
        }
        else {
            String consignationCode = null;
            String bidSectionCode = null;
            String sysName = null;
            try {
                WriteLogHelper.writeLog(logger, LogLevelEnum.记录, SYSTEMNAME, "公开竞价资格预审结束后发标接口_二次发标",
                        "鞍钢reciveJJZiShenResult()接受到的传输信息为:" + receivejson);
                JSONObject json = JSON.parseObject(receivejson);
                if (json.getJSONObject("msgBody") == null) {
                    return returnJJInfoMsg("", "msgBody为空", 0, "", "");
                }
                JSONObject offlineResult = json.getJSONObject("msgBody").getJSONObject("data");
                ZhglDataexchangeERP dataexchangeerp = new ZhglDataexchangeERP();
                if (StringUtil.isBlank(offlineResult.getString("bidSectionCode"))
                        || StringUtil.isBlank(offlineResult.getString("sysName"))) {
                    return returnJJInfoMsg(offlineResult.getString("sysName"), "bidSectionCode或sysName为空委托单数据不存在", 0,
                            offlineResult.getString("bidSectionCode"), offlineResult.getString("consignationCode"));
                }
                else {
                    consignationCode = offlineResult.getString("consignationCode");
                    bidSectionCode = offlineResult.getString("bidSectionCode");
                    sysName = offlineResult.getString("sysName");
                    CgTrustdeedinfo cgTrustdeedinfo = getCgTrustDeedInfoByBiaoBao(
                            offlineResult.getString("bidSectionCode").trim(), offlineResult.getString("sysName"), "3",
                            "1", service);

                    if (cgTrustdeedinfo == null || cgTrustdeedinfo.isEmpty()
                            || "2".equals(cgTrustdeedinfo.getVerifymode())) {
                        dataexchangeerp.setInfoguid(offlineResult.getString("bidSectionCode").trim());
                        dataexchangeerp.setDatakey(offlineResult.getString("bidSectionCode").trim());
                        dataexchangeerp.setDatastatus("1");// 修改
                        service.insert(dataexchangeerp);
                        return returnJJInfoMsg(offlineResult.getString("sysName"), "委托单数据不存在,或者委托单未提交", 0,
                                offlineResult.getString("bidSectionCode"), offlineResult.getString("consignationCode"));
                    }
                    dataexchangeerp.setRowguid(UUID.randomUUID().toString());
                    dataexchangeerp.setDatano(AnGangApiEnum.公开竞价资格预审结束后发标接口_二次发标.getValue());
                    dataexchangeerp.setDatacontent(receivejson);
                    dataexchangeerp.setFromplat(SYSTEMNAME);
                    dataexchangeerp.setInserttime(new Date());
                    dataexchangeerp.setDatakey(cgTrustdeedinfo.getTrustguid());
                    dataexchangeerp.setDatatype("json");
                    dataexchangeerp.setOperatedate(new Date());
                    dataexchangeerp.setFullclassname(
                            "com.epoint.ztbtoerp.angangrestservice.AnGangService@reciveJJZiShenResult");
                    dataexchangeerp.setDatastatus("1");
                    service.insert(dataexchangeerp);

                    String sql_change = "select * from CG_zhaobiaogginfo where 1=1 and zishentype='2' "
                            + "and gonggaoguid in(select gonggaoguid from CG_ZBGGANDBD where biaoduanguid=? )";
                    AGCgZhaobiaogginfo zsGGInfo = service.find(sql_change, AGCgZhaobiaogginfo.class,
                            cgTrustdeedinfo.getBiaoDuanGuid());
                    if (zsGGInfo == null || zsGGInfo.isEmpty()) {
                        String msg = new AnGangDataUtil().zsGGTwoCgGG(cgTrustdeedinfo, offlineResult, service);
                        if (StringUtil.isNotBlank(msg)) {
                            WriteLogHelper.writeLog(logger, LogLevelEnum.错误, SYSTEMNAME,
                                    AnGangApiEnum.公开竞价资格预审结束后发标接口_二次发标.toString(), "接受传输信息发生异常,异常原因为:" + msg);
                            new AGDB_AnGangJJInternaldockinginfo().insertInternaldockingInfoWithBackCount(
                                    cgTrustdeedinfo, "jjzsendfberr", "接受传输信息发生异常,异常原因为:" + msg);
                            return returnMsg(AnGangApiEnum.公开竞价资格预审结束后发标接口_二次发标, "", StatusEnum.接受失败.getValue(),
                                    "接受传输信息失败,失败原因为:" + msg);
                        }
                        new AGDB_AnGangJJInternaldockinginfo().insertInternaldockingInfoWithBackCount(cgTrustdeedinfo,
                                "jjzsendfbok", "成功");
                    }
                    else {
                        String msg = new AnGangDataUtil().zsGGTwoCgGG(cgTrustdeedinfo, offlineResult, service);
                        if (StringUtil.isNotBlank(msg)) {
                            WriteLogHelper.writeLog(logger, LogLevelEnum.错误, SYSTEMNAME,
                                    AnGangApiEnum.公开竞价资格预审结束后发标接口_二次发标.toString(), "接受传输信息发生异常,异常原因为:" + msg);
                            new AGDB_AnGangJJInternaldockinginfo().insertInternaldockingInfoWithBackCount(
                                    cgTrustdeedinfo, "jjzsendfberr", "接受传输信息发生异常,异常原因为:" + msg);
                            return returnMsg(AnGangApiEnum.公开竞价资格预审结束后发标接口_二次发标, "", StatusEnum.接受失败.getValue(),
                                    "接受传输信息失败,失败原因为:" + msg);
                        }
                        new AGDB_AnGangJJInternaldockinginfo().insertInternaldockingInfoWithBackCount(cgTrustdeedinfo,
                                "jjzsendfbok", "成功");
                    }
                }

            }
            catch (Exception e) {
                logger.error(e.getMessage(), e);
                WriteLogHelper.writeLog(logger, LogLevelEnum.错误, SYSTEMNAME,
                        AnGangApiEnum.公开竞价资格预审结束后发标接口_二次发标.toString(), "接受传输信息发生异常,异常原因为:" + e.getMessage());
                return returnJJInfoMsg(sysName, "接受传输信息发生异常,异常原因为:" + e.getMessage(), 0, consignationCode,
                        bidSectionCode);
            }
            finally {
                if (null != service) {
                    service.close();
                }
            }

            return returnMsg(AnGangApiEnum.公开竞价资格预审结束后发标接口_二次发标, "", StatusEnum.接受成功.getValue(),
                    StatusEnum.接受成功.toString());
        }
    }

    /**
     * 开标状态
     * E1CG26
     */
    @RequestMapping(value = "/reciveJJKaiBiaoStatus", method = RequestMethod.POST)
    public String reciveJJKaiBiaoStatus(@RequestBody String receivejson) {
        ZtbCommonDao service = ZtbCommonDao.getInstance();
        if (StringUtil.isBlank(receivejson)) {
            WriteLogHelper.writeLog(logger, LogLevelEnum.警告, SYSTEMNAME, "", "接受到的传输信息为空");
            return returnJJInfoMsg("", "接受到的传输信息为空", 0, "", "");
        }
        else {
            String consignationCode = null;
            String bidSectionCode = null;
            String sysName = null;
            try {
                WriteLogHelper.writeLog(logger, LogLevelEnum.记录, SYSTEMNAME, "开标状态传招标平台",
                        "鞍钢reciveKaiBiaoStatus()接受到的传输信息为:" + receivejson);
                JSONObject json = JSON.parseObject(receivejson);
                if (json.getJSONObject("msgBody") == null) {
                    return returnJJInfoMsg("", "msgBody为空", 0, "", "");
                }
                JSONObject offlineResult = json.getJSONObject("msgBody");
                ZhglDataexchangeERP dataexchangeerp = new ZhglDataexchangeERP();
                if (StringUtil.isBlank(offlineResult.getString("bidSectionCode"))
                        || StringUtil.isBlank(offlineResult.getString("sysName"))) {
                    return returnJJInfoMsg(offlineResult.getString("sysName"), "数据问题sysName或bidSectionCode为空", 0,
                            offlineResult.getString("bidSectionCode"), offlineResult.getString("sysName"));
                }
                else {
                    dataexchangeerp.setRowguid(UUID.randomUUID().toString());
                    dataexchangeerp.setDatano(AnGangApiEnum.开标状态传招标平台.getValue());
                    dataexchangeerp.setDatacontent(receivejson);
                    dataexchangeerp.setFromplat(SYSTEMNAME);
                    dataexchangeerp.setInserttime(new Date());
                    dataexchangeerp.setDatatype("json");
                    dataexchangeerp.setOperatedate(new Date());
                    dataexchangeerp.setFullclassname(
                            "com.epoint.ztbtoerp.angangrestservice.AnGangService@reciveKaiBiaoStatus");
                }
                CgTrustdeedinfo cgTrustdeedinfo = getCgTrustDeedInfoByBiaoBao(
                        offlineResult.getString("bidSectionCode").trim(), offlineResult.getString("sysName"), "3", "1",
                        service);

                if (cgTrustdeedinfo == null || cgTrustdeedinfo.isEmpty()) {
                    dataexchangeerp.setInfoguid(offlineResult.getString("bidSectionCode").trim());
                    dataexchangeerp.setDatakey(offlineResult.getString("bidSectionCode").trim());
                    dataexchangeerp.setDatastatus("1");// 修改
                    service.insert(dataexchangeerp);
                    return returnJJInfoMsg(offlineResult.getString("sysName"), "委托单数据不存在,或者委托单未提交", 0,
                            offlineResult.getString("bidSectionCode"), offlineResult.getString("sysName"));
                }
                else {
                    dataexchangeerp.setInfoguid(cgTrustdeedinfo.getERPTrustDeedNo());
                    dataexchangeerp.setDatakey(cgTrustdeedinfo.getTrustguid());
                    dataexchangeerp.setDatastatus("1");// 修改
                    service.insert(dataexchangeerp);
                    String changeZBType = AnGangDataUtil.changeZBType(cgTrustdeedinfo, offlineResult, service);
                    if (StringUtil.isNotBlank(changeZBType)) {
                        Record record = new Record();
                        record.put("status", "0");
                        record.put("jjkbzt", changeZBType);
                        new AGDB_AnGangJJInternaldockinginfo().insertInternaldockingInfo(cgTrustdeedinfo, "jjkbzt",
                                record);
                        return returnJJInfoMsg(offlineResult.getString("sysName"), "接收成功！", 0,
                                offlineResult.getString("sysName").trim(), offlineResult.getString("bidSectionCode"));
                    }
                    Record record = new Record();
                    record.put("status", "1");
                    record.put("jjkbzt", "接收成功");
                    new AGDB_AnGangJJInternaldockinginfo().insertInternaldockingInfo(cgTrustdeedinfo, "jjkbzt", record);
                    return returnJJInfoMsg(offlineResult.getString("sysName"), "接收成功", 0,
                            offlineResult.getString("sysName").trim(), offlineResult.getString("bidSectionCode"));
                }
            }
            catch (Exception e) {
                logger.error(e.getMessage(), e);
                WriteLogHelper.writeLog(logger, LogLevelEnum.错误, SYSTEMNAME, AnGangApiEnum.开标状态传招标平台.toString(),
                        "接受传输信息发生异常,异常原因为:" + e.getMessage());
                return returnJJInfoMsg(sysName, e.getMessage(), 0, consignationCode, bidSectionCode);
            }
            finally {
                if (null != service) {
                    service.close();
                }
            }
        }
    }

    /**
     * 竞价澄清信息
     * E1CG27
     */
    @RequestMapping(value = "/reciveJJClearInfo", method = RequestMethod.POST)
    public String reciveJJClearInfo(@RequestBody String receivejson) {
        ZtbCommonDao service = ZtbCommonDao.getInstance();
        if (StringUtil.isBlank(receivejson)) {
            WriteLogHelper.writeLog(logger, LogLevelEnum.警告, SYSTEMNAME, "", "接受到的传输信息为空");
            return returnJJInfoMsg("", "接受到的传输信息为空", 0, "", "");
        }
        else {
            String consignationCode = null;
            String sectionCode = null;
            String sysName = null;
            try {
                WriteLogHelper.writeLog(logger, LogLevelEnum.记录, SYSTEMNAME, "澄清信息回调",
                        "本钢reciveDecision()接受到的传输信息为:" + receivejson);
                JSONObject json = JSON.parseObject(receivejson);
                if (json.getJSONObject("msgBody") == null) {
                    return returnJJInfoMsg("", "msgBody为空", 0, "", "");
                }
                JSONObject offlineResult = json.getJSONObject("msgBody");
                sectionCode = offlineResult.getString("sectionCode");
                consignationCode = offlineResult.getString("consignationCode");
                sysName = offlineResult.getString("sysName");
                ZhglDataexchangeERP dataexchangeerp = new ZhglDataexchangeERP();
                if (StringUtil.isBlank(sectionCode) || StringUtil.isBlank(sysName)) {
                    return returnJJInfoMsg("", "发送过来的数据存在问题sectionCode或sysName为空", 0, "", "");
                }
                else {
                    dataexchangeerp.setRowguid(UUID.randomUUID().toString());
                    dataexchangeerp.setDatano(AnGangApiEnum.发起澄清.getValue());
                    dataexchangeerp.setDatacontent(receivejson);
                    dataexchangeerp.setFromplat(SYSTEMNAME);
                    dataexchangeerp.setInserttime(new Date());
                    dataexchangeerp.setDatatype("json");
                    dataexchangeerp.setOperatedate(new Date());
                    dataexchangeerp
                            .setFullclassname("com.epoint.ztbtoerp.angangrestservice.AnGangService@reciveClearInfo");
                }
                CgTrustdeedinfo cgTrustdeedinfo = getCgTrustDeedInfoByBiaoBao(sectionCode, sysName, "3", "1", service);
                if (cgTrustdeedinfo == null || cgTrustdeedinfo.isEmpty()) {
                    dataexchangeerp.setInfoguid(sectionCode);
                    dataexchangeerp.setDatakey(sectionCode);
                    dataexchangeerp.setDatastatus("1");// 修改
                    service.insert(dataexchangeerp);
                    return returnJJInfoMsg(sysName, "委托单数据不存在", 0, sectionCode, consignationCode);
                }
                else {
                    dataexchangeerp.setDatakey(cgTrustdeedinfo.getTrustguid());
                    dataexchangeerp.setInfoguid(cgTrustdeedinfo.getERPTrustDeedNo());
                    dataexchangeerp.setDatastatus("1");// 修改
                    service.insert(dataexchangeerp);
                    String clearInfo = AnGangDataUtil.clearInfo(cgTrustdeedinfo, offlineResult, service);
                    if (StringUtil.isNotBlank(clearInfo)) {
                        Record record = new Record();
                        record.set("jjfqcq", clearInfo);
                        record.set("status", "0");
                        record.set("clarifyCode", offlineResult.getString("clarifyCode"));
                        new AGDB_AnGangJJInternaldockinginfo().insertInternaldockingInfo(cgTrustdeedinfo, "jjfqcq",
                                record);
                        return returnJJInfoMsg(offlineResult.getString("sysName"), "澄清失败", 0, sectionCode,
                                consignationCode);
                    }
                    else {
                        Record record = new Record();
                        record.set("jjfqcq", "澄清成功");
                        record.set("status", "1");
                        record.set("clarifyCode", offlineResult.getString("clarifyCode"));
                        new AGDB_AnGangJJInternaldockinginfo().insertInternaldockingInfo(cgTrustdeedinfo, "jjfqcq",
                                record);
                        return returnJJInfoMsg(offlineResult.getString("sysName"), clearInfo, 1, sectionCode,
                                consignationCode);
                    }
                }
            }
            catch (Exception e) {
                logger.error(e.getMessage(), e);
                WriteLogHelper.writeLog(logger, LogLevelEnum.错误, SYSTEMNAME, AnGangApiEnum.发起澄清.toString(),
                        "接受传输信息发生异常,异常原因为:" + e.getMessage());
                return returnJJInfoMsg(sysName, e.getMessage(), 0, sectionCode, consignationCode);
            }
            finally {
                if (null != service) {
                    service.close();
                }
            }
        }
    }

    /**
     * 接受流标信息通知
     * E1CG30
     */
    @RequestMapping(value = "/submitJJLiuBiaoInfo", method = RequestMethod.POST)
    public String reciveJJLiuBiaoInfo(@RequestBody String receivejson) {
        ZtbCommonDao service = ZtbCommonDao.getInstance();
        if (StringUtil.isBlank(receivejson)) {
            WriteLogHelper.writeLog(logger, LogLevelEnum.警告, SYSTEMNAME, AnGangApiEnum.流标_终止接口.toString(),
                    "接受到的传输信息为空");
            return returnMsg(AnGangApiEnum.流标_终止接口, "", StatusEnum.接受失败.getValue(), "传输信息为空!");
        }
        else {
            try {
                WriteLogHelper.writeLog(logger, LogLevelEnum.记录, SYSTEMNAME, AnGangApiEnum.流标_终止接口.toString(),
                        "鞍钢reciveChengJiaoInfo()接受到的传输信息为:" + receivejson);
                JSONObject json = JSON.parseObject(receivejson);
                if (json.getJSONObject("msgBody") == null) {
                    return returnMsg(AnGangApiEnum.流标_终止接口, "", StatusEnum.接受失败.getValue(), "传输信息为空!");
                }
                JSONObject liubiaoInfo = json.getJSONObject("msgBody").getJSONObject("data");
                ZhglDataexchangeERP dataexchangeerp = new ZhglDataexchangeERP();
                if (StringUtil.isBlank(liubiaoInfo.getString("bidSectionCode"))) {
                    return returnMsg(AnGangApiEnum.流标_终止接口, "", StatusEnum.接受失败.getValue(), "字段bidSectionCode传输信息为空!");
                }
                else {
                    dataexchangeerp.setRowguid(UUID.randomUUID().toString());
                    dataexchangeerp.setDatano(AnGangApiEnum.流标_终止接口.getValue());
                    dataexchangeerp.setDatacontent(receivejson);
                    dataexchangeerp.setFromplat(SYSTEMNAME);
                    dataexchangeerp.setInfoguid(liubiaoInfo.getString("bidSectionCode").trim());
                    dataexchangeerp.setInserttime(new Date());
                    dataexchangeerp.setDatatype("json");
                    dataexchangeerp.setOperatedate(new Date());
                    dataexchangeerp.setFullclassname(
                            "com.epoint.ztbtoerp.angangrestservice.AnGangService@reciveJJLiuBiaoInfo");
                }
                CgTrustdeedinfo cgTrustdeedinfo = getCgTrustDeedInfoByBiaoBao(
                        liubiaoInfo.getString("bidSectionCode").trim(), liubiaoInfo.getString("sysName").trim(), "3",
                        "1", service);
                if (cgTrustdeedinfo == null || cgTrustdeedinfo.isEmpty()) {
                    dataexchangeerp.setDatakey(liubiaoInfo.getString("bidSectionCode").trim());
                    dataexchangeerp.setInfoguid(liubiaoInfo.getString("bidSectionCode").trim());
                    dataexchangeerp.setDatastatus("1");// 修改
                    service.insert(dataexchangeerp);
                    return returnMsg(AnGangApiEnum.流标_终止接口, "", StatusEnum.接受失败.getValue(),
                            "bidSectionCode数据异常，未找到对应委托!");
                }
                else {
                    dataexchangeerp.setDatakey(cgTrustdeedinfo.getTrustguid());
                    dataexchangeerp.setInfoguid(cgTrustdeedinfo.getERPTrustDeedNo());
                    dataexchangeerp.setDatastatus("1");// 修改
                    service.insert(dataexchangeerp);
                    CgYichangbdhistory cgYichangbdhistory = service.find(
                            "select * from CG_YiChangBDHistory where biaoduanguid in(select biaoduanguid from cg_biaoduaninfo where trustguid=?) and auditstatus='3'",
                            CgYichangbdhistory.class, cgTrustdeedinfo.getTrustguid());
                    if (cgYichangbdhistory == null || cgYichangbdhistory.isEmpty()) {
                        cgYichangbdhistory = new CgYichangbdhistory();
                        String msg = new AnGangDataUtil().JJLiuBiaoInfo(cgYichangbdhistory, cgTrustdeedinfo,
                                liubiaoInfo, service);
                        if (StringUtil.isNotBlank(msg)) {
                            WriteLogHelper.writeLog(logger, LogLevelEnum.错误, SYSTEMNAME,
                                    AnGangApiEnum.流标_终止接口.toString(), "接受传输信息发生异常,异常原因为:" + msg);
                            new AGDB_AnGangJJInternaldockinginfo().insertInternaldockingInfoWithBackCount(
                                    cgTrustdeedinfo, "jjlbzzbackerr", "接受传输信息发生异常,异常原因为:" + msg);
                            return returnMsg(AnGangApiEnum.流标_终止接口, "", StatusEnum.接受失败.getValue(),
                                    "接受传输信息失败,失败原因为:" + msg);
                        }
                        new AGDB_AnGangJJInternaldockinginfo().insertInternaldockingInfoWithBackCount(cgTrustdeedinfo,
                                "jjlbzzbackok", "成功");
                    }
                    else {
                        new AGDB_AnGangJJInternaldockinginfo().insertInternaldockingInfoWithBackCount(cgTrustdeedinfo,
                                "jjlbzzbackok", "异常已处理过，本次调用无效!");
                        return returnMsg(AnGangApiEnum.流标_终止接口, "", StatusEnum.接受失败.getValue(), "数据异常，存在已完成的推荐成交数据!");
                    }
                }
            }
            catch (Exception e) {
                logger.error(e.getMessage(), e);
                WriteLogHelper.writeLog(logger, LogLevelEnum.错误, SYSTEMNAME, AnGangApiEnum.流标_终止接口.toString(),
                        "接受传输信息发生异常,异常原因为:" + e.getMessage());
                return returnMsg(AnGangApiEnum.流标_终止接口, "", StatusEnum.接受失败.getValue(),
                        "接受传输信息失败,失败原因为:" + e.getMessage());
            }
            finally {
                if (null != service) {
                    service.close();
                }
            }
            return returnMsg(AnGangApiEnum.流标_终止接口, "", StatusEnum.接受失败.getValue(), StatusEnum.接受成功.toString());
        }
    }

    /**
     * 接受公开招标转竞谈单一来源接口回调
     * E1CG51
     */
    @RequestMapping(value = "/submitJJGKToDYInfo", method = RequestMethod.POST)
    public String reciveJJGKToDYInfo(@RequestBody String receivejson) {
        ZtbCommonDao service = ZtbCommonDao.getInstance();
        if (StringUtil.isBlank(receivejson)) {
            WriteLogHelper.writeLog(logger, LogLevelEnum.警告, SYSTEMNAME, AnGangApiEnum.公开招标转竞谈_单一来源接口回调.toString(),
                    "接受到的传输信息为空");
            return returnMsg(AnGangApiEnum.公开招标转竞谈_单一来源接口回调, "", StatusEnum.接受失败.getValue(), "传输信息为空!");
        }
        else {
            try {
                WriteLogHelper.writeLog(logger, LogLevelEnum.记录, SYSTEMNAME, AnGangApiEnum.公开招标转竞谈_单一来源接口回调.toString(),
                        "鞍钢reciveChengJiaoInfo()接受到的传输信息为:" + receivejson);
                JSONObject json = JSON.parseObject(receivejson);
                if (json.getJSONObject("msgBody") == null) {
                    return returnMsg(AnGangApiEnum.公开招标转竞谈_单一来源接口回调, "", StatusEnum.接受失败.getValue(), "传输信息为空!");
                }
                JSONObject chengjiaoInfo = json.getJSONObject("msgBody");
                ZhglDataexchangeERP dataexchangeerp = new ZhglDataexchangeERP();
                if (StringUtil.isBlank(chengjiaoInfo.getString("bidSectionCode"))) {
                    return returnMsg(AnGangApiEnum.公开招标转竞谈_单一来源接口回调, "", StatusEnum.接受失败.getValue(),
                            "字段bidSectionCode传输信息为空!");
                }
                else {
                    dataexchangeerp.setRowguid(UUID.randomUUID().toString());
                    dataexchangeerp.setDatano(AnGangApiEnum.公开招标转竞谈_单一来源接口回调.getValue());
                    dataexchangeerp.setDatacontent(receivejson);
                    dataexchangeerp.setFromplat(SYSTEMNAME);
                    dataexchangeerp.setInfoguid(chengjiaoInfo.getString("bidSectionCode").trim());
                    dataexchangeerp.setInserttime(new Date());
                    dataexchangeerp.setDatatype("json");
                    dataexchangeerp.setOperatedate(new Date());
                    dataexchangeerp
                            .setFullclassname("com.epoint.ztbtoerp.angangrestservice.AnGangService@reciveJJGKToDYInfo");
                }
                CgTrustdeedinfo cgTrustdeedinfo = getCgTrustDeedInfoByBiaoBao(
                        chengjiaoInfo.getString("bidSectionCode").trim(), chengjiaoInfo.getString("sysName").trim(),
                        "3", "1", service);
                if (cgTrustdeedinfo == null || cgTrustdeedinfo.isEmpty()) {
                    dataexchangeerp.setDatakey(chengjiaoInfo.getString("bidSectionCode").trim());
                    dataexchangeerp.setInfoguid(chengjiaoInfo.getString("bidSectionCode").trim());
                    dataexchangeerp.setDatastatus("1");// 修改
                    service.insert(dataexchangeerp);
                    return returnMsg(AnGangApiEnum.公开招标转竞谈_单一来源接口回调, "", StatusEnum.接受失败.getValue(),
                            "bidSectionCode数据异常，未找到对应委托!");
                }
                else {
                    dataexchangeerp.setDatakey(cgTrustdeedinfo.getTrustguid());
                    dataexchangeerp.setInfoguid(cgTrustdeedinfo.getERPTrustDeedNo());
                    dataexchangeerp.setDatastatus("1");// 修改
                    service.insert(dataexchangeerp);
                    CgShoubiaotuijian shoubiaotuijian = service.find(
                            "select * from CG_ShouBiaoTuiJian where biaoduanguid in(select biaoduanguid from cg_biaoduaninfo where trustguid=?) and auditstatus='3'",
                            CgShoubiaotuijian.class, cgTrustdeedinfo.getTrustguid());
                    if (shoubiaotuijian == null || shoubiaotuijian.isEmpty()) {
                        // shoubiaotuijian = new CgShoubiaotuijian();
                        // String msg = new
                        // AnGangDataUtil().JJChengJiaoInfo(shoubiaotuijian,
                        // cgTrustdeedinfo,
                        // chengjiaoInfo, service);
                        // if (StringUtil.isNotBlank(msg)) {
                        // WriteLogHelper.writeLog(logger, LogLevelEnum.错误,
                        // SYSTEMNAME,
                        // AnGangApiEnum.公开招标转竞谈_单一来源接口回调.toString(),
                        // "接受传输信息发生异常,异常原因为:" + msg);
                        // return returnMsg(AnGangApiEnum.公开招标转竞谈_单一来源接口回调, "",
                        // StatusEnum.接受失败.getValue(),
                        // "接受传输信息失败,失败原因为:" + msg);
                        // }
                    }
                    else {
                        // return returnMsg(AnGangApiEnum.公开招标转竞谈_单一来源接口回调, "",
                        // StatusEnum.接受失败.getValue(),
                        // "数据异常，存在已完成的推荐成交数据!");
                    }
                }
            }
            catch (Exception e) {
                logger.error(e.getMessage(), e);
                WriteLogHelper.writeLog(logger, LogLevelEnum.错误, SYSTEMNAME, AnGangApiEnum.公开招标转竞谈_单一来源接口回调.toString(),
                        "接受传输信息发生异常,异常原因为:" + e.getMessage());
                return returnMsg(AnGangApiEnum.公开招标转竞谈_单一来源接口回调, "", StatusEnum.接受失败.getValue(),
                        "接受传输信息失败,失败原因为:" + e.getMessage());
            }
            finally {
                if (null != service) {
                    service.close();
                }
            }
            return returnMsg(AnGangApiEnum.公开招标转竞谈_单一来源接口回调, "", StatusEnum.接受失败.getValue(),
                    StatusEnum.接受成功.toString());
        }
    }

    private CgTrustdeedinfo getCgTrustDeedInfo(String ERPTrustDeedNo, String sysname, String auditstatus,
            String isErpJingJia, ZtbCommonDao service) {
        SqlBuilder sqlBuilder = new SqlBuilder();
        sqlBuilder.append("select * from CG_TrustDeedInfo where ERPTrustDeedNo=?", ERPTrustDeedNo);
        if (StringUtil.isNotBlank(sysname)) {
            sqlBuilder.append(" and FromPlatNo=?", sysname);
        }
        if (StringUtil.isNotBlank(auditstatus)) {
            sqlBuilder.append(" and auditstatus=?", auditstatus);
        }
        if (StringUtil.isNotBlank(isErpJingJia)) {
            sqlBuilder.append(" and ifnull(isErpJingJia,'0')=?", isErpJingJia);
        }
        sqlBuilder.append(" and fromplat=?", SYSTEMNAME);
        return service.find(sqlBuilder.getSql(), CgTrustdeedinfo.class, sqlBuilder.getParams());
    }

    /**
     * 接受成交信息通知
     * E1CG29
     */
    @RequestMapping(value = "/submitJJChengJiaoInfo", method = RequestMethod.POST)
    public String reciveJJChengJiaoInfo(@RequestBody String receivejson) {
        ZtbCommonDao service = ZtbCommonDao.getInstance();
        if (StringUtil.isBlank(receivejson)) {
            WriteLogHelper.writeLog(logger, LogLevelEnum.警告, SYSTEMNAME, AnGangApiEnum.成交通知接口.toString(), "接受到的传输信息为空");
            return returnMsg(AnGangApiEnum.成交通知接口, "", StatusEnum.接受失败.getValue(), "传输信息为空!");
        }
        else {
            try {
                WriteLogHelper.writeLog(logger, LogLevelEnum.记录, SYSTEMNAME, AnGangApiEnum.成交通知接口.toString(),
                        "鞍钢reciveChengJiaoInfo()接受到的传输信息为:" + receivejson);
                JSONObject json = JSON.parseObject(receivejson);
                if (json.getJSONObject("msgBody") == null) {
                    return returnMsg(AnGangApiEnum.成交通知接口, "", StatusEnum.接受失败.getValue(), "传输信息为空!");
                }
                JSONObject chengjiaoInfo = json.getJSONObject("msgBody");
                ZhglDataexchangeERP dataexchangeerp = new ZhglDataexchangeERP();
                if (StringUtil.isBlank(chengjiaoInfo.getString("bidSectionCode"))) {
                    return returnMsg(AnGangApiEnum.成交通知接口, "", StatusEnum.接受失败.getValue(), "字段bidSectionCode传输信息为空!");
                }
                else {
                    dataexchangeerp.setRowguid(UUID.randomUUID().toString());
                    dataexchangeerp.setDatano(AnGangApiEnum.成交通知接口.getValue());
                    dataexchangeerp.setDatacontent(receivejson);
                    dataexchangeerp.setFromplat(SYSTEMNAME);
                    dataexchangeerp.setInserttime(new Date());
                    dataexchangeerp.setDatatype("json");
                    dataexchangeerp.setOperatedate(new Date());
                    dataexchangeerp.setFullclassname(
                            "com.epoint.ztbtoerp.angangrestservice.AnGangService@reciveJJChengJiaoInfo");
                }
                CgTrustdeedinfo cgTrustdeedinfo = getCgTrustDeedInfoByBiaoBao(
                        chengjiaoInfo.getString("bidSectionCode").trim(), chengjiaoInfo.getString("sysName").trim(),
                        "3", "1", service);
                if (cgTrustdeedinfo == null || cgTrustdeedinfo.isEmpty()) {
                    dataexchangeerp.setDatakey(chengjiaoInfo.getString("bidSectionCode").trim());
                    dataexchangeerp.setInfoguid(chengjiaoInfo.getString("bidSectionCode").trim());
                    dataexchangeerp.setDatastatus("1");// 修改
                    service.insert(dataexchangeerp);
                    return returnMsg(AnGangApiEnum.成交通知接口, "", StatusEnum.接受失败.getValue(),
                            "bidSectionCode数据异常，未找到对应委托!");
                }
                else {
                    dataexchangeerp.setDatakey(cgTrustdeedinfo.getTrustguid());
                    dataexchangeerp.setInfoguid(cgTrustdeedinfo.getERPTrustDeedNo());
                    dataexchangeerp.setDatastatus("1");// 修改
                    service.insert(dataexchangeerp);
                    AGCgZhongbiaojieguo cgZhongbiaojieguo = service.find(
                            "select * from CG_ZhongBiaoJieGuo where biaoduanguid =? and auditstatus='3'",
                            AGCgZhongbiaojieguo.class, cgTrustdeedinfo.getBiaoDuanGuid());
                    if (cgZhongbiaojieguo == null || cgZhongbiaojieguo.isEmpty()) {
                        cgZhongbiaojieguo = new AGCgZhongbiaojieguo();
                        String msg = new AnGangDataUtil().JJChengJiaoInfo(cgZhongbiaojieguo, cgTrustdeedinfo,
                                chengjiaoInfo, service);
                        if (StringUtil.isNotBlank(msg)) {
                            WriteLogHelper.writeLog(logger, LogLevelEnum.错误, SYSTEMNAME,
                                    AnGangApiEnum.成交通知接口.toString(), "接受传输信息发生异常,异常原因为:" + msg);
                            new AGDB_AnGangJJInternaldockinginfo().insertInternaldockingInfoWithBackCount(
                                    cgTrustdeedinfo, "jjchengjiaotzbackerr", msg);
                            return returnMsg(AnGangApiEnum.成交通知接口, "", StatusEnum.接受失败.getValue(),
                                    "接受传输信息失败,失败原因为:" + msg);
                        }
                        new AGDB_AnGangJJInternaldockinginfo().insertInternaldockingInfoWithBackCount(cgTrustdeedinfo,
                                "jjchengjiaotzbackok", "成功");
                    }
                    else {
                        new AGDB_AnGangJJInternaldockinginfo().insertInternaldockingInfoWithBackCount(cgTrustdeedinfo,
                                "jjchengjiaotzbackerr", "发布成交公告失败,存在已完成的数据");
                        return returnMsg(AnGangApiEnum.成交通知接口, "", StatusEnum.接受失败.getValue(), "数据异常，存在已完成的推荐成交数据!");
                    }
                }
            }
            catch (Exception e) {
                logger.error(e.getMessage(), e);
                WriteLogHelper.writeLog(logger, LogLevelEnum.错误, SYSTEMNAME, AnGangApiEnum.成交通知接口.toString(),
                        "接受传输信息发生异常,异常原因为:" + e.getMessage());
                return returnMsg(AnGangApiEnum.成交通知接口, "", StatusEnum.接受失败.getValue(),
                        "接受传输信息失败,失败原因为:" + e.getMessage());
            }
            finally {
                if (null != service) {
                    service.close();
                }
            }
            return returnMsg(AnGangApiEnum.成交通知接口, "", StatusEnum.接受失败.getValue(), StatusEnum.接受成功.toString());
        }
    }

    /**
     * 成交公告接口
     * E1CG28
     */
    @RequestMapping(value = "/submitJJChengJiaoGGInfo", method = RequestMethod.POST)
    public String reciveJJChengJiaoGGInfo(@RequestBody String receivejson) {
        ZtbCommonDao service = ZtbCommonDao.getInstance();
        if (StringUtil.isBlank(receivejson)) {
            WriteLogHelper.writeLog(logger, LogLevelEnum.警告, SYSTEMNAME, AnGangApiEnum.成交公告接口.toString(), "接受到的传输信息为空");
            return returnMsg(AnGangApiEnum.成交公告接口, "", StatusEnum.接受失败.getValue(), "传输信息为空!");
        }
        else {
            try {
                WriteLogHelper.writeLog(logger, LogLevelEnum.记录, SYSTEMNAME, AnGangApiEnum.成交通知接口.toString(),
                        "鞍钢reciveJJChengJiaoGGInfo()接受到的传输信息为:" + receivejson);
                JSONObject json = JSON.parseObject(receivejson);
                if (json.getJSONObject("msgBody") == null) {
                    return returnMsg(AnGangApiEnum.成交公告接口, "", StatusEnum.接受失败.getValue(), "传输信息为空!");
                }
                JSONObject chengjiaoInfo = json.getJSONObject("msgBody");
                ZhglDataexchangeERP dataexchangeerp = new ZhglDataexchangeERP();
                if (StringUtil.isBlank(chengjiaoInfo.getString("bidSectionCode"))) {
                    return returnMsg(AnGangApiEnum.成交通知接口, "", StatusEnum.接受失败.getValue(), "字段bidSectionCode传输信息为空!");
                }
                else {
                    dataexchangeerp.setRowguid(UUID.randomUUID().toString());
                    dataexchangeerp.setDatano(AnGangApiEnum.成交公告接口.getValue());
                    dataexchangeerp.setDatacontent(receivejson);
                    dataexchangeerp.setFromplat(SYSTEMNAME);
                    dataexchangeerp.setInserttime(new Date());
                    dataexchangeerp.setDatatype("json");
                    dataexchangeerp.setOperatedate(new Date());
                    dataexchangeerp.setFullclassname(
                            "com.epoint.ztbtoerp.angangrestservice.AnGangService@reciveJJChengJiaoGGInfo");
                }
                CgTrustdeedinfo cgTrustdeedinfo = getCgTrustDeedInfoByBiaoBao(
                        chengjiaoInfo.getString("bidSectionCode").trim(), chengjiaoInfo.getString("sysName").trim(),
                        "3", "1", service);
                if (cgTrustdeedinfo == null || cgTrustdeedinfo.isEmpty()) {
                    dataexchangeerp.setDatakey(chengjiaoInfo.getString("bidSectionCode").trim());
                    dataexchangeerp.setInfoguid(chengjiaoInfo.getString("bidSectionCode").trim());
                    dataexchangeerp.setDatastatus("1");// 修改
                    service.insert(dataexchangeerp);
                    return returnMsg(AnGangApiEnum.成交公告接口, "", StatusEnum.接受失败.getValue(),
                            "bidSectionCode数据异常，未找到对应委托!");
                }
                else {
                    dataexchangeerp.setDatakey(cgTrustdeedinfo.getTrustguid());
                    dataexchangeerp.setInfoguid(cgTrustdeedinfo.getERPTrustDeedNo());
                    dataexchangeerp.setDatastatus("1");// 修改
                    service.insert(dataexchangeerp);
                    CgZhongbiaogs cgzhongbiaogs = service.find(
                            "select * from cg_zhongbiaogs where gonggaoguid in(select gonggaoguid from cg_zbgsandbd where biaoduanGuid=?) and auditstatus='3'",
                            CgZhongbiaogs.class, cgTrustdeedinfo.getBiaoDuanGuid());
                    if (cgzhongbiaogs == null || cgzhongbiaogs.isEmpty()) {
                        cgzhongbiaogs = new CgZhongbiaogs();
                        String msg = new AnGangDataUtil().JJChengJiaoGGInfo(cgzhongbiaogs, cgTrustdeedinfo,
                                chengjiaoInfo, service);
                        if (StringUtil.isNotBlank(msg)) {
                            WriteLogHelper.writeLog(logger, LogLevelEnum.错误, SYSTEMNAME,
                                    AnGangApiEnum.成交公告接口.toString(), "接受传输信息发生异常,异常原因为:" + msg);
                            new AGDB_AnGangJJInternaldockinginfo().insertInternaldockingInfoWithBackCount(
                                    cgTrustdeedinfo, "jjchengjiaoggbackerr", "接受传输信息发生异常,异常原因为:" + msg);
                            return returnMsg(AnGangApiEnum.成交公告接口, "", StatusEnum.接受失败.getValue(),
                                    "接受传输信息失败,失败原因为:" + msg);
                        }
                        new AGDB_AnGangJJInternaldockinginfo().insertInternaldockingInfoWithBackCount(cgTrustdeedinfo,
                                "jjchengjiaoggbackok", "成功");
                    }
                    else {
                        new AGDB_AnGangJJInternaldockinginfo().insertInternaldockingInfoWithBackCount(cgTrustdeedinfo,
                                "jjchengjiaoggbackerr", "数据异常，存在已完成的推荐成交数据!");
                        return returnMsg(AnGangApiEnum.成交公告接口, "", StatusEnum.接受失败.getValue(), "数据异常，存在已完成的推荐成交数据!");
                    }
                }
            }
            catch (Exception e) {
                logger.error(e.getMessage(), e);
                WriteLogHelper.writeLog(logger, LogLevelEnum.错误, SYSTEMNAME, AnGangApiEnum.成交公告接口.toString(),
                        "接受传输信息发生异常,异常原因为:" + e.getMessage());
                return returnMsg(AnGangApiEnum.成交公告接口, "", StatusEnum.接受失败.getValue(),
                        "接受传输信息失败,失败原因为:" + e.getMessage());
            }
            finally {
                if (null != service) {
                    service.close();
                }
            }
            return returnMsg(AnGangApiEnum.成交公告接口, "", StatusEnum.接受失败.getValue(), StatusEnum.接受成功.toString());
        }
    }

    private CgTrustdeedinfo getCgTrustDeedInfoByBiaoBao(String biaoBaoNo, String sysname, String auditstatus,
            String isErpJingJia, ZtbCommonDao service) {
        SqlBuilder sqlBuilder = new SqlBuilder();
        sqlBuilder.append("select * from CG_TrustDeedInfo where TrustDeedNo=?", biaoBaoNo);
        if (StringUtil.isNotBlank(sysname)) {
            sqlBuilder.append(" and FromPlatNo=?", sysname);
        }
        if (StringUtil.isNotBlank(auditstatus)) {
            sqlBuilder.append(" and auditstatus=?", auditstatus);
        }
        if (StringUtil.isNotBlank(isErpJingJia)) {
            sqlBuilder.append(" and ifnull(isErpJingJia,'0')=?", isErpJingJia);
        }
        sqlBuilder.append(" and fromplat=?", SYSTEMNAME);
        Log.info("biaoBaoNo:" + biaoBaoNo + "sysname" + sysname + "SYSTEMNAME" + SYSTEMNAME);
        return service.find(sqlBuilder.getSql(), CgTrustdeedinfo.class, sqlBuilder.getParams());
    }

    private String returnMsg(AnGangApiEnum apiEnum, String businessId, String status, String remark) {
        JSONObject jsoncustom = new JSONObject();
        JSONObject msgBody = new JSONObject();
        msgBody.put("interfaceId", apiEnum.getValue());
        msgBody.put("interfaceName", apiEnum.toString());
        msgBody.put("businessId", businessId);
        msgBody.put("status", status);
        msgBody.put("remark", remark);
        jsoncustom.put("msgBody", msgBody);
        return jsoncustom.toJSONString();
    }

    /**
     * 单一来源返回信息
     */
    private String returnSingleMsg(String signId, String backStatus, String backMsg, String systemId) {
        JSONObject jsoncustom = new JSONObject();
        JSONObject msgBody = new JSONObject();
        msgBody.put("backStatus", backStatus);
        msgBody.put("backMsg", backMsg);
        msgBody.put("signId", signId);
        //msgBody.put("systemId", systemId);
        jsoncustom.put("msgBody", msgBody);
        return jsoncustom.toJSONString();
    }

    private String returnGysnumMsg(String systemId, int payNumber, int bidNumber) {
        JSONObject jsoncustom = new JSONObject();
        JSONObject msgBody = new JSONObject();
        msgBody.put("systemId", systemId);
        msgBody.put("payNumber", payNumber);
        msgBody.put("bidNumber", bidNumber);
        jsoncustom.put("msgBody", msgBody);
        return jsoncustom.toJSONString();
    }

    private String returnJJInfoMsg(String systemId, String backMsg, int status, String bidSectionCode,
            String bidEntrNo) {
        // TODO Auto-generated method stub
        JSONObject jsoncustom = new JSONObject();
        JSONObject msgBody = new JSONObject();
        msgBody.put("systemId", systemId);
        msgBody.put("bidEntrNo", bidEntrNo);
        msgBody.put("bidSectionCode", bidSectionCode);
        msgBody.put("backMsg", backMsg);
        msgBody.put("status", status);
        jsoncustom.put("msgBody", msgBody);
        return jsoncustom.toJSONString();
    }

    private String checkQingDanHasZero(String purchasetype, JSONObject modifInfo) {
        JSONArray details = null;
        if ("A".equalsIgnoreCase(purchasetype)) {
            details = modifInfo.getJSONArray("cgnEngDetails");
        }
        else if ("M".equalsIgnoreCase(purchasetype)) {
            details = modifInfo.getJSONArray("cgnMaterialDetails");
            if (details == null) {
                details = modifInfo.getJSONArray("cgnDeviceDetails");
            }
        }
        else if ("S".equalsIgnoreCase(purchasetype)) {
            details = modifInfo.getJSONArray("cgnServiceDetail");
        }
        if (details != null && !details.isEmpty()) {
            for (Object object : details) {
                JSONObject detail = (JSONObject) object;
                Double planPrice = detail.getDouble("planPrice");
                if ("S".equalsIgnoreCase(purchasetype)) {
                    planPrice = detail.getDouble("PlannedPrice");
                }
                if (planPrice == null || planPrice == 0.00) {
                    return "委托单存在计划单价为0或为空";
                }
            }
        }
        else {
            return "委托单存在计划单价为0或为空";
        }

        return "";
    }
}
