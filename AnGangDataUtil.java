package com.epoint.ztbtoerp.angangrestservice;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import org.apache.log4j.Logger;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.epoint.HuiYuan.domain.HuiYuanUser;
import com.epoint.HuiYuan.domain.HuiyuanUnitcominfo;
import com.epoint.Jsgc.domain.*;
import com.epoint.Jsgc.domain.CgZhongbiaodetail;
import com.epoint.NetZtbMis.ViewZBFileInfo.GlobalParams;
import com.epoint.ZtbCommon.SqlBuilder;
import com.epoint.ZtbCommon.ZtbCommonDao;
import com.epoint.ZtbMis.domain.ZhglAttachinfoYewu;
import com.epoint.ag.api.sendmessage.SendMessage;
import com.epoint.ag.jsgc.domaim.AGCgZschen<PERSON>qing;
import com.epoint.ag.qyztb.zbtool.info.AGZBToolInfoProxy;
import com.epoint.ag.qyztbmis.bizlogic.AGDB_JSGC_GZGongGao;
import com.epoint.ag.qyztbmis.bizlogic.DB_Trustdeed;
import com.epoint.ag.qyztbmis.bizlogic.erp.util.ERPCommon;
import com.epoint.ag.qyztbmis.docking.common.ProNameEnum;
import com.epoint.ag.qyztbmis.docking.common.SystemEnum;
import com.epoint.ag.qyztbmis.domain.*;
import com.epoint.ag.qyztbmis.materialmanage.entity.AGCgEscategory;
import com.epoint.ag.qyztbmis.outmethod.AGQYZtb;
import com.epoint.ag.qyztbmis.proxy.entity.*;
import com.epoint.basic.bizlogic.orga.ou.service.FrameOuService9;
import com.epoint.basic.bizlogic.orga.user.service.FrameUserService9;
import com.epoint.basic.bizlogic.sysconf.code.service.CodeItemsService9;
import com.epoint.basic.bizlogic.sysconf.systemparameters.service.FrameConfigService9;
import com.epoint.core.grammar.Record;
import com.epoint.core.utils.collection.EpointCollectionUtils;
import com.epoint.core.utils.date.EpointDateUtil;
import com.epoint.core.utils.regex.RegexValidateUtil;
import com.epoint.core.utils.string.StringUtil;
import com.epoint.frame.service.message.entity.MessagesCenter;
import com.epoint.frame.service.message.impl.MessagesCenterService9;
import com.epoint.frame.service.metadata.code.entity.CodeItems;
import com.epoint.frame.service.organ.ou.entity.FrameOu;
import com.epoint.frame.service.organ.user.entity.FrameUser;
import com.epoint.qyztb.common.ContextHelper;
import com.epoint.qyztbmis.bizlogic.DB_CG_TouBiaoDanWei_QY;
import com.epoint.qyztbmis.bizlogic.DB_GetSerialNumber_QY;
import com.epoint.qyztbmis.bizlogic.commonutil.TwoTuple;
import com.epoint.qyztbmis.domain.*;
import com.epoint.qyztbmis.requestplan.entity.PRPRLineItem;
import com.epoint.qyztbmis.requestplan.entity.PRPurchaseReq;
import com.epoint.qyztbmis.resterm.TermCodeService;
import com.epoint.ztb.ca.common.Functions;
import com.epoint.ztb.hyfilestatus.HyFileStatusService;
import com.epoint.ztbtoerp.util.CommonUtil;
import com.epoint.ztbtoerp.util.ERPFrameSysUtil;
import com.esotericsoftware.minlog.Log;

import Epoint.HuiYuanZtbMis.Bizlogic.HuiYuan.DB_HuiYuan_AlertInfo;
import Epoint.JSGCZtbMis.Bizlogic.JSGC.ChengQingBeiAn.DB_ZiShenChengQing;
import Epoint.ZtbMis.Bizlogic.VelocityHelper;
import Epoint.ZtbMis.Bizlogic.Common.EpZtbEncrypt;
import Epoint.ZtbMis.Bizlogic.Sys.DB_AllowTo;
import Epoint.ZtbMis.Bizlogic.Sys.DB_Frame_CodeItem;
import Epoint.ZtbMis.Bizlogic.Sys.DB_ZHGL_AttachInfo_YeWu;
import EpointBid_Constant.Enums.ClsEnum;
import EpointBid_Constant.Enums.ClsEnum.AuditStatus;

public class AnGangDataUtil
{
    private static final Logger logger = Logger.getLogger(AnGangDataUtil.class);

    private CodeItemsService9 codeservice = new CodeItemsService9();
    private ERPFrameSysUtil erpFrameSysUtil = new ERPFrameSysUtil();
    private String SupplyTypeId = "";

    private CodeItemsService9 codeItemsService9;

    /**
     * 采购公告接口
     */
    public String cgyg(JSONObject modifInfo, ZtbCommonDao service) {
        try {
            JSONObject consignation = modifInfo.getJSONObject("consignation");
            JSONArray cgnservicedetailList = modifInfo.getJSONArray("cgnServiceDetail");
            PRPurchaseReq prpurchasereq = service.find(PRPurchaseReq.class, consignation.getString("prno"));
            boolean isAdd = false;
            if (prpurchasereq != null && !prpurchasereq.isEmpty()) {
                if (!"1".equals(prpurchasereq.getAuditstatus())) {
                    return "当前采购预告已存在，状态【"
                            + new DB_Frame_CodeItem().getCodeText("显示审核状态", prpurchasereq.getAuditstatus())
                            + "】中，不允许操作！";
                }
                prpurchasereq.setOperateusername("erpsync");
                prpurchasereq.setAuditstatus("1");
                // 初始化数据
                prpurchasereq.setOperatedate(new Date());
                prpurchasereq.setSBR_Date(new Date());

                // 采购需求信息
                // prno 传的是rowguid
                // prpurchasereq.setPRNO(consignation.getString("prno"));
                prpurchasereq.set("prname", consignation.getString("prname"));
                prpurchasereq.set("projectjiaoyitype", consignation.getString("projectjiaoyitype"));
                prpurchasereq.set("ygsstart",
                        EpointDateUtil.convertString2Date(consignation.getString("ygsstart"), "yyyyMMddHHmmss"));
                prpurchasereq.set("ygsend",
                        EpointDateUtil.convertString2Date(consignation.getString("ygsend"), "yyyyMMddHHmmss"));

                // 网站首页正文内容
                prpurchasereq.setPRContent(consignation.getString("prcontent"));
                // 采购方与招标机构
                FrameOu dailiou = erpFrameSysUtil
                        .getFrameOuByXiaQuToDaiLi(formatString(consignation.getString("DailiName")), service);
                if (dailiou != null && !dailiou.isEmpty()) {
                    prpurchasereq.set("DailiName", dailiou.getOuname());
                    prpurchasereq.set("DailiGuid", dailiou.getOuguid());
                }
                prpurchasereq.setXiaQuCode(consignation.getString("bidCompanyName"));
                FrameOu zhaobiaogongsi = erpFrameSysUtil
                        .getFrameOuByXiaQu(formatString(consignation.getString("bidCompanyName")), service);
                if (zhaobiaogongsi != null && !zhaobiaogongsi.isEmpty()) {
                    prpurchasereq.set("Zbgsdizhi", zhaobiaogongsi.getAddress());
                }

                FrameUser wtrMan = erpFrameSysUtil.getFrameUser(modifInfo.getString("enterpriseCode"), service);
                if (wtrMan != null && !wtrMan.isEmpty()) {
                    prpurchasereq.setSBR_UnitGuid(wtrMan.getOuGuid());
                    prpurchasereq.setSBR_Code(wtrMan.getUserGuid());
                    prpurchasereq.setSBR_Name(wtrMan.getDisplayName());
                    prpurchasereq.set("workemail", wtrMan.getEmail());
                    prpurchasereq.setSBR_Tel(wtrMan.getMobile());
                    FrameOu ou = new FrameOuService9().getOuByOuGuid(wtrMan.getOuGuid());
                    if (ou != null && !ou.isEmpty()) {
                        prpurchasereq.setPRCompany(ou.getOuname());
                        prpurchasereq.set("CaiGouApplyDanWei", ou.getOuname());
                        prpurchasereq.setSBR_UnitName(ou.getOuname());// 部门名称
                        prpurchasereq.set("Wtdwdizhi", ou.getAddress());
                    }
                }
                else {
                    throw new RuntimeException(
                            "未在本系统中查询到【计划员名字（委托人）】名为" + modifInfo.getString("enterpriseName") + " 的用户！");
                }

                // 需求清单
                for (Object cgnservice : cgnservicedetailList) {
                    JSONObject cgnservicedetail = JSON.parseObject(JSON.toJSONString(cgnservice));
                    PRPRLineItem prprlineitem = service.find(
                            "select * from PR_PRLineItem where ReqGuid=? and rowguid=?", PRPRLineItem.class,
                            prpurchasereq.getRowguid(), cgnservicedetail.getString("cgnguid"));
                    if (prprlineitem != null && !prprlineitem.isEmpty()) {
                        prprlineitem.setCurrency("156");// 默认人民币
                        prprlineitem.setOperatedate(new Date());
                        prprlineitem.setOperateusername(wtrMan.getDisplayName());
                        prprlineitem.setSbr_Unitguid(wtrMan.getOuGuid());
                        prprlineitem.setProductCommodity(cgnservicedetail.getString("productcommodity"));
                        double totalprice = new BigDecimal(cgnservicedetail.getString("totalprice"))
                                .setScale(6, BigDecimal.ROUND_HALF_EVEN).doubleValue();
                        prprlineitem.setTotalPrice(totalprice);
                        prprlineitem
                                .set("expecteddate",
                                        EpointDateUtil.convertDate2String(
                                                EpointDateUtil.convertString2Date(
                                                        cgnservicedetail.getString("expecteddate"), "yyyyMMddHHmmss"),
                                                "yyyy-MM-dd"));
                        prprlineitem.set("productdescription", cgnservicedetail.getString("Productdescription"));
                        prprlineitem.set("remark", cgnservicedetail.getString("remark"));
                        service.update(prprlineitem);
                    }
                    else {
                        prprlineitem = new PRPRLineItem();
                        prprlineitem.setRowguid(cgnservicedetail.getString("cgnguid"));
                        String lineItemNo = new DB_GetSerialNumber_QY().CreateNewNo("采购需求行项目编号", 4, null, null);
                        prprlineitem.setCurrency("156");// 默认人民币
                        prprlineitem.setLineItemNo(lineItemNo);
                        prprlineitem.setOperatedate(new Date());
                        prprlineitem.setOperateusername(wtrMan.getDisplayName());
                        prprlineitem.setSbr_Unitguid(wtrMan.getOuGuid());
                        prprlineitem.setReqGuid(prpurchasereq.getRowguid());
                        prprlineitem.setProductCommodity(cgnservicedetail.getString("productcommodity"));
                        double totalprice = new BigDecimal(cgnservicedetail.getString("totalprice"))
                                .setScale(6, BigDecimal.ROUND_HALF_EVEN).doubleValue();
                        prprlineitem.setTotalPrice(totalprice);
                        prprlineitem
                                .set("expecteddate",
                                        EpointDateUtil.convertDate2String(
                                                EpointDateUtil.convertString2Date(
                                                        cgnservicedetail.getString("expecteddate"), "yyyyMMddHHmmss"),
                                                "yyyy-MM-dd"));
                        prprlineitem.set("productdescription", cgnservicedetail.getString("Productdescription"));
                        service.insert(prprlineitem);
                    }
                }

                prpurchasereq.set("gonggaocontent",
                        gongGaoContent(false, prpurchasereq.getRowguid(), prpurchasereq.getPRContent(), service));
                prpurchasereq.set("webgonggaocontent",
                        gongGaoContent(true, prpurchasereq.getRowguid(), prpurchasereq.getPRContent(), service));

                service.update(prpurchasereq);
            }
            else {
                isAdd = true;
                prpurchasereq = new PRPurchaseReq();
                String rowguid = consignation.getString("prno");
                prpurchasereq.setRowguid(rowguid);
                prpurchasereq.setOperateusername("erpsync");
                prpurchasereq.setAuditstatus("1");
                // 初始化数据
                prpurchasereq.setOperatedate(new Date());
                prpurchasereq.setSBR_Date(new Date());

                // 采购需求信息
                // prno 传的是rowguid
                // prpurchasereq.setPRNO(consignation.getString("prno"));
                prpurchasereq.set("prname", consignation.getString("prname"));
                prpurchasereq.set("projectjiaoyitype", consignation.getString("projectjiaoyitype"));
                prpurchasereq.set("ygsstart",
                        EpointDateUtil.convertString2Date(consignation.getString("ygsstart"), "yyyyMMddHHmmss"));
                prpurchasereq.set("ygsend",
                        EpointDateUtil.convertString2Date(consignation.getString("ygsend"), "yyyyMMddHHmmss"));

                // 网站首页正文内容
                prpurchasereq.setPRContent(consignation.getString("prcontent"));
                // 采购方与招标机构
                FrameOu dailiou = erpFrameSysUtil
                        .getFrameOuByXiaQuToDaiLi(formatString(consignation.getString("DailiName")), service);
                if (dailiou != null && !dailiou.isEmpty()) {
                    prpurchasereq.set("DailiName", dailiou.getOuname());
                    prpurchasereq.set("DailiGuid", dailiou.getOuguid());
                }
                prpurchasereq.setXiaQuCode(consignation.getString("bidCompanyName"));
                FrameOu zhaobiaogongsi = erpFrameSysUtil
                        .getFrameOuByXiaQu(formatString(consignation.getString("bidCompanyName")), service);
                if (zhaobiaogongsi != null && !zhaobiaogongsi.isEmpty()) {
                    prpurchasereq.set("Zbgsdizhi", zhaobiaogongsi.getAddress());
                }

                FrameUser wtrMan = erpFrameSysUtil.getFrameUser(modifInfo.getString("enterpriseCode"), service);
                if (wtrMan != null && !wtrMan.isEmpty()) {
                    prpurchasereq.setSBR_UnitGuid(wtrMan.getOuGuid());
                    prpurchasereq.setSBR_Code(wtrMan.getUserGuid());
                    prpurchasereq.setSBR_Name(wtrMan.getDisplayName());
                    prpurchasereq.set("workemail", wtrMan.getEmail());
                    prpurchasereq.setSBR_Tel(wtrMan.getMobile());
                    FrameOu ou = new FrameOuService9().getOuByOuGuid(wtrMan.getOuGuid());
                    if (ou != null && !ou.isEmpty()) {
                        prpurchasereq.setPRCompany(ou.getOuname());
                        prpurchasereq.set("CaiGouApplyDanWei", ou.getOuname());
                        prpurchasereq.setSBR_UnitName(ou.getOuname());// 部门名称
                        prpurchasereq.set("Wtdwdizhi", ou.getAddress());
                    }
                }
                else {
                    throw new RuntimeException(
                            "未在本系统中查询到【计划员名字（委托人）】名为" + modifInfo.getString("enterpriseName") + " 的用户！");
                }

                // 需求清单
                for (Object cgnservice : cgnservicedetailList) {
                    JSONObject cgnservicedetail = JSON.parseObject(JSON.toJSONString(cgnservice));
                    PRPRLineItem prprlineitem = new PRPRLineItem();
                    prprlineitem.setRowguid(cgnservicedetail.getString("cgnguid"));
                    String lineItemNo = new DB_GetSerialNumber_QY().CreateNewNo("采购需求行项目编号", 4, null, null);
                    prprlineitem.setCurrency("156");// 默认人民币
                    prprlineitem.setLineItemNo(lineItemNo);
                    prprlineitem.setOperatedate(new Date());
                    prprlineitem.setOperateusername(wtrMan.getDisplayName());
                    prprlineitem.setSbr_Unitguid(wtrMan.getOuGuid());
                    prprlineitem.setReqGuid(prpurchasereq.getRowguid());
                    prprlineitem.setProductCommodity(cgnservicedetail.getString("productcommodity"));
                    double totalprice = new BigDecimal(cgnservicedetail.getString("totalprice"))
                            .setScale(6, BigDecimal.ROUND_HALF_EVEN).doubleValue();
                    prprlineitem.setTotalPrice(totalprice);
                    prprlineitem.set("expecteddate", cgnservicedetail.getString("expecteddate"));
                    prprlineitem.set("productdescription", cgnservicedetail.getString("Productdescription"));
                    service.insert(prprlineitem);
                }

                prpurchasereq.set("gonggaocontent",
                        gongGaoContent(false, prpurchasereq.getRowguid(), prpurchasereq.getPRContent(), service));
                prpurchasereq.set("webgonggaocontent",
                        gongGaoContent(true, prpurchasereq.getRowguid(), prpurchasereq.getPRContent(), service));

                service.insert(prpurchasereq);
            }
            // 处理附件
            JSONArray publicAttachment = modifInfo.getJSONObject("publicAttachment").getJSONArray("data");
            ERPCommon.hasEndFix(publicAttachment);
            JSONArray noPublicAttachment = modifInfo.getJSONObject("noPublicAttachment").getJSONArray("data");
            ERPCommon.hasEndFix(noPublicAttachment);
            String clientType = "";
            String sql_attach = "";

            // 公开附件
            if (consignation.getJSONObject("publicAttachment") != null) {
                clientType = "CGYG001";
                sql_attach = "select * from ZHGL_AttachInfo_YeWu where clientguid = ? and clienttype = '" + clientType
                        + "' order by row_id desc";
                erpFrameSysUtil.dealFile(consignation.getJSONObject("publicAttachment"), prpurchasereq.getRowguid(),
                        clientType, sql_attach, isAdd, service);
            }
            // 非公开
            if (consignation.getJSONObject("noPublicAttachment") != null) {
                clientType = "CGYG002";
                sql_attach = "select * from ZHGL_AttachInfo_YeWu where clientguid = ? and clienttype = '" + clientType
                        + "' order by row_id desc";
                erpFrameSysUtil.dealFile(consignation.getJSONObject("noPublicAttachment"), prpurchasereq.getRowguid(),
                        clientType, sql_attach, isAdd, service);
            }
            return "";
        }
        catch (Exception e) {
            e.printStackTrace();
            return "系统操作异常，错误堆栈：" + e.getMessage();
        }
    }

    private String gongGaoContent(boolean iswebcontent, String Rowguid, String PRContent, ZtbCommonDao service) {
        return veloPrintToHtml("pages_ag/template/CaiGouYuGao.vm", iswebcontent, Rowguid, PRContent, service);
    }

    public String veloPrintToHtml(String templateName, boolean iswebcontent, String Rowguid, String PRContent,
            ZtbCommonDao service) {
        VelocityHelper velocityHelper = new VelocityHelper(templateName);
        velocityHelper.put("iswebcontent", iswebcontent);
        velocityHelper.put("prcontent", PRContent);
        List<PRPRLineItem> lst = service.findList("select * from PR_PRLineItem where ReqGuid=? order by row_id",
                PRPRLineItem.class, Rowguid);
        int xh = 1;
        for (PRPRLineItem item : lst) {
            item.set("xh", xh);
            xh++;
        }
        velocityHelper.put("detaillst", lst);
        velocityHelper.put("hasxqqd", lst.size() > 0);
        return velocityHelper.Render();
    }

    public String analysisInfo(boolean isAdd, CgTrustdeedinfo cgTrustdeedinfo, JSONObject modifInfo,
            ZtbCommonDao service) {

        logger.info("isAdd:" + isAdd);
        cgTrustdeedinfo.setSbr_date(new Date());
        boolean isJingJia = StringUtil.isNotBlank(cgTrustdeedinfo.getIsErpJingJia())
                && "1".equals(cgTrustdeedinfo.getIsErpJingJia());

        String trustGuid = cgTrustdeedinfo.getTrustguid();
        JSONObject consignation = modifInfo.getJSONObject("consignation");
        Date now = new Date();
        String jinezhanbi = consignation.getString("jinezhanbi");
        if (StringUtil.isNotBlank(jinezhanbi)) {
            String[] jinezhanbis = jinezhanbi.split(";");
            Double sum = 0.0;
            for (int i = 0; i < jinezhanbis.length; i++) {
                sum += Double.parseDouble(jinezhanbis[i]);
            }
            if (!sum.equals(100.00)) {
                return "主要标的类别采购金额占比合计不为100%，请校验！";
            }
        }

        FrameUser wtrMan = erpFrameSysUtil.getFrameUser(formatString(modifInfo.getString("enterpriseCode")), service);
        if (wtrMan != null && !wtrMan.isEmpty()) {
            if (!isAdd) {// 如果不是新增。当新的委托人与原本的委托人不一致时，重置工作流
                if (!wtrMan.getUserGuid().equals(cgTrustdeedinfo.getSbr_code())) {
                    cgTrustdeedinfo.setPvi_guid("");
                }
            }
            cgTrustdeedinfo.setSbr_name(wtrMan.getDisplayName());// 创建人姓名
            cgTrustdeedinfo.setSbr_code(wtrMan.getUserGuid());// 委托人
            if (StringUtil.isNotBlank(consignation.getString("linkmanMail"))) {
                cgTrustdeedinfo.setWorkemail(formatString(consignation.getString("linkmanMail")));// 联系人EMAIL
            }
            else {
                cgTrustdeedinfo.setWorkemail(wtrMan.getEmail());
            }
            if (StringUtil.isNotBlank(consignation.getString("linkmanTel"))) {
                cgTrustdeedinfo.setSbr_tel(formatString(consignation.getString("linkmanTel")));
                cgTrustdeedinfo.setSbr_moblie(formatString(consignation.getString("linkmanTel")));// 联系方式
            }
            else {
                cgTrustdeedinfo.setSbr_tel(wtrMan.getMobile());
                cgTrustdeedinfo.setSbr_moblie(wtrMan.getMobile());// 联系方式
            }
        }
        FrameOu benDiOu = new FrameOuService9().getOuByOuGuid(wtrMan.getOuGuid());
        if (benDiOu != null) {
            cgTrustdeedinfo.setSbr_unitguid(benDiOu.getOuguid());
            cgTrustdeedinfo.setSbr_unitname(benDiOu.getOuname());// 部门名称
            cgTrustdeedinfo.setWtdwdizhi(benDiOu.getAddress());
            cgTrustdeedinfo.setCaiGouApplyDanWei(benDiOu.getOuname());// 使用单位名称
        }

        FrameOu zhaobiaogongsi = erpFrameSysUtil
                .getFrameOuByXiaQu(formatString(consignation.getString("bidCompanyName")), service);
        if (zhaobiaogongsi != null && !zhaobiaogongsi.isEmpty()) {
            cgTrustdeedinfo.setXiaqucode(zhaobiaogongsi.getStr("xiaqucode"));// 招标公司-攀钢默认都是成都分公司
            cgTrustdeedinfo.setBiddingcompany(zhaobiaogongsi.getOuguid());// 招标公司
            cgTrustdeedinfo.setZbgsdizhi(zhaobiaogongsi.getAddress());
            cgTrustdeedinfo.setDepartmentname(zhaobiaogongsi.getOuname());
            cgTrustdeedinfo.setDepartmentGuid(zhaobiaogongsi.getOuguid());
        }
        FrameUser promanager = erpFrameSysUtil.getFrameUser(formatString(consignation.getString("bidManagerId")),
                service);
        if (promanager == null) {
            promanager = erpFrameSysUtil.getFrameUser(formatString(consignation.getString("bidManagerName")), service);
        }
        if (promanager != null && !promanager.isEmpty()) {
            cgTrustdeedinfo.setPromanagerguid(promanager.getUserGuid());// 项目经理标识
            cgTrustdeedinfo.setPromanager(promanager.getDisplayName());// 项目经理
            cgTrustdeedinfo.setProManagerPhone(promanager.getTelephoneOffice());
            FrameOu bumenOu = new FrameOuService9().getOuByOuGuid(promanager.getOuGuid());
            logger.info("bumenOu=" + bumenOu);
            if (bumenOu != null && !bumenOu.isEmpty()) {
                logger.info("bumenOu.getOuguid()" + bumenOu.getOuguid());
                cgTrustdeedinfo.setDepartmentGuid(bumenOu.getOuguid());
                logger.info("bumenOu.getOuname()" + bumenOu.getOuname());
                cgTrustdeedinfo.setDepartmentname(bumenOu.getOuname());
            }
        }
        AGCgEscategory agCgEscategory = erpFrameSysUtil
                .getCgEscategory(formatString(consignation.getString("supplyClassId")), service);
        String serviceClass = ERPCommon.isServiceClass(agCgEscategory);
        if (StringUtil.isNotBlank(serviceClass)) {
            return serviceClass;
        }
        if (agCgEscategory != null && !agCgEscategory.isEmpty()) {
            cgTrustdeedinfo.setSupplytypeid(agCgEscategory.getCategoryGuid());// 供应分类ID
            SupplyTypeId = agCgEscategory.getCategoryGuid();
            cgTrustdeedinfo.setSupplytype(erpFrameSysUtil.getFullName(agCgEscategory));// 供应分类
            cgTrustdeedinfo.setSupplytypefullpath(agCgEscategory.getCategoryGuid());//
            String purchaseType = "";
            if (agCgEscategory.getCategoryGuid().startsWith("4.") || agCgEscategory.getCategoryGuid().startsWith("5.")
                    || "4".equals(agCgEscategory.getCategoryGuid()) || "5".equals(agCgEscategory.getCategoryGuid())) {
                purchaseType = "A";
            }
            else if (agCgEscategory.getCategoryGuid().startsWith("1.")
                    || agCgEscategory.getCategoryGuid().startsWith("2.")
                    || agCgEscategory.getCategoryGuid().startsWith("3.") || "1".equals(agCgEscategory.getCategoryGuid())
                    || "2".equals(agCgEscategory.getCategoryGuid()) || "3".equals(agCgEscategory.getCategoryGuid())) {
                purchaseType = "M";
            }
            else if (agCgEscategory.getCategoryGuid().startsWith("6.")
                    || "6".equals(agCgEscategory.getCategoryGuid())) {
                purchaseType = "S";
            }
            // codeservice.getItemTextByCodeName("bid_bengangerp_purchaseType",formatString(consignation.getString("purchaseType")))
            cgTrustdeedinfo.setPurchasetype(purchaseType);// 采购类别
        }
        else {
            return "未找到供应分类，无法确定采购类别！" + consignation.getString("supplyClassId");
        }
        String checkItemCode = checkItemCode(cgTrustdeedinfo.getPurchasetype(), modifInfo);
        if (StringUtil.isNotBlank(checkItemCode)) {
            return checkItemCode;
        }
        cgTrustdeedinfo.set("tprule", consignation.getString("tprule"));// 谈判规则
        cgTrustdeedinfo.setIstwostage(consignation.getString("twostage"));// 是否两阶段
        cgTrustdeedinfo.setFromPlatNo(formatString(modifInfo.getString("sysName")));
        cgTrustdeedinfo.setOperatedate(now);
        cgTrustdeedinfo.setPurchasesolutionno(formatString(consignation.getString("purchaseSolutionNo")));// 采购方案号
        cgTrustdeedinfo.setERPTrustDeedNo(formatString(consignation.getString("bidEntrNo")));// ERP招标委托号
        cgTrustdeedinfo.setIslawbid(formatString(consignation.getString("lawBid")));// 法定招标
        cgTrustdeedinfo.setIszixing(formatString(consignation.getString("iszixing")));// 是否自行招标
        cgTrustdeedinfo.set("isjizhong", formatString(consignation.getString("isUnionCG")));// 是否集中采购
        // 校验清单中的计划单价是否有空
        String checkQingDanHasZero = checkQingDanHasZero(cgTrustdeedinfo.getPurchasetype(), modifInfo, isJingJia);
        if (StringUtil.isNotBlank(checkQingDanHasZero)) {
            return checkQingDanHasZero;
        }

        cgTrustdeedinfo.setPurchaseprojectname(formatString(consignation.getString("consignationName")));// 委托单名称

        String isCsErp = new FrameConfigService9().getFrameConfigValue("Bid_ag_是否测试ERP对接");
        if (StringUtil.isNotBlank(isCsErp) && isCsErp.contains(SystemEnum.鞍钢ERP.getValue())) {
            cgTrustdeedinfo.setPurchaseprojectname("【国泰测试】" + formatString(consignation.getString("consignationName")));// 委托单名称
        }
        else {
            cgTrustdeedinfo.setPurchaseprojectname(formatString(consignation.getString("consignationName")));// 委托单名称
        }
        cgTrustdeedinfo.setPurchasemode(codeservice.getItemTextByCodeName("bid_bengangerp_bidType",
                formatString(consignation.getString("bidType"))));// 招标方式

        if ("1".equals(cgTrustdeedinfo.getIslawbid())) {
            cgTrustdeedinfo.setHxrgstimes(3.0);
            cgTrustdeedinfo.setIshxrgsjiaga("1");
            cgTrustdeedinfo.setIsNeedWTSHFile("1");// 是否审核招标文件formatString(consignation.getString("checkBidFile"))
        }
        else {
            cgTrustdeedinfo.setIshxrgsjiaga("0");
            if ("G".equals(cgTrustdeedinfo.getPurchasemode()) || "Q".equals(cgTrustdeedinfo.getPurchasemode())) {
                // 公开招标或邀请招标
                cgTrustdeedinfo.setHxrgstimes(3.0);
            }
            else {
                cgTrustdeedinfo.setHxrgstimes(1.0);
            }
            cgTrustdeedinfo.setIsNeedWTSHFile("0");// 是否审核招标文件
        }

        cgTrustdeedinfo.setVerifymode(codeservice.getItemTextByCodeName("bid_bengangerp_qualifyCheckType",
                formatString(consignation.getString("qualifyCheckType"))));// 资格审核方式
        if ("0".equals(formatString(consignation.getString("qualifyCheckType")))) {// 预审
            cgTrustdeedinfo.setZSBidevaluationmethod("AGHGZ[ZF]");
        }
        cgTrustdeedinfo.setBidevaluationmethod(codeservice.getItemTextByCodeName("bid_bengangerp_evaluationMethod",
                formatString(consignation.getString("evaluationMethod"))));// 评标办法

        cgTrustdeedinfo.setToubiaoyxq(formatString(consignation.getString("toubiaoyxq")));
        cgTrustdeedinfo.setAllowcombination(formatString(consignation.getString("uniteBid")));// 是否允许联合体投标
        cgTrustdeedinfo.setAllowalternate(formatString(consignation.getString("alternativeBidSolution")));// 是否允许递交备选投标方案
        cgTrustdeedinfo.setGysaccessmode(codeservice.getItemTextByCodeName("bid_bengangerp_supplierAdmittance",
                formatString(consignation.getString("supplierAdmittance"))));// 供方准入方式
        cgTrustdeedinfo.setQuotationtype(codeservice.getItemTextByCodeName("bid_bengangerp_priceType",
                formatString(consignation.getString("priceType"))));// 报价类型
        cgTrustdeedinfo.setPricecontrolmode(codeservice.getItemTextByCodeName("bid_bengangerp_priceCtrlType",
                formatString(consignation.getString("priceCtrlType"))));// 价格控制方式
        if (StringUtil.isNotBlank(cgTrustdeedinfo.getPricecontrolmode())
                && cgTrustdeedinfo.getPricecontrolmode().contains("高")
                && StringUtil.isNotBlank(formatString(consignation.getString("maxpriceMethod")))) {
            cgTrustdeedinfo.setSelTypeCeilingPrice("2");// 最高投标限价选择方式默认2：计算方法
            cgTrustdeedinfo.setMaxbidpricemethod(formatString(consignation.getString("maxpriceMethod")));
        }
        else {
            cgTrustdeedinfo.setSelTypeCeilingPrice("1");// 最高投标限价选择方式默认2：计算方法
        }

        cgTrustdeedinfo.setTotalceilingprice(consignation.getDouble("highestPrice"));// 最高限价
        cgTrustdeedinfo.setTotalBidMinPrice(consignation.getDouble("bidBottom"));// 标底
        cgTrustdeedinfo.setGysType(formatString(consignation.getString("gystype")));// 供应商type
        cgTrustdeedinfo.setIsinternational(formatString(consignation.getString("internationalBid")));// 国际招标

        String taxRate = new BigDecimal(formatString(consignation.getString("rate"))).stripTrailingZeros()
                .toPlainString();
        cgTrustdeedinfo.setTaxrate(taxRate);
        cgTrustdeedinfo.setIscombined(formatString(consignation.getString("combination")));// 是否是组合标
        cgTrustdeedinfo.setBiddingattribute(codeservice.getItemTextByCodeName("bid_bengangerp_bidAttr",
                formatString(consignation.getString("bidAttr"))));// 招标属性
        cgTrustdeedinfo.setTotalForecastPrice(consignation.getDouble("referPrice"));// 参考价合计,预测价
        cgTrustdeedinfo.setIsappointexperts("2");
        String string = consignation.getString("IsLastPurchaseSolutionNo");
        if ("1".equals(string)) {
            // 查询时候存在已经流标的标段
            String sql = "select* from cg_yichangbdhistory cy where cy.biaoduanno =? and auditstatus ='3'";
            CgYichangbdhistory find = service.find(sql, CgYichangbdhistory.class,
                    consignation.getString("LastPurchaseSolutionNo"));
            if (find == null) {
                return "重新招标上次采购方案号填写错误";
            }
        }
        if (StringUtil.isNotBlank(modifInfo.getString("error"))) {
            return modifInfo.getString("error");
        }

        cgTrustdeedinfo.setIsLastpurchasesolutionno(consignation.getString("IsLastPurchaseSolutionNo"));
        cgTrustdeedinfo.setLastpurchasesolutionno(consignation.getString("LastPurchaseSolutionNo"));
        cgTrustdeedinfo.set("Isneedjishuattachment", "0");// 需要技术交流形成技术附件 默认否

        cgTrustdeedinfo.setSourceoffunds(codeservice.getItemTextByCodeName("bid_bengangerp_fundSource",
                formatString(consignation.getString("fundSource"))));// 资金来源

        cgTrustdeedinfo.setIspaymentdeviate(formatString(consignation.getString("payTypeFlag")));// 是否允许偏离付款条件
        cgTrustdeedinfo.setPaymentdeviate(formatString(consignation.getString("PaymentDeviate")));
        cgTrustdeedinfo.setIsdeliverydeviate(formatString(consignation.getString("deliveryDateFlag")));// 是否允许偏离工期
        cgTrustdeedinfo.setDeliverydeviate(formatString(consignation.getString("DeliveryDeviate")));
        cgTrustdeedinfo.setIskdjdeviate(formatString(consignation.getString("technicalFlag")));// 是否允许偏离技术指标
        cgTrustdeedinfo.setKdjdeviate(formatString(consignation.getString("KDJDeviate")));
        cgTrustdeedinfo.setIsotherdeviate(formatString(consignation.getString("otherFlag")));// 是否允许偏离其他
        cgTrustdeedinfo.setOtherdeviate(formatString(consignation.getString("OtherDeviate")));

        cgTrustdeedinfo.setInterpricecondition(formatString(consignation.getString("offer")).replace(";", ","));// 报价选项

        StringBuilder sb = new StringBuilder();
        JSONObject quotationextension = consignation.getJSONObject("QuotationExtension");
        if (quotationextension != null) {
            if ("1".equals(quotationextension.getString("singleFlag"))) {
                sb.append("单重,");
            }
            if ("1".equals(quotationextension.getString("ingredientFlag"))) {
                sb.append("材质,");
            }
            if ("1".equals(quotationextension.getString("producerFlag"))) {
                sb.append("产地,");
            }
            if ("1".equals(quotationextension.getString("manufacturerFlag"))) {
                sb.append("制造商,");
            }
            if ("1".equals(quotationextension.getString("lifetimeFlag"))) {
                sb.append("寿命,");
            }
            if ("1".equals(quotationextension.getString("jhDate"))) {
                sb.append("交货期,");
            }
            String trimEnd = StringUtil.trimEnd(sb.toString(), ",");
            cgTrustdeedinfo.setQuotationExtension(trimEnd);
        }

        cgTrustdeedinfo.setWeiTuoBiZhong(codeservice.getItemTextByCodeName("bid_bengangerp_wtCurrency",
                formatString(consignation.getString("wtCurrency"))));// 委托币种
        cgTrustdeedinfo.setIslvyuebzj(formatString(consignation.getString("performanceBond")));// 履约保证金是/否单选框
        cgTrustdeedinfo.setIslvyuebzj(formatString(consignation.getString("performanceBond")));// 履约保证金是/否单选框
        if ("1".equals(consignation.getString("performanceBond"))) {
            if (StringUtil.isNotBlank(consignation.getString("performanceBondText"))) {
                cgTrustdeedinfo.setLvyuebzjdw("3");
                cgTrustdeedinfo.setLvyuebzjsm(consignation.getString("performanceBondText"));// 履约保证金说明
            }
        }
        Log.info(consignation.getString("payType") + consignation.getString("payTypeName"));
        if (StringUtil.isNotBlank(consignation.getString("payTypeName"))) {
            cgTrustdeedinfo.setPaymentmode(consignation.getString("payTypeName"));// 付款方式名
        }
        else {
            String sql = "select structcontent from cg_structinfo where categoryguid=?";
            String find = service.find(sql, String.class, consignation.getString("payType"));
            cgTrustdeedinfo.setPaymentmode(find);// 付款方式名
        }

        cgTrustdeedinfo.set("PaymentModeCode", consignation.getString("payType"));// 付款类型
        cgTrustdeedinfo.setYqzbtype(formatString(consignation.getString("yqzbtype")));// 邀请招标类型
        cgTrustdeedinfo.setBaojiatype(formatString(consignation.getString("Baojiafangshi")));// 报价方式
        cgTrustdeedinfo.setOtherpromode(formatString(consignation.getString("OtherProMode")));// 招标失败转其他采购方式
        /*
         * if ("1".equals(formatString(consignation.getString("OtherProMode")))) {
         * cgTrustdeedinfo.setOtherpromodejj(formatString(consignation.getString(
         * "OtherProMode")));// 1转谈判采购，2不转
         * }
         * else {
         * cgTrustdeedinfo.setOtherpromodejj("2");// 1转谈判采购，2不转
         * }
         */
        // 修改为接口报文中给啥存啥 add by xxb 2025年1月2日16:58:58
        if (StringUtil.isNotBlank(consignation.getString("OtherProMode"))) {
            cgTrustdeedinfo.setOtherpromodejj(formatString(consignation.getString("OtherProMode")));// 竞价失败转其他采购方式
        }
        cgTrustdeedinfo.setIssignagreement(formatString(consignation.getString("IsSignAgreement")));// 是否签订技术协议
        cgTrustdeedinfo.set("chengjiaoyuanze", formatString(consignation.getString("chengjiaoyuanze")));// 中标/成交原则
        // BD，YD是“单” add by xxb 2025年1月2日17:00:01
        if ("BX".equals(cgTrustdeedinfo.getPurchasemode()) || "YX".equals(cgTrustdeedinfo.getPurchasemode())
                || "BD".equals(cgTrustdeedinfo.getPurchasemode()) || "YD".equals(cgTrustdeedinfo.getPurchasemode())) {
            cgTrustdeedinfo.setBaojiatimes("单");// 报价次数
        }
        // BL，YL是“多” add by xxb 2025年1月2日17:00:09
        else if ("B".equals(cgTrustdeedinfo.getPurchasemode()) || "Y".equals(cgTrustdeedinfo.getPurchasemode())
                || "BL".equals(cgTrustdeedinfo.getPurchasemode()) || "YL".equals(cgTrustdeedinfo.getPurchasemode())) {
            cgTrustdeedinfo.setBaojiatimes("多");// 报价次数
        }

        cgTrustdeedinfo.setPromanagement(formatString(consignation.getString("ProManagement")));// 项目管理部门
        cgTrustdeedinfo.setProManagementGuid(formatString(consignation.getString("ProManagementCode")));// 项目管理部门代码
        cgTrustdeedinfo.setTotalplannedprice(consignation.getDouble("bidMoney"));// 计划价(标的额)
        cgTrustdeedinfo.setDeliverydate(formatString(consignation.getString("deliveryDate")));// 交货期
        // cgTrustdeedinfo.set("jjPingShenType",
        // codeservice.getItemTextByCodeName("bid_bengangerp_pingshenfangshi",
        // formatString(consignation.getString("pingshenfangshi"))));
        // 评审方式修改见需求：https://oa.epoint.com.cn/productrelease/cpzt/demandmanagejy/demandbasicinfo_detail?ProcessVersionInstanceGuid=f3b11e72-0e3a-40db-b16a-4d0ded6d6d48&WorkItemGuid=c0adf0ae-ebf2-4905-b6a7-004a40b2bd78&MessageItemGuid=acc9b504-fa3e-426f-9800-7337542df954&messageGuid=4fd3d42b-d4fe-4d4e-8400-934e4c5dbe78
        if ("G".equals(cgTrustdeedinfo.getPurchasemode()) || "Q".equals(cgTrustdeedinfo.getPurchasemode())) {// 公开招标或邀请招标
            cgTrustdeedinfo.set("jjPingShenType", "2".equals(cgTrustdeedinfo.getOtherpromode()) ? "1" : "");
        }
        else if ("BX".equals(cgTrustdeedinfo.getPurchasemode()) || "YX".equals(cgTrustdeedinfo.getPurchasemode())) {// 公开询比，邀请询比
            cgTrustdeedinfo.set("jjPingShenType", "4");
        }
        cgTrustdeedinfo.setShebeixiufuquyu(consignation.getString("Xiufuquyu"));
        cgTrustdeedinfo.setChangwaiyunfeigd(formatString(consignation.getString("fare")));// 运费规定
        cgTrustdeedinfo.setYcydpingbiao(consignation.getString("ycydpingbiao"));
        cgTrustdeedinfo.setYcydplace(consignation.getString("ycydplace"));
        // 需求：个性化字段isnewstandard根据系统参数赋值 add by xxb 2024年12月19日14:18:00
        if ("1".equals(new FrameConfigService9().getFrameConfigValue("是否启用鞍钢集团采购委托标准（2024）"))) {
            cgTrustdeedinfo.set("isnewstandard", "1");
        }
        else {
            cgTrustdeedinfo.set("isnewstandard", "0");
        }
        // plus表
        CgTrustdeedinfoplus cgTrustdeedinfoplus = service.find(CgTrustdeedinfoplus.class, trustGuid);
        if (cgTrustdeedinfoplus == null || cgTrustdeedinfoplus.isEmpty()) {
            cgTrustdeedinfoplus = new CgTrustdeedinfoplus();
            cgTrustdeedinfoplus.setOperatedate(now);
            cgTrustdeedinfoplus.setRowguid(trustGuid);
            cgTrustdeedinfoplus.setTrustguid(trustGuid);

            dealTrustdeedinfoplus(cgTrustdeedinfo, cgTrustdeedinfoplus, modifInfo, service);
            service.insert(cgTrustdeedinfoplus);
        }
        else {
            dealTrustdeedinfoplus(cgTrustdeedinfo, cgTrustdeedinfoplus, modifInfo, service);

            cgTrustdeedinfoplus.setOperatedate(now);
            service.update(cgTrustdeedinfoplus);
        }
        CgTrustdeedinfoplus2 cgTrustdeedinfoplus2 = service.find(CgTrustdeedinfoplus2.class, trustGuid);
        FrameOu frameOu = new FrameOuService9().getOuByOuGuid(cgTrustdeedinfo.getSbr_unitguid());
        logger.info("AnGangDataUtil:" + cgTrustdeedinfo.getSbr_unitguid() + "==" + frameOu + "=="
                + consignation.getString("companyOrgCode"));
        String oucode = "";
        if (frameOu != null) {
            oucode = frameOu.getOucode();
        }
        if (cgTrustdeedinfoplus2 == null || cgTrustdeedinfoplus2.isEmpty()) {
            cgTrustdeedinfoplus2 = new CgTrustdeedinfoplus2();
            cgTrustdeedinfoplus2.setOperatedate(now);
            cgTrustdeedinfoplus2.setRowguid(trustGuid);
            cgTrustdeedinfoplus2.setTrustguid(trustGuid);
            cgTrustdeedinfoplus2.set("oucode",
                    StringUtil.isNotBlank(consignation.getString("companyOrgCode"))
                            ? consignation.getString("companyOrgCode")
                            : oucode);
            cgTrustdeedinfoplus2.set("iszjpingshen", consignation.getString("iszjpingshen"));
            if (StringUtil.isNotBlank(consignation.getString("ispaybzj"))) {
                cgTrustdeedinfoplus2.setIspaybzj(consignation.getString("ispaybzj"));
                cgTrustdeedinfoplus2.setPaybzj(consignation.getDouble("paybzj"));
            }
            else {
                cgTrustdeedinfoplus2.setIspaybzj("0");

            }
            cgTrustdeedinfoplus2.set("tanpanfangshi", consignation.getString("tanpanfangshi"));
            cgTrustdeedinfoplus2.set("tanpanshuxing", consignation.getString("tanpanshuxing"));
            cgTrustdeedinfoplus2.set("cgzzdyzsjcode", consignation.getString("caigouunitcode"));
            cgTrustdeedinfoplus2.set("cgzzdyzsjname", consignation.getString("caigouunitname"));
            cgTrustdeedinfoplus2.set("cgzzdyssgszsjcode", consignation.getString("caigougongsicode"));
            cgTrustdeedinfoplus2.set("cgzzdyssgszsjname", consignation.getString("caigougongsiname"));

            service.insert(cgTrustdeedinfoplus2);
        }
        else {
            if (StringUtil.isNotBlank(consignation.getString("ispaybzj"))) {
                cgTrustdeedinfoplus2.setIspaybzj(consignation.getString("ispaybzj"));
                cgTrustdeedinfoplus2.setPaybzj(consignation.getDouble("paybzj"));
            }
            else {
                cgTrustdeedinfoplus2.setIspaybzj("0");

            }
            cgTrustdeedinfoplus2.set("oucode",
                    StringUtil.isNotBlank(consignation.getString("companyOrgCode"))
                            ? consignation.getString("companyOrgCode")
                            : oucode);
            cgTrustdeedinfoplus2.set("tanpanfangshi", consignation.getString("tanpanfangshi"));
            cgTrustdeedinfoplus2.set("tanpanshuxing", consignation.getString("tanpanshuxing"));
            cgTrustdeedinfoplus2.set("iszjpingshen", consignation.getString("iszjpingshen"));
            cgTrustdeedinfoplus2.set("cgzzdyzsjcode", consignation.getString("caigouunitcode"));
            cgTrustdeedinfoplus2.set("cgzzdyzsjname", consignation.getString("caigouunitname"));
            cgTrustdeedinfoplus2.set("cgzzdyssgszsjcode", consignation.getString("caigougongsicode"));
            cgTrustdeedinfoplus2.set("cgzzdyssgszsjname", consignation.getString("caigougongsiname"));
            cgTrustdeedinfoplus2.setOperatedate(now);
            service.update(cgTrustdeedinfoplus2);
        }

        cgTrustdeedinfo.setTrustStatus("0");// 委托单退回后置为1，这里接收之后置回来
        // 需求清单
        if (!isAdd) {
            logger.info("删除需求清单");
            service.execute("delete from CG_DemandInventory where trustguid=?", trustGuid);
            service.execute("delete from CG_RecommendedSupplier where trustguid=?", trustGuid);
            service.execute("delete from CG_Qualifications where trustguid=?", trustGuid);
            service.execute("delete from cg_trust_mainbidtype where trustguid=?", trustGuid);
        }
        String[] biaodileibies = consignation.getString("biaodileibie").split(";");
        String[] jinezhanbis = consignation.getString("jinezhanbi").split(";");
        for (int i = 0; i < biaodileibies.length; i++) {
            if (StringUtil.isNotBlank(biaodileibies[i])) {
                Record record = new Record();
                record.set("rowguid", UUID.randomUUID().toString());
                record.setPrimaryKeys("rowguid");
                record.set("trustguid", trustGuid);
                record.setSql_TableName("cg_trust_mainbidtype");
                record.set("maintypecode", biaodileibies[i]);
                record.set("proportion", jinezhanbis[i]);
                service.insert(record);
            }
        }
        JSONArray qualifications = modifInfo.getJSONArray("qualifications");
        if (qualifications != null) {
            for (Object object : qualifications) {
                JSONObject qualification = (JSONObject) object;
                CgQualifications ziZhi = new CgQualifications();
                ziZhi.setRowguid(UUID.randomUUID().toString());
                ziZhi.setQualificationno(formatString(qualification.getString("QualificationNo")));// 资质编号
                ziZhi.setQualificationname(formatString(qualification.getString("QualificationName")));// 资质名称
                ziZhi.setQualificationcontent(formatString(qualification.getString("QualificationContent")));// 资质描述
                ziZhi.setQualificationremarks(formatString(qualification.getString("QualificationRemarks")));// 资质备注
                ziZhi.setTrustguid(trustGuid);
                ziZhi.setGystype(StringUtil.isBlank(qualification.getString("gystype")) ? "1"
                        : qualification.getString("gystype"));
                ziZhi.setOperatedate(now);
                service.insert(ziZhi);
            }
        }
        // 获取是否推荐供应商，为空或者为 1 则为接受供应商IsRecSupplier
        String IsRecSupplier = modifInfo.getString("IsRecSupplier");

        if (StringUtil.isBlank(IsRecSupplier) || "1".equals(IsRecSupplier)) {
            cgTrustdeedinfo.setIsrecsupplier("1");
            insertQingDan(cgTrustdeedinfo.getPurchasetype(), cgTrustdeedinfo.getTrustguid(), modifInfo, service);
        }

        if ("AGZHPGF[ZF]".equals(cgTrustdeedinfo.getBidevaluationmethod())) {
            insertXiangXiPingShen(cgTrustdeedinfo, modifInfo, service);
        }

        // 推荐供应商
        JSONArray cgnSuppliers = modifInfo.getJSONArray("cgnSuppliers");
        cgTrustdeedinfo.setIsrecsupplier("0");// 先默认否
        if (cgnSuppliers != null) {
            for (Object o : cgnSuppliers) {
                JSONObject cgnSupplier = (JSONObject) o;
                String gysmsg = insertGys(cgnSupplier, trustGuid, service);
                if (StringUtil.isNotBlank(gysmsg)) {
                    return gysmsg;
                }
                cgTrustdeedinfo.setIsrecsupplier("1");
            }
        }
        String clientType = "";
        String sql_attach = "";
        // 公开附件
        if (consignation.getJSONObject("publicAttachment") != null) {
            clientType = "AG003";
            sql_attach = "select * from ZHGL_AttachInfo_YeWu where clientguid = ? and clienttype = '" + clientType
                    + "' order by row_id desc";
            erpFrameSysUtil.dealFile(consignation.getJSONObject("publicAttachment"), trustGuid, clientType, sql_attach,
                    isAdd, service);
            if(consignation.getJSONObject("publicAttachment").containsKey("num")){
                String publicAttatchmentNum = consignation.getJSONObject("publicAttachment").getString("num");
                cgTrustdeedinfo.set("publicAttatchmentNum",publicAttatchmentNum);
                service.update(cgTrustdeedinfo);
            }
        }
        // 非公开
        if (consignation.getJSONObject("noPublicAttachment") != null) {
            clientType = "AG004";
            sql_attach = "select * from ZHGL_AttachInfo_YeWu where clientguid = ? and clienttype = '" + clientType
                    + "' order by row_id desc";
            erpFrameSysUtil.dealFile(consignation.getJSONObject("noPublicAttachment"), trustGuid, clientType,
                    sql_attach, isAdd, service);
            if(consignation.getJSONObject("noPublicAttachment").containsKey("num")){
                String nopublicAttatchmentNum = consignation.getJSONObject("noPublicAttachment").getString("num");
                cgTrustdeedinfo.set("nopublicAttatchmentNum",nopublicAttatchmentNum);
                service.update(cgTrustdeedinfo);
            }
        }
        // 标底
        if (consignation.getJSONObject("bidBottomAttachment") != null) {
            clientType = "AG007";
            sql_attach = "select * from ZHGL_AttachInfo_YeWu where clientguid = ? and clienttype = '" + clientType
                    + "' order by row_id desc";
            erpFrameSysUtil.dealFile(consignation.getJSONObject("bidBottomAttachment"), trustGuid, clientType,
                    sql_attach, isAdd, service);
        }
        // 非法定招标情况说明附件
        if (consignation.getJSONObject("islawbidAttachment") != null) {
            clientType = "AG013";
            sql_attach = "select * from ZHGL_AttachInfo_YeWu where clientguid = ? and clienttype = 'AG013' order by row_id desc";
            erpFrameSysUtil.dealFile(consignation.getJSONObject("bidBottomAttachment"), trustGuid, clientType,
                    sql_attach, isAdd, service);
        }
        // 鞍钢也要进行自动提交
        if (StringUtil.isBlank(cgTrustdeedinfo.getTrustdeedno())) {
            String trustDeedNo = new DB_Trustdeed().createTrustNo(cgTrustdeedinfo);
            cgTrustdeedinfo.setTrustdeedno(trustDeedNo);
        }
        if (isJingJia) {// 是ERP竞价类的对接才会走这里
            cgTrustdeedinfo
                    .setBidDocSaleBegin(formatDateString(modifInfo.getString("bidDocSaleBegin"), "yyyyMMddHHmmss"));
            cgTrustdeedinfo.setBidDocSaleEnd(formatDateString(modifInfo.getString("bidDocSaleEnd"), "yyyyMMddHHmmss"));
            cgTrustdeedinfo.setTenderDocSubmitEnd(
                    formatDateString(modifInfo.getString("tenderDocSubmitEnd"), "yyyyMMddHHmmss"));
            cgTrustdeedinfo.setTenderDocQuestionEnd(
                    formatDateString(modifInfo.getString("tenderDocQuestionEnd"), "yyyyMMddHHmmss"));
            if (StringUtil.isBlank(cgTrustdeedinfo.getTrustdeedno())) {
                String trustDeedNo = new DB_Trustdeed().createTrustNo(cgTrustdeedinfo);
                cgTrustdeedinfo.setTrustdeedno(trustDeedNo);
            }
            cgTrustdeedinfo.setShr_date(new Date());
            String jingJiaMsg = analysisMoreJingJiaInfo(cgTrustdeedinfo, cgTrustdeedinfoplus, modifInfo, service);
            if (StringUtil.isNotBlank(jingJiaMsg)) {
                return jingJiaMsg;
            }
        }

        // 竞价类的接口数据合并

        codeItemsService9 = new CodeItemsService9();
        JqxtTrustProjectInfo dataBeanJqxt = null;
        // 需求：增加公开多轮谈判 BL 邀请多轮谈判 YL add by xxb 2025年1月7日21:08:13
        if ("B".equals(cgTrustdeedinfo.getPurchasemode()) || "Y".equals(cgTrustdeedinfo.getPurchasemode())
                || "BL".equals(cgTrustdeedinfo.getPurchasemode()) || "YL".equals(cgTrustdeedinfo.getPurchasemode())) {
            dataBeanJqxt = service.find(JqxtTrustProjectInfo.class, cgTrustdeedinfo.getRowguid());
            if (dataBeanJqxt == null || dataBeanJqxt.isEmpty()) {
                dataBeanJqxt = new JqxtTrustProjectInfo();
                dataBeanJqxt.setRowguid(cgTrustdeedinfo.getRowguid());
                dataBeanJqxt.setTrustguid(cgTrustdeedinfo.getRowguid());
                dataBeanJqxt.setJingjiafangshi(codeItemsService9.getItemValueByCodeName("erp竞价报价方式",
                        consignation.getString("jingjiafangshi")));
                dataBeanJqxt.setCurrencyunit(consignation.getString("currencyunit"));
                dataBeanJqxt.setJingjiatype(consignation.getString("jingjiatype"));
                if (StringUtil.isNotBlank(consignation.getString("jingjiastep"))) {
                    dataBeanJqxt.setJingjiastep(Double.parseDouble(consignation.getString("jingjiastep")));
                }
                else {
                    dataBeanJqxt.setJingjiastep(0.0);
                }
                dataBeanJqxt.setAllowfromprice(consignation.getString("allowfromprice"));
                dataBeanJqxt.setProjectremark(consignation.getString("projectremark"));
                // dataBeanJqxt.setJingjiafangshi("1");// 默认加价
                dataBeanJqxt.setPrecision(6);// 金额精度默认6
                dataBeanJqxt.setMaxallow(consignation.getString("allowfromprice"));// 默认允许
                dataBeanJqxt.setBiaodino("委托编号");// 默认允许
                dataBeanJqxt.setProjectJiaoYiType("QY"); // 默认企业类型
                dataBeanJqxt.setOperatedate(new Date());
                dataBeanJqxt.setBiaodino(cgTrustdeedinfo.getTrustdeedno());
                // 默认起始价，加价默认为-99999999999，减价默认为99999999999
                if ("1".equals(dataBeanJqxt.getJingjiafangshi())) {
                    dataBeanJqxt.setFromprice(Double.valueOf("-99999999999"));
                }
                else if ("-1".equals(dataBeanJqxt.getJingjiafangshi())) {
                    dataBeanJqxt.setFromprice(Double.valueOf("99999999999"));
                }
                service.insert(dataBeanJqxt);
            }
            else {
                dataBeanJqxt.set("iszjpingshen", consignation.getString("iszjpingshen"));
                dataBeanJqxt.setJingjiafangshi(codeItemsService9.getItemValueByCodeName("erp竞价报价方式",
                        consignation.getString("jingjiafangshi")));
                dataBeanJqxt.setCurrencyunit(consignation.getString("currencyunit"));
                dataBeanJqxt.setJingjiatype(consignation.getString("jingjiatype"));
                if (StringUtil.isNotBlank(consignation.getString("jingjiastep"))) {
                    dataBeanJqxt.setJingjiastep(Double.parseDouble(consignation.getString("jingjiastep")));
                }
                else {
                    dataBeanJqxt.setJingjiastep(0.0);
                }
                dataBeanJqxt.setAllowfromprice(consignation.getString("allowfromprice"));
                dataBeanJqxt.setProjectremark(consignation.getString("projectremark"));
                // dataBeanJqxt.setJingjiafangshi("1");// 默认加价
                dataBeanJqxt.setPrecision(6);// 金额精度默认6
                dataBeanJqxt.setMaxallow("1");// 默认允许
                dataBeanJqxt.setBiaodino("委托编号");// 默认允许
                dataBeanJqxt.setProjectJiaoYiType("QY"); // 默认企业类型
                dataBeanJqxt.setOperatedate(new Date());
                dataBeanJqxt.setBiaodino(cgTrustdeedinfo.getTrustdeedno());
                // 默认起始价，加价默认为-99999999999，减价默认为99999999999
                if ("1".equals(dataBeanJqxt.getJingjiafangshi())) {
                    dataBeanJqxt.setFromprice(Double.valueOf("-99999999999"));
                }
                else if ("-1".equals(dataBeanJqxt.getJingjiafangshi())) {
                    dataBeanJqxt.setFromprice(Double.valueOf("99999999999"));
                }
                service.update(dataBeanJqxt);
            }
        }
        return "";
    }

    public String analysisMoreJingJiaInfo(CgTrustdeedinfo cgTrustdeedinfo, CgTrustdeedinfoplus cgTrustdeedinfoplus,
            JSONObject modifInfo, ZtbCommonDao service) {
        JqxtTrustProjectInfo dataBeanJqxt = null;
        if (("B".equals(cgTrustdeedinfo.getPurchasemode()) || "Y".equals(cgTrustdeedinfo.getPurchasemode())
                || "BX".equals(cgTrustdeedinfo.getPurchasemode()) || "YX".equals(cgTrustdeedinfo.getPurchasemode()))
                && "多".equals(cgTrustdeedinfo.getBaojiatimes())) {
            dataBeanJqxt = service.find(JqxtTrustProjectInfo.class, cgTrustdeedinfo.getRowguid());
            if (dataBeanJqxt == null || dataBeanJqxt.isEmpty()) {
                dataBeanJqxt = new JqxtTrustProjectInfo();
                dataBeanJqxt.setRowguid(cgTrustdeedinfo.getRowguid());
                dataBeanJqxt.setTrustguid(cgTrustdeedinfo.getRowguid());
                // dataBeanJqxt.setJingjiafangshi("1");// 默认加价
                dataBeanJqxt.setPrecision(6);// 金额精度默认6
                dataBeanJqxt.setCurrencyunit("1");// 默认元
                dataBeanJqxt.setJingjiatype("3");// 默认无要求
                dataBeanJqxt.setAllowfromprice("1");// 默认允许
                dataBeanJqxt.setMaxallow("1");// 默认允许
                dataBeanJqxt.setBiaodino("委托编号");// 默认允许
                dataBeanJqxt.setProjectJiaoYiType("QY"); // 默认企业类型
                dataBeanJqxt.setOperatedate(new Date());
                dataBeanJqxt.setBiaodino(cgTrustdeedinfo.getTrustdeedno());
                service.insert(dataBeanJqxt);
            }
            else {
                dataBeanJqxt.setBiaodino(cgTrustdeedinfo.getTrustdeedno());
                service.update(dataBeanJqxt);
            }
        }
        // // 多轮竞价设置的评标办法是独立的 不走评标系统 不去同步评标办法模板 add by zhujy 2022年9月30日14:52:37
        // if (!(("B".equals(cgTrustdeedinfo.getPurchasemode()) ||
        // "Y".equals(cgTrustdeedinfo.getPurchasemode())
        // || "BX".equals(cgTrustdeedinfo.getPurchasemode()) ||
        // "YX".equals(cgTrustdeedinfo.getPurchasemode()))
        // && "多".equals(cgTrustdeedinfo.getBaojiatimes()))) {
        // // 自动同步评审办法
        // // 多伦竞价无需同步 20221008
        // String msgString = autoaddTerm(cgTrustdeedinfo, service);
        // if (StringUtil.isNotBlank(msgString)) {
        // return msgString;
        // }
        // }
        //
        // // 竞价类的不用评分点
        // logger.info("btnEnd计时3：" + EpointDateUtil.convertDate2String(new
        // Date()));
        // if ("1".equals(cgTrustdeedinfo.getVerifymode())) {
        // addOtherTerm("ZS_" + cgTrustdeedinfo.getRowguid(), cgTrustdeedinfo,
        // cgTrustdeedinfoplus, service);
        // }
        // addOtherTerm(cgTrustdeedinfo.getRowguid(), cgTrustdeedinfo,
        // cgTrustdeedinfoplus, service);
        //
        // 各评分点与委托单内容相联动
        // dealTerm(cgTrustdeedinfo, cgTrustdeedinfoplus, service);
        logger.info("btnEnd计时4：" + EpointDateUtil.convertDate2String(new Date()));

        String creatJJProMsg = ERPFrameSysUtil.autoCreateJJPurchasePorject(cgTrustdeedinfo, cgTrustdeedinfoplus,
                service);
        if (StringUtil.isNotBlank(creatJJProMsg)) {
            return creatJJProMsg;
        }
        // 招标文件信息从委托单过来，所以在此创建文件
        if ("1".equals(cgTrustdeedinfo.getVerifymode())) {
            String fileMsg = ERPFrameSysUtil.autoCreateJJZiShenfilebeian(cgTrustdeedinfo, modifInfo, service);
            if (StringUtil.isNotBlank(fileMsg)) {
                return fileMsg;
            }
        }
        else {
            String fileMsg = ERPFrameSysUtil.autoCreateJJZhaoBiaofilebeian("1", cgTrustdeedinfo, modifInfo, service);
            if (StringUtil.isNotBlank(fileMsg)) {
                return fileMsg;
            }
        }
        String frameConfig = new FrameConfigService9().getFrameConfigValueByNameWithDefault("Bid_Ag_不需要推送网站的招标方式",
                "Q;Y;YX");
        List<String> lst = Arrays.asList(frameConfig.split(";"));
        // 除公开竞价以外，需要自动创建公告
        if (StringUtil.isNotBlank(cgTrustdeedinfo.getPurchasemode())
                && lst.contains(cgTrustdeedinfo.getPurchasemode())) {// erp竞价类的邀请竞价，不接受竞价公告，需要创建公告
            String creatJJGongGaoMsg = ERPFrameSysUtil.autoCreateJJGonngGao(cgTrustdeedinfo, "", modifInfo, "E1CG23",
                    service);
            if (StringUtil.isNotBlank(creatJJGongGaoMsg)) {
                return creatJJGongGaoMsg;
            }
        }
        return "";
    }

    protected void dealTerm(CgTrustdeedinfo cgTrustdeedinfo, CgTrustdeedinfoplus cgTrustdeedinfoplus,
            ZtbCommonDao service) {
        // 知识库：评审点不与委托单任何选项进行关联，此调整仅针对国际设备
        if ("1".equals(cgTrustdeedinfo.getIsinternational()) && cgTrustdeedinfo.getSupplytypeid().startsWith("3")) {
            return;
        }
        List<CgTerm> listTerm = service.findList("select * from cg_term where clientguid=? and isitem='2'",
                CgTerm.class, cgTrustdeedinfo.getRowguid());
        boolean isDelete = false;
        String needUpdateMiaoShu = "";
        for (CgTerm cgTerm : listTerm) {
            if ("初步评审".equals(cgTerm.getItemname())) {
                isDelete = false;
                needUpdateMiaoShu = "";
                switch (cgTerm.getReqcontent()) {
                    case "联合体投标人":
                        if (!"1".equals(cgTrustdeedinfo.getAllowcombination())) {
                            isDelete = true;
                        }
                        else {
                            needUpdateMiaoShu = "允许联合体投标";
                        }
                        break;
                    case "联合体协议书":
                        if (!"1".equals(cgTrustdeedinfo.getAllowcombination())) {
                            isDelete = true;
                        }
                        else {
                            needUpdateMiaoShu = "提交符合招标文件要求的联合体协议书，明确各方承担连带责任，并明确联合体牵头人";
                        }
                        break;
                    case "投标函签字盖章":
                        needUpdateMiaoShu = "投标函应符合“投标文件格式”的规定，有法定代表人或其委托代理签字或加盖单位公章。由法定代表人（单位负责人）签字的，应附法定代表人（单位负责人）身份证明；由代理人签字的，应附授权委托书；身份证明或授权委托书应符合“投标文件格式”的规定。";
                        break;
                    case "营业执照":
                        if (!service.check_Item_Exist("cg_qualifications",
                                "trustguid=? and QualificationName like '%营业执照%' and ifnull(HeBingGuid,'null')='null' ",
                                cgTrustdeedinfo.getRowguid())) {
                            isDelete = true;
                        }
                        else {
                            needUpdateMiaoShu = "具备有效的营业执照";
                        }
                        break;
                    case "资质要求":
                        String strZiZhi = service.queryString(
                                "select group_concat(QualificationName) from cg_qualifications where trustguid=? and ifnull(HeBingGuid,'null')='null' order by Row_ID",
                                cgTrustdeedinfo.getRowguid());
                        if (StringUtil.isNotBlank(strZiZhi)) {
                            needUpdateMiaoShu = strZiZhi;
                        }
                        else {
                            isDelete = true;
                        }
                        break;
                    case "财务要求":
                        if (!"1".equals(cgTrustdeedinfo.getStr("IsFinancialSituation"))) {
                            isDelete = true;
                        }
                        else {
                            needUpdateMiaoShu = cgTrustdeedinfoplus.getFinancialsituation();
                        }
                        break;
                    case "业绩要求":
                        if (!"1".equals(cgTrustdeedinfo.getStr("IsAchieveRequire"))) {
                            isDelete = true;
                        }
                        else {
                            needUpdateMiaoShu = cgTrustdeedinfoplus.getAchieverequire();
                        }
                        break;
                    case "能力要求":
                        if (!"1".equals(cgTrustdeedinfo.getStr("IsAbilityRequire"))) {
                            isDelete = true;
                        }
                        else {
                            needUpdateMiaoShu = cgTrustdeedinfoplus.getAbilityrequire();
                        }
                        break;
                    case "其他要求":
                        if (!"1".equals(cgTrustdeedinfo.getStr("IsOtherRequires"))) {
                            isDelete = true;
                        }
                        else {
                            needUpdateMiaoShu = cgTrustdeedinfoplus.getOtherrequires();
                        }
                        break;
                    case "质量要求":
                        if (!"1".equals(cgTrustdeedinfo.getStr("IsTechnicalRequirements"))) {
                            isDelete = true;
                        }
                        else {
                            needUpdateMiaoShu = cgTrustdeedinfoplus.getTechnicalrequirements();
                        }
                        break;
                    case "交货期":
                    case "工期":
                    case "服务期":
                        if ("1".equals(cgTrustdeedinfo.getIsdeliverydeviate())) {
                            needUpdateMiaoShu = "允许偏离，" + cgTrustdeedinfo.getDeliverydeviate();
                        }
                        else {
                            if ("A".equals(cgTrustdeedinfo.getPurchasetype())) {
                                needUpdateMiaoShu = "不允许偏离，" + cgTrustdeedinfoplus.getNondays() + "天";
                            }
                            else {
                                needUpdateMiaoShu = "不允许偏离，" + cgTrustdeedinfo.getDeliverydate();
                            }
                        }
                        break;
                    case "工程地点":
                    case "服务地点":
                        if (StringUtil.isNotBlank(cgTrustdeedinfoplus.getNonplace())) {
                            needUpdateMiaoShu = cgTrustdeedinfoplus.getNonplace();
                        }
                        break;
                    case "交货地点":
                        String jiaoHuoPlace = "";
                        List<CgDemandinventory> listDemandinventory = service.findList(
                                "select jiaohuoplace from cg_demandinventory where trustguid=? order by row_id",
                                CgDemandinventory.class, cgTrustdeedinfo.getRowguid());
                        if (listDemandinventory.size() > 1) {
                            jiaoHuoPlace = listDemandinventory.get(0).getJiaohuoplace() + "等";
                        }
                        else if (!listDemandinventory.isEmpty()) {
                            jiaoHuoPlace = listDemandinventory.get(0).getJiaohuoplace();
                        }
                        if (StringUtil.isNotBlank(jiaoHuoPlace)) {
                            needUpdateMiaoShu = jiaoHuoPlace.substring(0, jiaoHuoPlace.length() - 1);
                        }
                        break;
                    case "付款方式":
                        if ("1".equals(cgTrustdeedinfo.getIspaymentdeviate())) {
                            needUpdateMiaoShu = "允许偏离，" + cgTrustdeedinfo.getPaymentdeviate();
                        }
                        else {
                            needUpdateMiaoShu = "不允许偏离，" + cgTrustdeedinfo.getPaymentmode();
                        }
                        break;
                    case "投标有效期":
                        if (StringUtil.isNotBlank(cgTrustdeedinfo.getToubiaoyxq())) {
                            needUpdateMiaoShu = cgTrustdeedinfo.getToubiaoyxq() + "天";
                        }
                        break;
                    case "质量管理体系与措施":
                        needUpdateMiaoShu = "符合国家、地方、招标技术资料及招标人规定现行的有关标准、规范";
                        break;
                    case "安全管理体系与措施":
                    case "环境保护管理体系与措施":
                        needUpdateMiaoShu = "符合国家、地方及招标人规定现行有关制度要求";
                        break;
                    case "工程进度计划与措施":
                        needUpdateMiaoShu = "满足施工项目工程进度要求";
                        break;
                    case "资源配备计划":
                        needUpdateMiaoShu = "满足施工项目建设要求";
                        break;
                    case "工程预算书":
                        needUpdateMiaoShu = "按照需求清单编写工程项目在未来一定时期内的收入和支出情况所做的计划";
                        break;
                }

                if (isDelete) {
                    service.delete(CgTerm.class, cgTerm.getRowguid());
                }
                if (StringUtil.isNotBlank(needUpdateMiaoShu)) {
                    service.execute("update cg_term set miaoshu=? where rowguid=?", needUpdateMiaoShu,
                            cgTerm.getRowguid());
                }
            }
        }

        // 去除没有评分点的评分项
        List<Record> listClear = service.findList(
                "select ItemName,count(ItemName) cnt from cg_term where ClientGuid =? and ItemName is not null group by ItemName",
                Record.class, cgTrustdeedinfo.getRowguid());
        for (Record item : listClear) {
            if (item.getInt("cnt") == 1) {
                service.execute("delete from cg_term where ClientGuid =? and isitem='1' and ItemName=?",
                        cgTrustdeedinfo.getRowguid(), item.getStr("ItemName"));
            }
        }
    }

    // 自动同步
    public String autoaddTerm(CgTrustdeedinfo cgTrustdeedinfo, ZtbCommonDao service) {
        // 请求接口获取数据
        TwoTuple<Integer, List<Record>> ret = new AGZBToolInfoProxy().getpbbfname();
        List<Record> mobanList = ret.second;
        if ("1".equals(cgTrustdeedinfo.getVerifymode())) {
            mobanList = mobanList.stream()
                    .filter(moban -> cgTrustdeedinfo.getZSBidevaluationmethod().equals(moban.getStr("pingbiaomark")))
                    .collect(Collectors.toList());
            // 判断是否已经存在在有效期内未提交的数据
            if (EpointCollectionUtils.isEmpty(mobanList)) {
                return "没有同步到该评标办法模板";
            }
            else {
                ERPFrameSysUtil.addTermByModel(mobanList.get(0).getStr("rowguid"), "ZS_" + cgTrustdeedinfo.getRowguid(),
                        cgTrustdeedinfo, service);
            }
        }
        mobanList = ret.second.stream()
                .filter(moban -> cgTrustdeedinfo.getBidevaluationmethod().equals(moban.getStr("pingbiaomark")))
                .collect(Collectors.toList());
        // 国际标只有货物和服务类，其中只有货物类有国际标的评标办法
        if ("1".equals(cgTrustdeedinfo.getIsinternational()) && "M".equals(cgTrustdeedinfo.getPurchasetype())) {
            mobanList = ret.second.stream()
                    .filter(moban -> cgTrustdeedinfo.getBidevaluationmethod().equals(moban.getStr("pingbiaomark"))
                            && moban.getStr("mark").contains("(GJB)"))
                    .collect(Collectors.toList());
            // 如果是法定的，把询比的和非法定的过滤掉
            if ("1".equals(cgTrustdeedinfo.getIslawbid())) {
                mobanList = mobanList.stream().filter(
                        moban -> !moban.getStr("mark").contains("[XB]") && !moban.getStr("mark").contains("FFD"))
                        .collect(Collectors.toList());
            }
            // 如果不是法定的
            else {
                // 询比的匹配带有[XB]标识的模板
                if ("BX".equals(cgTrustdeedinfo.getPurchasemode()) || "YX".equals(cgTrustdeedinfo.getPurchasemode())) {
                    mobanList = mobanList.stream().filter(moban -> moban.getStr("mark").contains("[XB]"))
                            .collect(Collectors.toList());
                }
                // 否则匹配带有FFD标识的模板
                else {
                    mobanList = mobanList.stream().filter(moban -> moban.getStr("mark").contains("FFD"))
                            .collect(Collectors.toList());
                }
            }
        }
        // 判断是否已经存在在有效期内未提交的数据
        if (EpointCollectionUtils.isEmpty(mobanList)) {
            return "没有同步到该评标办法模板";
        }
        return ERPFrameSysUtil.addTermByModel(mobanList.get(0).getStr("rowguid"), cgTrustdeedinfo.getRowguid(),
                cgTrustdeedinfo, service);
    }

    protected void addOtherTerm(String ClientGuid, CgTrustdeedinfo cgTrustdeedinfo,
            CgTrustdeedinfoplus cgTrustdeedinfoplus, ZtbCommonDao service) {
        String[] chubuarr = {"isTechnicalRequirements", "isBusinessRequirements", "isAchieveRequire",
                "isAbilityRequire", "isFinancialSituation", "isOtherRequires" };
        List<String> chubulist = new ArrayList<String>();
        for (String string : chubuarr) {
            if ("1".equals(cgTrustdeedinfo.getStr(string))) {
                chubulist.add(string);
            }
        }
        // 资质的单独合并资质要求
        List<CgQualifications> zizhilist = service.findList(
                "select QualificationName,gystype from CG_Qualifications where TrustGuid =? and ifnull(HeBingGuid,'null')='null' ",
                CgQualifications.class, cgTrustdeedinfo.getRowguid());
        // 判断有没有初步评审 我已评估不存在性能问题
        String Tbpartition = "初步评审";
        String Itemname = "初步评审";
        if ("1".equals(cgTrustdeedinfo.getVerifymode())) {
            Tbpartition = "审查标准";
            Itemname = "审查标准";
        }
        if ("1".equals(cgTrustdeedinfo.getIsinternational()) && "1".equals(cgTrustdeedinfo.getIslawbid())
                && !"BX".equals(cgTrustdeedinfo.getPurchasemode()) && !"YX".equals(cgTrustdeedinfo.getPurchasemode())) {
            Itemname = "符合性检查";
        }
        AGCgTerm cgTermCB = service.find(
                "select * from CG_term where Tbpartition=? and Itemname=? and clientguid =? and isitem='1'",
                AGCgTerm.class, Tbpartition, Itemname, ClientGuid);
        // 评分项的排序
        String sortPFD = cgTermCB != null ? cgTermCB.getSort() : "";

        List<String> pfxlist = new ArrayList<String>();
        // 判断需不需要处理
        if (EpointCollectionUtils.isNotEmpty(chubulist) || !zizhilist.isEmpty()) {
            pfxlist = chubulist;

            if (cgTermCB == null || cgTermCB.isEmpty()) {
                // 新增评分项
                AGCgTerm pfx = new AGCgTerm();
                ERPFrameSysUtil.initResposeterm(pfx, ClientGuid, cgTrustdeedinfo.getSbr_name());
                // 评分步骤名称
                pfx.setTbpartition(Tbpartition);
                pfx.setLowscore(0.00);
                pfx.setHighscore(100.00);
                // 评委类型
                pfx.setPartitiontype("EP,TP");
                // 评分项名称
                pfx.setItemname(Itemname);
                pfx.setIsitem("1");
                pfx.setRestype("required");
                // 分数模式
                pfx.setReviewway("2");
                // 汇总方式
                pfx.setSummaryway("-1");
                pfx.setPingfenmodel("2");
                pfx.setPfdsummaryway("-1");
                pfx.setSort("0101");
                pfx.setPfxno("04");
                pfx.setQuanzhong(0.00);
                pfx.setFxpassrate("100");
                pfx.setTotalpassrate("51");
                service.insert(pfx);

                sortPFD = pfx.getSort();
            }
            else {
                sortPFD = cgTermCB.getSort();
            }
            // 新增评分点
            for (String string : pfxlist) {
                String reqcontent = "";
                if ("isTechnicalRequirements".equals(string)) {
                    reqcontent = "技术质量要求";
                }
                else if ("isBusinessRequirements".equals(string)) {
                    reqcontent = "商务要求";
                }
                else if ("isAchieveRequire".equals(string)) {
                    reqcontent = "业绩要求";
                }
                else if ("isAbilityRequire".equals(string)) {
                    reqcontent = "能力要求";
                }
                else if ("isFinancialSituation".equals(string)) {
                    reqcontent = "财务状况";
                }
                else if ("isOtherRequires".equals(string)) {
                    reqcontent = "其他要求";
                }

                AGCgTerm pfd = new AGCgTerm();
                ERPFrameSysUtil.initResposeterm(pfd, ClientGuid, cgTrustdeedinfo.getSbr_name());
                // 评分步骤名称
                pfd.setTbpartition(Tbpartition);
                pfd.setLowscore(0.00);
                pfd.setHighscore(100.00);
                // 评分点名称
                pfd.setReqcontent(reqcontent);
                pfd.setMiaoshu(cgTrustdeedinfoplus.getStr(Functions.TrimStart(string, "is")));
                // 附件类型
                pfd.setFieldtype("attach");
                // 是否为必过项
                pfd.setIsmustpass("1");
                pfd.setOrgguid(UUID.randomUUID().toString());
                pfd.setIsitem("2");
                // 评分点汇总模式
                pfd.setPfdsummaryway("-100");
                // 评分项名称
                pfd.setItemname(Itemname);
                // 附件类型
                pfd.setUploadtype("rar,zip,docx,png,txt,pdf,xls,jpg,jpeg,gif,doc,bmp,xlsx");
                pfd.setSort(sortPFD + String.format("%02d",
                        service.queryInt(
                                "select count(1) from cg_term where clientguid=? and isitem='2' and itemname=?",
                                ClientGuid, Itemname) + 1));
                pfd.setPfxno(service.queryString(
                        "select pfxno from cg_term where clientguid=? and isitem='1' and itemname=?", ClientGuid,
                        pfd.getItemname()));
                pfd.setRestype("required");
                // 评分模式
                pfd.setPingfenmodel("1");
                if ("其他要求".equals(reqcontent)) {
                    pfd.setAssessItemCode("GC_Item_OtherRequirements");
                }
                else if ("技术质量要求".equals(reqcontent)) {
                    pfd.setAssessItemCode("GC_Item_TechnicalQualityRequirements");
                }
                service.insert(pfd);
            }
        }

        if (EpointCollectionUtils.isNotEmpty(zizhilist)) {
            int i = 1;
            // 新增评分点
            for (CgQualifications cgQualifications : zizhilist) {
                AGCgTerm pfd = new AGCgTerm();
                ERPFrameSysUtil.initResposeterm(pfd, ClientGuid, cgTrustdeedinfo.getSbr_name());
                // 评分步骤名称
                pfd.setTbpartition(Tbpartition);
                pfd.setLowscore(0.00);
                pfd.setHighscore(100.00);
                // 评分点名称
                pfd.setReqcontent("资质要求-" + cgQualifications.getQualificationname());
                // 附件类型
                pfd.setFieldtype("attach");
                // 是否为必过项
                pfd.setIsmustpass("1");
                pfd.setOrgguid(UUID.randomUUID().toString());
                pfd.setIsitem("2");
                // 评分点汇总模式
                pfd.setPfdsummaryway("-100");
                // 评分项名称
                pfd.setItemname(Itemname);
                // 附件类型
                pfd.setUploadtype("rar,zip,docx,png,txt,pdf,xls,jpg,jpeg,gif,doc,bmp,xlsx");
                pfd.setSort(sortPFD + String.format("%02d",
                        service.queryInt(
                                "select count(1) from cg_term where clientguid=? and isitem='2' and itemname=?",
                                ClientGuid, Itemname) + 1));
                pfd.setPfxno(service.queryString(
                        "select pfxno from cg_term where clientguid=? and isitem='1' and itemname=?", ClientGuid,
                        pfd.getItemname()));
                pfd.setRestype("required");
                // 评分模式
                pfd.setPingfenmodel("1");
                pfd.setIszizhi("1");
                pfd.setGystype(cgQualifications.getGystype());
                if (pfd.getReqcontent().contains("安全生产许可证")) {
                    pfd.setAssessItemCode("GC_Item_SafetyLicense");
                }
                else if (pfd.getReqcontent().contains("营业执照")) {
                    pfd.setAssessItemCode("GC_Item_BusinessLicense");
                }
                else if (pfd.getReqcontent().contains("质量管理体系")) {
                    pfd.setAssessItemCode("GC_Item_QMS");
                }
                else if (pfd.getReqcontent().contains("纳税人资格证明")) {
                    pfd.setAssessItemCode("GC_Item_GTQualification");
                }
                else if (pfd.getReqcontent().contains("环境管理体系")) {
                    pfd.setAssessItemCode("GC_Item_EMS");
                }
                else if (pfd.getReqcontent().contains("职业健康安全管理体系")) {
                    pfd.setAssessItemCode("GC_Item_OHSMS");
                }
                else if (pfd.getReqcontent().contains("危险化学品经营许可证")) {
                    pfd.setAssessItemCode("GC_Item_HazardousChemicalsBL");
                }
                else if (pfd.getReqcontent().contains("道路运输经营许可证")) {
                    pfd.setAssessItemCode("GC_Item_RoadTransportBL");
                }
                else if (pfd.getReqcontent().contains("特种设备制造许可证")) {
                    pfd.setAssessItemCode("GC_Item_SpecialEquipmentML");
                }
                else if (pfd.getReqcontent().contains("危险品经营许可证")) {
                    pfd.setAssessItemCode("GC_Item_DangerousGoodssBL");
                }
                else if (pfd.getReqcontent().contains("强制性产品")) {
                    pfd.setAssessItemCode("GC_Item_ChinaCompulsoryCertification");
                }
                else if (pfd.getReqcontent().contains("特种设备安装改造维修许可证")) {
                    pfd.setAssessItemCode("GC_Item_SpecialEquipmentIMML");
                }
                else if (pfd.getReqcontent().contains("食品经营许可证")) {
                    pfd.setAssessItemCode("GC_Item_FoodBL");
                }
                else {
                    pfd.setAssessItemCode("GC_Item_QualificationLevel-" + (i++));
                }
                service.insert(pfd);
            }
        }

        if (!ClientGuid.startsWith("ZS_")) {
            // 综合评估法处理合并详细评审
            if ("AGZHPGF[ZF]".equals(cgTrustdeedinfo.getBidevaluationmethod())) {
                // 我已评估不存在性能问题
                List<CgTermPre> cgTermPres = service.findList("select * from CG_term_pre where clientguid =?",
                        CgTermPre.class, cgTrustdeedinfo.getRowguid());
                if (EpointCollectionUtils.isNotEmpty(cgTermPres)) {
                    AGCgTerm pfbz = null;
                    if (!service.check_Item_Exist("CG_term", "clientguid=? and isitem='0' and TBPartition='详细评审'",
                            ClientGuid)) {
                        // 新增评分步骤
                        pfbz = new AGCgTerm();
                        ERPFrameSysUtil.initResposeterm(pfbz, ClientGuid, cgTrustdeedinfo.getSbr_name());
                        // 评分步骤名称
                        pfbz.setTbpartition("详细评审");
                        pfbz.setIsitem("0");
                        // 排序
                        pfbz.setSort(new TermCodeService(service).getSort(ClientGuid, false, "01"));// 由于是新增步骤，没有父节点，所以此处可以写死01，
                        // 评审步骤
                        pfbz.setPsbz(Integer.parseInt(pfbz.getSort()));
                        // 废标节点
                        pfbz.setIsfeibiao("0");
                        pfbz.setRestype("optional");
                        service.insert(pfbz);
                    }
                    else {
                        String pfbzguid = service.queryString(
                                "select rowguid from CG_term where clientguid=? and isitem='0' and TBPartition='详细评审'",
                                ClientGuid);
                        pfbz = service.find(AGCgTerm.class, pfbzguid);
                    }

                    // 获取评分项的类型
                    List<CodeItems> codeItems = new CodeItemsService9().getCodeItemsListByName("评分项类型");
                    List<CgTermPre> itemPres = null;
                    for (CodeItems codeItem : codeItems) {
                        // 根据评分项类型筛选有没有数据
                        itemPres = cgTermPres.stream()
                                .filter(cgTermPre -> codeItem.getItemValue().equals(cgTermPre.getItemname()))
                                .collect(Collectors.toList());
                        // 判断是否有该评分类型的数据
                        if (EpointCollectionUtils.isNotEmpty(itemPres)) {
                            List<AGCgTerm> list_xiangxipfx = service.findList(
                                    "select * from CG_term where clientguid=? and isitem='1' and TBPartition='详细评审'",
                                    AGCgTerm.class, ClientGuid);
                            list_xiangxipfx = list_xiangxipfx.stream()
                                    .filter(xiangxipfx -> codeItem.getItemValue().contains(xiangxipfx.getItemname()))
                                    .collect(Collectors.toList());
                            AGCgTerm pfx = null;
                            if (list_xiangxipfx.isEmpty()) {
                                // 新增评分项
                                pfx = new AGCgTerm();
                                ERPFrameSysUtil.initResposeterm(pfx, ClientGuid, cgTrustdeedinfo.getSbr_name());
                                // 评分步骤名称
                                pfx.setTbpartition(pfbz.getTbpartition());
                                // 废标节点
                                pfx.setIsfeibiao(pfbz.getIsfeibiao());
                                pfx.setLowscore(itemPres.get(0).getLowscore());
                                pfx.setHighscore(itemPres.get(0).getHighscore());
                                // 评委类型
                                pfx.setPartitiontype(itemPres.get(0).getPartitiontype());
                                // 评分项名称
                                pfx.setItemname(codeItem.getItemValue());
                                pfx.setIsitem("1");
                                pfx.setRestype("optional");
                                // 分数模式
                                pfx.setReviewway(itemPres.get(0).getReviewway());
                                // 汇总方式
                                pfx.setSummaryway(itemPres.get(0).getSummaryway());
                                pfx.setSort(new TermCodeService(service).getSort(cgTrustdeedinfo.getRowguid(), true,
                                        pfbz.getSort()));
                                String lastpfxno = service.queryString(
                                        "select PFXNo from cg_term where PFXNo is not null and ClientGuid =? order by PFXNo  desc limit 1",
                                        ClientGuid);
                                pfx.setPfxno(String.format("%02d", Integer.parseInt(lastpfxno) + 1));
                                pfx.setQuanzhong(0.00);
                                service.insert(pfx);
                            }
                            else {
                                pfx = service.find(AGCgTerm.class, list_xiangxipfx.get(0).getRowguid());
                            }

                            // 新增评分点
                            for (CgTermPre cgTermPre : itemPres) {
                                AGCgTerm pfd = new AGCgTerm();
                                ERPFrameSysUtil.initResposeterm(pfd, ClientGuid, cgTrustdeedinfo.getSbr_name());
                                // 评分步骤名称
                                pfd.setTbpartition(pfbz.getTbpartition());
                                // 废标节点
                                pfd.setIsfeibiao(pfbz.getIsfeibiao());
                                pfd.setLowscore(cgTermPre.getLowscore());
                                pfd.setHighscore(cgTermPre.getHighscore());
                                // 评分点名称
                                pfd.setReqcontent(cgTermPre.getReqcontent());
                                // 附件类型
                                pfd.setFieldtype(cgTermPre.getFieldtype());
                                // 是否为必过项
                                pfd.setIsmustpass("1");
                                pfd.setOrgguid(UUID.randomUUID().toString());
                                pfd.setIsitem("2");
                                pfd.setIsjiagefen("0");
                                // 评分点汇总模式
                                pfd.setPfdsummaryway(cgTermPre.getPfdsummaryway());
                                // 评分项名称
                                pfd.setItemname(pfx.getItemname());
                                // 附件类型
                                pfd.setUploadtype("rar,zip,docx,png,txt,pdf,xls,jpg,jpeg,gif,doc,bmp,xlsx");
                                pfd.setSort(new TermCodeService(service).getSort(ClientGuid, true, pfx.getSort()));
                                pfd.setPfxno(pfx.getPfxno());
                                pfd.setRestype("optional");
                                // 评分模式
                                pfd.setPingfenmodel(cgTermPre.getPingfenmodel());
                                // 评审标准
                                pfd.setMiaoshu(cgTermPre.getPfdremark());
                                service.insert(pfd);
                            }
                        }
                    }
                }
            }
        }
    }

    /**
     * 招标委托方案订正信息
     */
    public String gengZhengCgTrustdeed(CgTrustdeedchangeinfo cgTrustdeedchangeinfo, CgTrustdeedinfo cgTrustdeedinfo,
            JSONObject modifInfo, ZtbCommonDao service) {
        AGCgBiaoduaninfo bdInfo = service.find(AGCgBiaoduaninfo.class, cgTrustdeedinfo.getBiaoDuanGuid());
        String gzggRowguid = UUID.randomUUID().toString();
        cgTrustdeedchangeinfo.setSbrdate(new Date());
        cgTrustdeedchangeinfo.setRowguid(gzggRowguid);
        cgTrustdeedchangeinfo.setSbrcode(formatString(modifInfo.getString("enterpriseCode")));
        cgTrustdeedchangeinfo.setSbrname(formatString(modifInfo.getString("enterpriseName")));
        cgTrustdeedchangeinfo.setTitle(formatString(modifInfo.getString("title")));
        cgTrustdeedchangeinfo.setGengzhengcontent(formatString(modifInfo.getString("noticeContent")));
        cgTrustdeedchangeinfo.setSbrunitguid("companyCode");
        cgTrustdeedchangeinfo.setSbrunitname("");
        cgTrustdeedchangeinfo.setSbridcard("");
        cgTrustdeedchangeinfo.setSbrtel("");
        cgTrustdeedchangeinfo.setSbrmoblie("");
        cgTrustdeedchangeinfo.setBiaoduanguid(bdInfo.getBiaoduanguid());
        cgTrustdeedchangeinfo.setBiaoduanname(bdInfo.getBiaoduanname());
        cgTrustdeedchangeinfo.setBiaoduanno(bdInfo.getBiaoduanno());
        cgTrustdeedchangeinfo.setTrustdeedguid(cgTrustdeedinfo.getRowguid());
        cgTrustdeedchangeinfo.setWeituodanweiguid(cgTrustdeedinfo.getSbr_unitguid());
        cgTrustdeedchangeinfo.setWeituodanweiname(cgTrustdeedinfo.getSbr_unitname());
        cgTrustdeedchangeinfo.setWeituoren(cgTrustdeedinfo.getSbr_name());
        cgTrustdeedchangeinfo.setZhaobiaodanwei(cgTrustdeedinfo.getXiaqucode());
        cgTrustdeedchangeinfo.setPromanager(cgTrustdeedinfo.getPromanager());
        cgTrustdeedchangeinfo.setPromanagerguid(cgTrustdeedinfo.getPromanagerguid());
        cgTrustdeedchangeinfo.setZishentype(cgTrustdeedinfo.getVerifymode());
        if (modifInfo.getJSONObject("files") != null) {
            if (modifInfo.getJSONObject("files").getJSONArray("data") != null) {
                for (Object o : modifInfo.getJSONObject("files").getJSONArray("data")) {
                    JSONObject data = (JSONObject) o;
                    erpFrameSysUtil.downloadFile(data, cgTrustdeedchangeinfo.getRowguid(), "WTSBG001", service);
                }
            }
        }
        return "";
    }

    /**
     * 资格预审公告_竞价采购公告接口
     */
    public String zsGGorJJCgGG(CgTrustdeedinfo cgTrustdeedinfo, JSONObject modifInfo, ZtbCommonDao service) {
        CgTrustdeedinfoplus cgTrustdeedinfoplus = service.find(CgTrustdeedinfoplus.class,
                cgTrustdeedinfo.getTrustguid());
        String creatJJGongGaoMsg = ERPFrameSysUtil.autoCreateJJGonngGao(cgTrustdeedinfo, "", modifInfo, "E1CG21",
                service);
        if (StringUtil.isNotBlank(creatJJGongGaoMsg)) {
            return creatJJGongGaoMsg;
        }
        return "";
    }

    /**
     * 公开竞价资格预审结束后发标接口(二次发标)信息（E1CG25）
     */
    public String zsGGTwoCgGG(CgTrustdeedinfo cgTrustdeedinfo, JSONObject modifInfo, ZtbCommonDao service) {
        CgTrustdeedinfoplus cgTrustdeedinfoplus = service.find(CgTrustdeedinfoplus.class,
                cgTrustdeedinfo.getTrustguid());
        // 预审项目招标阶段发标的，在此创建文件
        String fileMsg = ERPFrameSysUtil.autoCreateJJZhaoBiaofilebeian("2", cgTrustdeedinfo, modifInfo, service);
        if (StringUtil.isNotBlank(fileMsg)) {
            return fileMsg;
        }
        String dwMsg = changeBDinfoAndDw(modifInfo, cgTrustdeedinfo, service);
        if (StringUtil.isNotBlank(dwMsg)) {
            return dwMsg;
        }
        String creatJJGongGaoMsg = ERPFrameSysUtil.autoCreateJJGonngGao(cgTrustdeedinfo, "2", modifInfo, "E1CG25",
                service);
        if (StringUtil.isNotBlank(creatJJGongGaoMsg)) {
            return creatJJGongGaoMsg;
        }
        return "";
    }

    private void insertXiangXiPingShen(CgTrustdeedinfo cgTrustdeedinfo, JSONObject modifInfo, ZtbCommonDao service) {
        // TODO Auto-generated method stub
        String sql = "delete from cg_term_pre where Clientguid=? ";
        service.execute(sql, cgTrustdeedinfo.getTrustguid());
        JSONArray jsonArray = modifInfo.getJSONArray("reviews");
        String startPbjzjcalmethod = "";
        String startTbbjpclcalmethod = "";
        HashMap<String, Double> hashMap = new HashMap<String, Double>();
        hashMap.put("商务评审标准", 0.0);
        hashMap.put("技术评审标准", 0.0);
        hashMap.put("投标报价评审因素", 0.0);
        hashMap.put("其他因素评审标准", 0.0);
        for (Object object : jsonArray) {
            JSONObject jo = (JSONObject) object;
            String itemname = jo.getString("itemname");
            String reqcontent = jo.getString("reqcontent");
            Double lowscore = jo.getDouble("lowscore");
            Double highscore = jo.getDouble("highscore");
            String pfdremark = jo.getString("pfdremark");
            String pbjzjcalmethod = jo.getString("pbjzjcalmethod");
            String tbbjpclcalmethod = jo.getString("tbbjpclcalmethod");

            CgTermPre cgTermPre = new CgTermPre();
            cgTermPre.setRowguid(UUID.randomUUID().toString());
            // 分数模式
            cgTermPre.setReviewway("1");
            // 汇总方式
            cgTermPre.setSummaryway("-1");
            // 评委类型
            cgTermPre.setPartitiontype("EP,TP");
            // 响应类型
            cgTermPre.setRestype("optional");
            // 响应字段类型
            cgTermPre.setFieldtype("attach");
            // 评分模式
            cgTermPre.setPingfenmodel("2");
            // 评分点汇总方式
            cgTermPre.setPfdsummaryway("-100");
            cgTermPre.setItemname(getItemKey(itemname));
            cgTermPre.setReqcontent(reqcontent);
            cgTermPre.setLowscore(lowscore);
            cgTermPre.setHighscore(highscore);
            hashMap.put(getItemKey(itemname), hashMap.get(getItemKey(itemname)) + highscore);
            cgTermPre.setPfdremark(pfdremark);
            cgTermPre.setClientguid(cgTrustdeedinfo.getRowguid());
            cgTrustdeedinfo.set("pbjzjcalmethod", pbjzjcalmethod);
            cgTrustdeedinfo.set("tbbjpclcalmethod", tbbjpclcalmethod);
            service.insert(cgTermPre);
        }
        cgTrustdeedinfo.set("shangwupart", hashMap.get("商务评审标准"));
        cgTrustdeedinfo.set("jishupart", hashMap.get("技术评审标准"));
        cgTrustdeedinfo.set("toubiaobaojiapart", hashMap.get("投标报价评审因素"));
        cgTrustdeedinfo.set("otherpart", hashMap.get("其他因素评审标准"));

    }

    private String getItemKey(String itemName) {
        String key = null;
        switch (itemName) {
            case "shangwupart":
                key = "商务评审标准";
                break;
            case "jishupart":
                key = "技术评审标准";
                break;
            case "toubiaobaojiapart":
                key = "投标报价评审因素";
                break;
            case "otherpart":
                key = "其他因素评审标准";
                break;
            default:
                break;
        }
        return key;
    }

    /**
     * 资格预审公告_竞价采购公告 变更接口
     */
    public String zsGGorJJCgGGBG(CgTrustdeedinfo cgTrustdeedinfo, JSONObject modifInfo, ZtbCommonDao service) {
        AGCgBiaoduaninfo bdInfo = service.find(AGCgBiaoduaninfo.class, cgTrustdeedinfo.getBiaoDuanGuid());
        if ("1".equals(cgTrustdeedinfo.getVerifymode())) {// 预审项目
            if (StringUtil.isBlank(bdInfo.getZishenkaibiaodate())) {
                return "标段资审开标时间异常！";
            }
            // 预审阶段：CG_ZhaoBiaoGGInfo cg_zbggandbd 'ZS_'+biaoduanguid
            // CG_ZiShenWJ.rowguid=CG_ZiShengFileAndBD.beianguid
            // 澄清（变更）：CG_ZSChengQing.beianguid=CG_ZiShengFileAndBD.beianguid
            if (StringUtil.isBlank(bdInfo.getKaibiaodate()) || bdInfo.getZishenkaibiaodate().after(new Date())) {
                String sql_ggandbd = "select * from CG_ZBGGANDBD where biaoduanguid=?";
                String sql_gg = "select * from CG_zhaobiaogginfo where gonggaoguid=? and ifnull(auditstatus,'') = '3' ";
                CgZbggandbd zsggandbd = service.find(sql_ggandbd, CgZbggandbd.class,
                        "ZS_" + cgTrustdeedinfo.getBiaoDuanGuid());
                if (zsggandbd == null || zsggandbd.isEmpty()) {
                    return "未查询到标段预审公告标段数据，无法变更！";
                }
                sql_gg += " and zishentype = '1' ";
                AGCgZhaobiaogginfo zsGG = service.find(sql_gg, AGCgZhaobiaogginfo.class, zsggandbd.getGonggaoguid());
                if (zsGG == null || zsGG.isEmpty()) {
                    return "未查询到预审公告数据，无法变更！";
                }
                String sql_zswjandbd = "select * from CG_ZiShengFileAndBD where biaoduanguid=?";
                String sql_wj = "select * from CG_ZiShenWJ where beianguid=? and ifnull(auditstatus,'') = '3' ";
                CgZishengfileandbd zswjandbd = service.find(sql_zswjandbd, CgZishengfileandbd.class,
                        cgTrustdeedinfo.getBiaoDuanGuid());
                if (zswjandbd == null || zswjandbd.isEmpty()) {
                    return "未查询到标段资审公告数据，无法变更！";
                }
                CgZishenwj zswj = service.find(sql_wj, CgZishenwj.class, zswjandbd.getBeianguid());
                if (zswj == null || zswj.isEmpty()) {
                    return "未查询到资审文件数据，无法变更！";
                }
                String zbggMsg = yuShenGGBG(zsGG, zswj, bdInfo, cgTrustdeedinfo, modifInfo, service);
                if (StringUtil.isNotBlank(zbggMsg)) {
                    return zbggMsg;
                }
            }
            else {// 过了资审开标时间//没到自身开标时间
                  // 后审阶段：CG_ZhaoBiaoGGInfo cg_zbggandbd biaoduanguid
                  // cg_zhaobiaofilebeian.beianguid=cg_zbfileandbd.beianguid
                  // 变更：CG_GZGongGao.gonggaoguid=cg_zbggandbd.gonggaoguid
                  // cg_zbggandbd.biaoduanguid
                String sql_ggandbd = "select * from CG_ZBGGANDBD where biaoduanguid=?";
                String sql_gg = "select * from CG_zhaobiaogginfo where gonggaoguid=? and ifnull(auditstatus,'') = '3' ";
                CgZbggandbd zbggandbd = service.find(sql_ggandbd, CgZbggandbd.class, cgTrustdeedinfo.getBiaoDuanGuid());
                if (zbggandbd == null || zbggandbd.isEmpty()) {
                    return "未查询到标段招标公告（二次发标）数据，无法变更！";
                }
                sql_gg += " and zishentype = '2' ";
                AGCgZhaobiaogginfo zbGG = service.find(sql_gg, AGCgZhaobiaogginfo.class, zbggandbd.getGonggaoguid());
                if (zbGG == null || zbGG.isEmpty()) {
                    return "未查询到二次发标数据，无法变更！";
                }
                String zbggMsg = zhaoBiaoGGBG(zbGG, bdInfo, cgTrustdeedinfo, modifInfo, service);
                if (StringUtil.isNotBlank(zbggMsg)) {
                    return zbggMsg;
                }
            }

        }
        else {// 后审项目
              // 招标公告文件：cg_zhaobiaogginfo cg_zbggandbd biaoduanguid
              // cg_zhaobiaofilebeian.beianguid=cg_zbfileandbd.beianguid
              // 变更：CG_GZGongGao.gonggaoguid=cg_zhaobiaogginfo.gonggaoguid
              // cg_zbggandbd.biaoduanguid
            String sql_ggandbd = "select * from CG_ZBGGANDBD where biaoduanguid=?";
            String sql_gg = "select * from CG_zhaobiaogginfo where gonggaoguid=? and ifnull(auditstatus,'') = '3' ";
            CgZbggandbd zbggandbd = service.find(sql_ggandbd, CgZbggandbd.class, cgTrustdeedinfo.getBiaoDuanGuid());
            if (zbggandbd == null || zbggandbd.isEmpty()) {
                return "未查询到标段资审公告数据，无法变更！";
            }
            AGCgZhaobiaogginfo zbGG = service.find(sql_gg, AGCgZhaobiaogginfo.class, zbggandbd.getGonggaoguid());
            if (zbGG == null || zbGG.isEmpty()) {
                return "未查询到资审公告数据，无法变更！";
            }
            String zbggMsg = zhaoBiaoGGBG(zbGG, bdInfo, cgTrustdeedinfo, modifInfo, service);
            if (StringUtil.isNotBlank(zbggMsg)) {
                return zbggMsg;
            }
        }

        return "";
    }

    /**
     * 预审项目，预审阶段变更公告接口
     * 
     * @return
     */
    public String yuShenGGBG(AGCgZhaobiaogginfo zsGG, CgZishenwj zswj, AGCgBiaoduaninfo bdInfo,
            CgTrustdeedinfo cgTrustdeedinfo, JSONObject modifInfo, ZtbCommonDao service) {
        AGCgZschengqing zschengqing = new AGCgZschengqing();
        zschengqing.setRowguid(UUID.randomUUID().toString());
        zswj.setProjectjiaoyitype(bdInfo.getProjectjiaoyitype());
        zschengqing.setSbrcode(cgTrustdeedinfo.getSbr_code());
        zschengqing.setSbrname(cgTrustdeedinfo.getSbr_name());
        zschengqing.setSbrunitguid(cgTrustdeedinfo.getSbr_unitguid());
        zschengqing.setSbrunitname(cgTrustdeedinfo.getSbr_unitname());
        zschengqing.setSbrtel(cgTrustdeedinfo.getSbr_tel());
        zschengqing.setSbrmoblie(cgTrustdeedinfo.getSbr_moblie());
        zschengqing.setXiaqucode(zswj.getXiaqucode());
        zschengqing.setBeianguid(zswj.getBeianguid());
        zschengqing.setZschengqingguid(zschengqing.getRowguid());
        zschengqing.setProjectjiaoyitype(zswj.getProjectjiaoyitype());
        zschengqing.setFabaoname(zswj.getFabaoname());
        zschengqing.setFabaono(zswj.getFabaono());
        zschengqing.setFabaoguid(zswj.getFabaoguid());
        zschengqing.setBiaoduanname(bdInfo.getBiaoduanname());
        zschengqing.setBiaoduanno(bdInfo.getBiaoduanno());
        zschengqing.setChengqingnum(new DB_ZiShenChengQing().GetChengQingNum(zswj.getBeianguid()) + "");
        zschengqing.setGonggaotitle(bdInfo.getBiaoduanname() + "项目变更公告第" + zschengqing.getChengqingnum() + "次");
        zschengqing.setOrgApplyEndDate(zswj.getApplyenddate());
        zschengqing.setOrgFaShouEndDate(zswj.getApplyenddate());
        String a = new DB_ZiShenChengQing().GetZiShenChengQingFileNo(zschengqing.getBiaoduanno(),
                Integer.parseInt(zschengqing.getChengqingnum()));
        zschengqing.setZschengqingno(a);
        zschengqing.setCqitem("3");// 是否修改时间
        if ("1".equals(modifInfo.getString("isSubmit"))) {
            zschengqing
                    .setBgzsfileenddate(formatDateString(modifInfo.getString("tenderDocSubmitEnd"), "yyyyMMddHHmmss"));
            zschengqing
                    .setBgApplyEndDate(formatDateString(modifInfo.getString("tenderDocSubmitEnd"), "yyyyMMddHHmmss"));
        }
        if ("1".equals(modifInfo.getString("isEnd"))) {
            zschengqing.setBgzsfashouenddate(formatDateString(modifInfo.getString("saleEndTime"), "yyyyMMddHHmmss"));
            zschengqing.setBgFaShouEndDate(formatDateString(modifInfo.getString("saleEndTime"), "yyyyMMddHHmmss"));
        }
        if ("1".equals(modifInfo.getString("isOpen"))) {
            bdInfo.setZishenkaibiaodate(formatDateString(modifInfo.getString("openTime"), "yyyyMMddHHmmss"));
            zsGG.setGonggaorealto(bdInfo.getZishenkaibiaodate());// 此字段不变无法报名
        }
        if ("1".equals(modifInfo.getString("isStart"))) {
            bdInfo.setZsfileopendate(formatDateString(modifInfo.getString("saleStartTIme"), "yyyyMMddHHmmss"));
        }
        zschengqing.setCqcontent(modifInfo.getString("content"));

        service.update(bdInfo);
        service.update(zsGG);// 更新老公告时间
        // 新增状态
        zschengqing.setSbrdate(new Date());
        // 澄清文件递交时间
        zschengqing.setCqsenddate(new Date());
        zschengqing.setAuditstatus("3");
        service.insert(zschengqing);
        // 附件：
        JSONObject visibleFiles = modifInfo.getJSONObject("visibleFiles");
        String clientType = "";
        String sql_attach = "";
        // 公开附件
        if (visibleFiles != null) {
            clientType = "J044";
            sql_attach = "select * from ZHGL_AttachInfo_YeWu where clientguid = ? and clienttype = '" + clientType
                    + "' order by row_id desc";
            erpFrameSysUtil.dealFile(visibleFiles, zschengqing.getRowguid(), clientType, sql_attach, false, service);
        }
        btnSendMsg(zschengqing, zswj, bdInfo);
        HyFileStatusService.setFileStatus(bdInfo.getBiaoduanguid(), "zscqfile", zschengqing.getZschengqingguid());
        // 记录日志
        Epoint.ZtbMis.Bizlogic.Sys.DB_OperationLog.Sys_OperationLog_Add_NoTran(zschengqing.getRowguid(),
                EpointBid_Constant.Enums.ClsEnum.SubSysName.建设工程, ClsEnum.OperName.增加,
                "保存资审澄清文件 项目名称 : " + zschengqing.getFabaoname() + ",标段：" + zschengqing.getBiaoduanname(), "保存资格预审澄清文件");

        return "";
    }

    protected void btnSendMsg(AGCgZschengqing zschengqing, CgZishenwj zswj, AGCgBiaoduaninfo bdInfo) {
        String strsql = "select distinct(danweiguid),danweiname from Cg_Filedownload_Log where clientguid=?";
        List<Record> DW_down = ZtbCommonDao.getInstance().findList(strsql, Record.class, zswj.getBeianguid());
        if (EpointCollectionUtils.isNotEmpty(DW_down)) {
            for (int i = 0; i < DW_down.size(); i++) {
                Epoint.HuiYuanZtbMis.Bizlogic.HuiYuan.DB_HuiYuan_AlertInfo.HuiYuan_AlertInfo_Add(
                        DW_down.get(i).getStr("DANWEIGUID"), DW_down.get(i).getStr("DANWEINAME"), "",
                        "【重要通知】资格预审文件澄清通知：" + zswj.getShowbiaoduanname(), "", zschengqing.getSbrcode(),
                        zschengqing.getSbrname(), new Date(), "", DB_HuiYuan_AlertInfo.MESSAGE_TYPE_CHENGQING,
                        zschengqing.getCqcontent(), ClsEnum.Sys_DanWeiType.投标人.getValue(), bdInfo.getBiaoduanguid(),
                        ClsEnum.SubSysName.建设工程.getValue());

            }
        }

    }

    /**
     * 预审项目后审阶段变更公告接口
     * 后身项目招标公告变更接口
     * 
     * @return
     */
    public String zhaoBiaoGGBG(AGCgZhaobiaogginfo zbGG, AGCgBiaoduaninfo bdInfo, CgTrustdeedinfo cgTrustdeedinfo,
            JSONObject modifInfo, ZtbCommonDao service) {
        AGCgGzgonggao gzGongGao = new AGCgGzgonggao();
        gzGongGao.setRowguid(UUID.randomUUID().toString());
        gzGongGao.setGzguid(gzGongGao.getRowguid());
        gzGongGao.setGonggaoguid(zbGG.getGonggaoguid());
        gzGongGao.setBaomingenddate(zbGG.getGonggaorealto());
        gzGongGao.setPmto(EpointDateUtil.convertDate2String(zbGG.getGonggaorealto(), "HH:mm"));
        gzGongGao.setFabaoguid(zbGG.getFabaoguid());
        gzGongGao.setFabaono(zbGG.getFabaono());
        gzGongGao.setFabaoname(zbGG.getStr(StringUtil.toUpperCase("FaBaoName")));
        gzGongGao.setJianshedanwei(zbGG.getStr(StringUtil.toUpperCase("JianSheDanWei")));
        gzGongGao.setDailiname(zbGG.getStr(StringUtil.toUpperCase("DaiLiName")));
        gzGongGao.setXiaqucode(zbGG.getStr(StringUtil.toUpperCase("XiaQuCode")));
        gzGongGao.setProjectjiaoyitype(zbGG.getStr(StringUtil.toUpperCase("ProjectJiaoYiType")));
        gzGongGao.setGonggaotitle(zbGG.getStr(StringUtil.toUpperCase("ShowBiaoDuanName")) + "变更公告第"
                + (AGDB_JSGC_GZGongGao.GetGZGongGaoCount(zbGG.getGonggaoguid()) + 1) + "次");
        gzGongGao.setAuditstatus("3");
        // 强类型方式读取字段,在ContextHelper中判断是否招标办对字段进行统一处理
        gzGongGao.setSbrdate(new Date());
        gzGongGao.setSbrcode(cgTrustdeedinfo.getSbr_code());
        gzGongGao.setSbrname(cgTrustdeedinfo.getSbr_name());
        gzGongGao.setSbrunitguid(cgTrustdeedinfo.getSbr_unitguid());
        gzGongGao.setSbrunitname(cgTrustdeedinfo.getSbr_unitname());
        gzGongGao.setSbrtel(cgTrustdeedinfo.getSbr_tel());
        gzGongGao.setSbrmoblie(cgTrustdeedinfo.getSbr_moblie());
        gzGongGao.setShowbiaoduanname(zbGG.getShowbiaoduanname());
        gzGongGao.setShowbiaoduanno(zbGG.getShowbiaoduanno());
        // 获取标段中最近一次设置的时间oldzbfilelqdateto
        // 招标文件发售开始时间
        gzGongGao.setOldzbfilelqdatefrom(EpointDateUtil.convertString2DateAuto(EpointDateUtil
                .convertDate2String(bdInfo.get(StringUtil.toUpperCase("zbfilelqdatefrom")), "yyyy/MM/dd HH:mm")));
        gzGongGao.set("IsChangeZBFileLQFrom", modifInfo.getString("isStart"));
        if ("1".equals(modifInfo.getString("isStart"))) {
            gzGongGao.setNewzbfilelqdatefrom(formatDateString(modifInfo.getString("saleStartTIme"), "yyyyMMddHHmmss"));
        }
        // 招标文件发售截止时间
        gzGongGao.setOldzbfilelqdateto(EpointDateUtil.convertString2DateAuto(EpointDateUtil
                .convertDate2String(bdInfo.get(StringUtil.toUpperCase("zbfilelqdateto")), "yyyy/MM/dd HH:mm")));
        gzGongGao.set("IsChangeZBFileLQTo", modifInfo.getString("isEnd"));
        if ("1".equals(modifInfo.getString("isEnd"))) {
            gzGongGao.setNewzbfilelqdateto(formatDateString(modifInfo.getString("saleEndTime"), "yyyyMMddHHmmss"));
        }
        // 招标文件澄清截止
        gzGongGao.setOldtiwenenddate(EpointDateUtil.convertString2DateAuto(EpointDateUtil
                .convertDate2String(bdInfo.get(StringUtil.toUpperCase("tiwenenddate")), "yyyy/MM/dd HH:mm")));
        gzGongGao.set("IsChangeTWSJ", modifInfo.getString("isAsk"));
        if ("1".equals(modifInfo.getString("isAsk"))) {
            gzGongGao.setNewtiwenenddate(formatDateString(modifInfo.getString("askEndTime"), "yyyyMMddHHmmss"));
        }
        // 投标文件递交截止/开标时间
        gzGongGao.setOldkaibiaodate(EpointDateUtil.convertString2DateAuto(EpointDateUtil
                .convertDate2String(bdInfo.get(StringUtil.toUpperCase("kaibiaodate")), "yyyy/MM/dd HH:mm")));
        gzGongGao.set("IsChangeKBSJ", modifInfo.getString("isOpen"));
        if ("1".equals(modifInfo.getString("isOpen"))) {
            gzGongGao.setKaibiaodate(formatDateString(modifInfo.getString("openTime"), "yyyyMMddHHmmss"));
        }
        gzGongGao.setGonggaotitle(modifInfo.getString("title"));
        gzGongGao.setShrdate(new Date());
        gzGongGao.setGengzhengcontent(modifInfo.getString("content"));
        JqxtTrustProjectInfo jqxtrecord = service.find(JqxtTrustProjectInfo.class, cgTrustdeedinfo.getRowguid());
        if (jqxtrecord != null && ("多".equals(cgTrustdeedinfo.getBaojiatimes())
                && ("B".equals(cgTrustdeedinfo.getPurchasemode()) || "Y".equals(cgTrustdeedinfo.getPurchasemode())))) {
            gzGongGao.setOldyudingfrom(EpointDateUtil.convertDate2String(jqxtrecord.getYudingfrom(), "yyyy-MM-dd"));
            gzGongGao.setOldstarttime(jqxtrecord.getStarttime());
            gzGongGao.setOldyubeiminute(jqxtrecord.getYubeiminute());
            gzGongGao.setOldyanshisecond(jqxtrecord.getYanshisecond());
            if ("1".equals(gzGongGao.getStr("Ischangeydf")) && gzGongGao.getNewyudingfrom() != null) {
                jqxtrecord.setYudingfrom(EpointDateUtil.convertString2Date(gzGongGao.getNewyudingfrom(), "yyyy-MM-dd"));
            }
            if ("1".equals(gzGongGao.getStr("Ischangestm")) && gzGongGao.getNewstarttime() != null) {
                jqxtrecord.setStarttime(gzGongGao.getNewstarttime());
            }
            if ("1".equals(gzGongGao.getStr("Ischangeybm")) && gzGongGao.getNewyubeiminute() != null) {
                jqxtrecord.setYubeiminute(gzGongGao.getNewyubeiminute());
            }
            if ("1".equals(gzGongGao.getStr("Ischangeys")) && gzGongGao.getNewyanshisecond() != null) {
                jqxtrecord.setYanshisecond(gzGongGao.getNewyanshisecond());
            }
            service.update(jqxtrecord);
        }
        // 变更公告放置时间,有个默认时间的,审核通过的时候,CG_GZGongGao表GongGaoFrom取审核通过的时候,GongGaoTO默认增加五天的值
        Date date = new Date();
        Calendar calendar = new GregorianCalendar();
        calendar.setTime(date);
        calendar.add(Calendar.DATE, 5);
        gzGongGao.setGonggaofrom(date);
        gzGongGao.setGonggaoto(calendar.getTime());
        // 反写4个变更的时间到标段表
        if (StringUtil.isNotBlank(gzGongGao.getNewzbfilelqdatefrom())) {
            bdInfo.set("zbfilelqdatefrom", gzGongGao.getNewzbfilelqdatefrom());
            zbGG.setZbfilelqdatefrom(gzGongGao.getNewzbfilelqdatefrom());

        }
        if (StringUtil.isNotBlank(gzGongGao.getNewzbfilelqdateto())) {
            bdInfo.set("zbfilelqdateto", gzGongGao.getNewzbfilelqdateto());
            zbGG.setZbfilelqdateto(gzGongGao.getNewzbfilelqdateto());
            cgTrustdeedinfo.set("biddocsaleend", gzGongGao.getNewzbfilelqdateto());

        }
        if (StringUtil.isNotBlank(gzGongGao.getNewtiwenenddate())) {
            bdInfo.set("tiwenenddate", gzGongGao.getNewtiwenenddate());
        }
        if (StringUtil.isNotBlank(gzGongGao.getKaibiaodate())) {
            bdInfo.setKaibiaodate(gzGongGao.getKaibiaodate());
            bdInfo.setBzjshoudate(gzGongGao.getKaibiaodate());
            bdInfo.setBaojiato(gzGongGao.getKaibiaodate());
            bdInfo.setBaomingto(gzGongGao.getKaibiaodate());
            zbGG.setGonggaorealto(gzGongGao.getKaibiaodate());
        }
        zbGG.setGzisselected("1");

        if ("Q".equals(cgTrustdeedinfo.getPurchasemode()) || "Y".equals(cgTrustdeedinfo.getPurchasemode())
                || "P".equals(cgTrustdeedinfo.getPurchasemode()) || "YX".equals(cgTrustdeedinfo.getPurchasemode())
                || "YJ".equals(cgTrustdeedinfo.getPurchasemode())) {
            // 审核通过后自动发送
            // 更新单位的邀请涵发送状态
            List<CgToubiaodanwei> danweilst = new DB_CG_TouBiaoDanWei_QY()
                    .GetListByGongGaoGuid(gzGongGao.getGonggaoguid());
            for (CgToubiaodanwei cgToubiaodanwei : danweilst) {
                // 如果是开标后变更，将置为已读的邀请函且未确认参加的单位的邀请函重新置为未读状态
                if (gzGongGao.getOldkaibiaodate().before(new Date())
                        && !"确认参加".equals(cgToubiaodanwei.getIsbaoming())) {
                    service.execute(
                            "update huiyuan_message set isend=null where biaoduanguid=? and danweiguid=? and handletype=? and isend='1' ",
                            cgToubiaodanwei.getBiaoduanguid(), cgToubiaodanwei.getDanweiguid(),
                            DB_HuiYuan_AlertInfo.MESSAGE_TYPE_YAOQING);
                }
                if ("1".equals(cgToubiaodanwei.getIssendyaoqinghan())) {
                    continue;
                }
                cgToubiaodanwei.setIssendyaoqinghan("1");
                cgToubiaodanwei.setYqhsenddate(new Date());
                service.update(cgToubiaodanwei);

                // 记录日志
                Epoint.ZtbMis.Bizlogic.Sys.DB_OperationLog.Sys_OperationLog_Add_NoTran(
                        cgToubiaodanwei.getRowguid(), EpointBid_Constant.Enums.ClsEnum.SubSysName.建设工程,
                        EpointBid_Constant.Enums.ClsEnum.OperName.增加, "发出邀请函，标段(包)为："
                                + cgToubiaodanwei.getBiaoduanguid() + ",单位名称为：" + cgToubiaodanwei.getDanweiname(),
                        "发出邀请函");
                CgBiaoduaninfo bd = service.find(CgBiaoduaninfo.class, cgToubiaodanwei.getBiaoduanguid());
                String strTitle = "【邀请提醒】标段(包)名称：" + bd.getBiaoduanname();
                String handleurl = "qyztbmis/pages_fz/toubiaobm/YQBaoMing_TBR_Workflow?BiaoDuanGuid="
                        + cgToubiaodanwei.getBiaoduanguid() + "&BaoMingGuid=" + cgToubiaodanwei.getRowguid();
                // 向Huiyuan_Message表插入数据
                DB_HuiYuan_AlertInfo.HuiYuan_AlertInfo_Add(cgToubiaodanwei.getDanweiguid(),
                        cgToubiaodanwei.getDanweiname(), "", strTitle, "", cgTrustdeedinfo.getSbr_code(),
                        ContextHelper.getDisplayName(), new Date(), handleurl,
                        DB_HuiYuan_AlertInfo.MESSAGE_TYPE_YAOQING, "", cgToubiaodanwei.getDanweitype(),
                        bd.getBiaoduanguid(), bd.getProjectjiaoyitype());
            }
        }
        // 查询已经购买过文件得供应商 我已评估不存在性能问题
        List<CgToubiaodanwei> toubiaodanweis = service.findList(
                "select ct.danweiguid,ct.danweiname,ct.LianXiRen,ct.Lixirentel,ifnull(ct.IsDecrypted,'1') as IsDecrypted from CG_TouBiaoDanWei  ct left join cg_biaoduaninfo cb on ct.biaoduanguid=cb.biaoduanguid  where ct.biaoduanguid =? and (ifnull(ct.iszhifu,'0')='1' or cb.BiaoShuPrice=0)",
                CgToubiaodanwei.class, cgTrustdeedinfo.getBiaoDuanGuid());
        if (EpointCollectionUtils.isNotEmpty(toubiaodanweis)) {
            EpZtbEncrypt.getInstance().DecryDataTable(toubiaodanweis, "CG_TouBiaoDanWei",
                    cgTrustdeedinfo.getBiaoDuanGuid(), ClsEnum.SubSysName.政府采购);
            SendMessage sendMessage = new SendMessage();
            // 发送短信或者代办
            for (CgToubiaodanwei toubiaodanwei : toubiaodanweis) {
                sendMessage.sendSystemAndMobileMessage(cgTrustdeedinfo.getBiaoDuanGuid(),
                        ProNameEnum.招标文件竞价文件变更公告.getValue(), toubiaodanwei.getDanweiguid(),
                        toubiaodanwei.getDanweiname(), toubiaodanwei.getLianxiren(), toubiaodanwei.getLixirentel(),
                        gzGongGao.getRowguid());
            }
        }
        // 附件：
        JSONObject visibleFiles = modifInfo.getJSONObject("visibleFiles");
        String clientType = "";
        String sql_attach = "";
        // 如果是邀请招标类的需要添加需要设置isyqh=1，并且把附件添加到变更邀请涵中
        if ("Q".equals(bdInfo.getZhaobiaofangshi()) || "YX".equals(bdInfo.getZhaobiaofangshi())
                || "Y".equals(bdInfo.getZhaobiaofangshi()) || "YJ".equals(bdInfo.getZhaobiaofangshi())
                || "P".equals(bdInfo.getZhaobiaofangshi())) {
            gzGongGao.setIsYQH("1");
            gzGongGao.setGzgonggaostatuscode("4");
            if (visibleFiles != null) {
                clientType = "AG023";
                sql_attach = "select * from ZHGL_AttachInfo_YeWu where clientguid = ? and clienttype = '" + clientType
                        + "' order by row_id desc";
                erpFrameSysUtil.dealFile(visibleFiles, gzGongGao.getRowguid(), clientType, sql_attach, false, service);
            }
        }
        else {
            // 公告附件
            if (visibleFiles != null) {
                clientType = "AG012";
                sql_attach = "select * from ZHGL_AttachInfo_YeWu where clientguid = ? and clienttype = '" + clientType
                        + "' order by row_id desc";
                erpFrameSysUtil.dealFile(visibleFiles, gzGongGao.getRowguid(), clientType, sql_attach, false, service);
            }
        }
        service.insert(gzGongGao);
        service.update(bdInfo);
        service.update(zbGG);
        return "";
    }

    /**
     * 招标委托标底信息
     */
    public String biaodijiaInfo(CgTrustdeedinfo cgTrustdeedinfo, JSONObject modifInfo, ZtbCommonDao service) {
        String trustGuid = cgTrustdeedinfo.getTrustguid();
        Date now = new Date();
        // 获取开标date,如果开标时间早于当前时间，则返回报错信息
        AGCgBiaoduaninfo find = service.find(AGCgBiaoduaninfo.class, cgTrustdeedinfo.getBiaoDuanGuid());
        if (find.getKaibiaodate() != null && EpointDateUtil.compareDateOnDay(new Date(), find.getKaibiaodate()) > 0) {
            return "当前标段已经开标不能进行标底价录入";
        }
        cgTrustdeedinfo.setTotalBidMinPrice(modifInfo.getDouble("bidBottom"));
        cgTrustdeedinfo.setTotalForecastPrice(modifInfo.getDouble("referPrice"));
        if (modifInfo.getJSONObject("files") != null) {
            String sql_attach = "select * from ZHGL_AttachInfo_YeWu where clientguid = ? and clienttype = 'AG010' order by row_id desc";
            erpFrameSysUtil.dealFile(modifInfo.getJSONObject("files"), trustGuid, "AG010", sql_attach, false, service);
        }
        // plus表
        CgTrustdeedinfoplus cgTrustdeedinfoplus = service.find(CgTrustdeedinfoplus.class, trustGuid);
        if (cgTrustdeedinfoplus == null || cgTrustdeedinfoplus.isEmpty()) {
            return "数据丢失，请联系管理员重新推送！";
        }
        else {
            cgTrustdeedinfoplus.setOperatedate(now);
            cgTrustdeedinfoplus.setBasebiddescription(formatString(modifInfo.getString("content")));
            service.update(cgTrustdeedinfoplus);
        }
        // 需求清单
        JSONArray qingdanMList = null;
        if ("A".equals(cgTrustdeedinfo.getPurchasetype())) {
            qingdanMList = modifInfo.getJSONArray("cgnEngMList");
        }
        else if ("M".equals(cgTrustdeedinfo.getPurchasetype())) {
            qingdanMList = modifInfo.getJSONArray("cgnMaterialMList");
        }
        else if ("S".equals(cgTrustdeedinfo.getPurchasetype())) {
            qingdanMList = modifInfo.getJSONArray("cgnServiceMList");
            if (qingdanMList == null) {// 设备修复
                qingdanMList = modifInfo.getJSONArray("cgnDeviceMList");
            }
        }

        if (qingdanMList != null) {
            for (Object o : qingdanMList) {
                JSONObject data = (JSONObject) o;
                String msg = updateQingDanBD(data, cgTrustdeedinfo, modifInfo.getString("enterpriseCode"),
                        modifInfo.getString(modifInfo.getString("enterpriseName")), service);
                if (StringUtil.isNotBlank(msg)) {
                    return msg;
                }
            }
        }
        else {
            return "标底价数据丢失，请联系管理员重新推送！";
        }
        return "";
    }

    /**
     * 招标委托技术交流反馈信息
     */
    public String jiShuJiaoLiuInfo(CgZigeyushen zgys, JSONObject modifInfo) {
        List<CgToubiaodanwei> tbdwlist = ZtbCommonDao.getInstance().findList(
                " select ct.*,hu.code from  cg_toubiaodanwei  ct left join huiyuan_unitcominfo hu on hu.DanWeiGuid = ct.DanWeiGuid  where biaoduanguid = ? and isin <>'2' order by row_id desc",
                CgToubiaodanwei.class, zgys.getBiaoduanguid());
        JSONArray mainDataArray = modifInfo.getJSONObject("msgBody").getJSONArray("data");
        JSONObject files = modifInfo.getJSONObject("msgBody").getJSONObject("files");

        Integer num = null;
        JSONArray fieldataArray = null;

        if (files != null) {
            num = files.getInteger("num");
            fieldataArray = files.getJSONArray("data");
        }
        int loop = 0;

        if (mainDataArray != null) {
            for (Object o : mainDataArray) {
                for (CgToubiaodanwei tbdw : tbdwlist) {
                    JSONObject data = (JSONObject) o;
                    // 匹配到业务库中的投标单位
                    if (tbdw.get("code").equals(data.getString("supplyCode"))) {
                        loop++;
                        // 1 迁移
                        CgZishenresulthistory history = new CgZishenresulthistory();
                        history.setRowguid(UUID.randomUUID().toString());
                        history.setDanweiguid(tbdw.getDanweiguid());
                        history.setDanweiname(tbdw.getDanweiname());
                        history.setBiaoduanguid(zgys.getBiaoduanguid());
                        history.setEliminate(tbdw.getZishenresult());
                        history.setReason(tbdw.getNotpassreason());
                        history.setRemark("该单位在当前业务系统中的资格预审结果为：" + ("1".equals(tbdw.getZishenresult()) ? "通过" : "不通过")
                                + "【接受结果为】：" + ("1".equals(data.getString("eliminate")) ? "淘汰" : "未淘汰")
                                + "当前系统中的不通过理由为：" + tbdw.getNotpassreason() + "【接受结果为】:" + data.getString("reason"));
                        history.setOperatedate(new Date());
                        ZtbCommonDao.getInstance().insert(history);

                        // 2更新业务库
                        // 淘汰了 就是业务库中的 不通过 2
                        // 反之 为通过 1
                        tbdw.set("jishujiaoliu", "1".equals(data.getString("eliminate")) ? "0" : "1");
                        tbdw.setNotpassreason(data.getString("reason"));
                        ZtbCommonDao.getInstance().update(tbdw);

                    }
                }
            }

            if (loop == 0) {
                return "通过接受的supplyCode和业务库中的投标单位表9s未匹配上";
            }
            ZtbCommonDao service = ZtbCommonDao.getInstance();

            if (num != null && fieldataArray != null) {
                // 获取附件信息
                String clientType = "ZS009";
                String sql_attach = "select * from ZHGL_AttachInfo_YeWu where clientguid = ? and clienttype = 'ZS009' order by row_id desc";
                try {
                    logger.info("插入附件");
                    erpFrameSysUtil.dealFile(files, zgys.getRowguid(), clientType, sql_attach, false, service);
                    if (service != null) {
                        service.close();
                    }
                }
                catch (Exception e) {
                    // TODO: handle exception
                    logger.error(e.getMessage(), e);
                }
                finally {
                    if (service != null) {
                        service.close();
                    }
                }
            }
        }
        else {
            return "接受的单位信息数据为空";
        }
        return "";
    }

    /**
     * 定标结果信息
     */
    public String DingBiaoJieGuoInfo(CgTrustdeedinfo cgTrustdeedinfo, CgShoubiaotuijian cgShoubiaotuijian,
            JSONObject modifInfo, ZtbCommonDao service) {
        String trustGuid = cgTrustdeedinfo.getTrustguid();
        if ("1".equals(formatString(modifInfo.getString("decisionStatus")))) {
            String dingBiaoYiJian = "";// 定标意见
            dingBiaoYiJian = formatString(modifInfo.getString("approveMessage"));
            cgShoubiaotuijian.set("handleopinion", dingBiaoYiJian);
            cgShoubiaotuijian.set("handletype", "1");
            JSONArray details = null;
            if ("A".equals(cgTrustdeedinfo.getPurchasetype())) {
                details = modifInfo.getJSONArray("dsnEngDetail");
            }
            else if ("S".equals(cgTrustdeedinfo.getPurchasetype())) {
                details = modifInfo.getJSONArray("dsnServiceDetail");
            }
            else if ("M".equals(cgTrustdeedinfo.getPurchasetype())) {
                details = modifInfo.getJSONArray("dsnMaterialDetail");
                if (details == null || details.size() < 1) {
                    details = modifInfo.getJSONArray("dsnDeviceDetail");
                }
            }
            boolean checkDataIsNoChange = ERPCommon.checkDataIsNoChange(cgTrustdeedinfo, details);
            if (!checkDataIsNoChange) {
                return "存在物料成交总价与拟中标总价不一致,请核实数据";
            }
            if (details != null) {
                List<CgShoubiaoandbditem> old_List = service.findList(
                        "select * from CG_ShouBiaoAndBDItem where biaoduanguid=?", CgShoubiaoandbditem.class,
                        cgTrustdeedinfo.getBiaoDuanGuid());
                String uuid = UUID.randomUUID().toString();
                for (CgShoubiaoandbditem old : old_List) {// 先将老数据加个等备份。，然后删除新增
                    AGCgShoubiaoandbditemhistory itemhistory = new AGCgShoubiaoandbditemhistory();
                    itemhistory.setRowguid(UUID.randomUUID().toString());
                    itemhistory.setBiaoduanguid(cgTrustdeedinfo.getBiaoDuanGuid());
                    itemhistory.setTrustguid(cgTrustdeedinfo.getTrustguid());
                    itemhistory.setGysdanweiguid(old.getDanweiguid());
                    itemhistory.setAwardtype(old.getAwardtype());
                    itemhistory.setAwardtotalprice(old.getAwardtotalprice());
                    itemhistory.setQuasiawardtotalprice(old.getQuasiawardtotalprice());
                    itemhistory.setAwardquantity(old.getAwardquantity());
                    itemhistory.setAwardsinprice(old.getAwardsinprice());
                    itemhistory.setItemguid(old.getItemguid());
                    itemhistory.set("purResultLineId", old.getStr("purResultLineId"));// 采购实施结果明细号
                    itemhistory.setShoubiaoguid(old.getShoubiaoguid());
                    itemhistory.setOperatedate(new Date());
                    itemhistory.setPviguid(uuid);// pviguid拿来当作表示同一批备份的数据
                    service.insert(itemhistory);
                    service.delete(old);
                }
                service.execute("delete from CG_GGZhongBiaoDetail where biaoduanguid=?",
                        cgTrustdeedinfo.getBiaoDuanGuid());
                for (Object o : details) {
                    JSONObject detail = (JSONObject) o;
                    String msg = insertDingBiao(detail, cgTrustdeedinfo, service);
                    if (StringUtil.isNotBlank(msg)) {
                        return msg;
                    }
                }
                JSONArray dsnResults = modifInfo.getJSONArray("dsnResult");
                if (dsnResults != null) {
                    for (Object o : dsnResults) {
                        JSONObject dsnResult = (JSONObject) o;
                        String where = "danweiguid in(select danweiguid from cg_toubiaodanwei where (iszbr='1' or ifnull(PingBiaoPrice,0)!=0) and biaoduanguid='"
                                + cgShoubiaotuijian.getBiaoduanguid() + "')";
                        HuiyuanUnitcominfo gys = new ERPFrameSysUtil()
                                .getHuiyuanUnitcominfo(formatString(dsnResult.getString("supplyCode")), where, service);
                        if (gys == null || gys.isEmpty()) {
                            return "缺少投标单位信息数据";
                        }
                        CgToubiaodanwei danwei = service.find(
                                "select * from CG_TOUBIAODANWEI where biaoduanguid=? and danweiguid=? ",
                                CgToubiaodanwei.class, cgShoubiaotuijian.getBiaoduanguid(), gys.getDanweiguid());
                        CgGGZhongbiaodetail detail = new CgGGZhongbiaodetail();
                        dingBiaoYiJian = formatString(dsnResult.getString("approveNotes"));
                        // String winnum =
                        // StringUtil.isBlank(dsnResult.getString("winNum")) ?
                        // "0"
                        // : String.valueOf(dsnResult.getString("winNum"));
                        // String bidPrice =
                        // StringUtil.isBlank(dsnResult.getString("bidPrice")) ?
                        // "0"
                        // : String.valueOf(dsnResult.getString("bidPrice"));
                        // Double totalbidprice = new
                        // BigDecimal(bidPrice).doubleValue();
                        String winPrice = StringUtil.isBlank(dsnResult.getString("winPrice")) ? "0"
                                : String.valueOf(dsnResult.getString("winPrice"));
                        Double totalwinprice = new BigDecimal(winPrice).doubleValue();
                        detail.setRowguid(UUID.randomUUID().toString());
                        detail.setOperatedate(new Date());
                        detail.setOperateusername("系统管理员");
                        detail.setGonggaoguid(cgShoubiaotuijian.getRowguid());
                        detail.setBiaoduanguid(cgShoubiaotuijian.getBiaoduanguid());
                        detail.setDanweiguid(gys.getDanweiguid());
                        detail.setDanweiname(gys.getDanweiname());
                        detail.set("dbreason", dingBiaoYiJian);
                        // detail.setZhongbiaomoney(totalbidprice);// 评标价格-拟中标总价
                        detail.setZhongbiaomoney(StringUtil.isBlank(danwei.getStr("pingbiaoprice")) ? 0
                                : Double.valueOf(danwei.getStr("pingbiaoprice")));
                        detail.setZhongbiaoprice(totalwinprice);// 中标价格
                        service.insert(detail);
                    }
                }
                // 给项目经理发送消息提醒
                MessagesCenter bean = new MessagesCenter();
                MessagesCenterService9 messagecenetrservice = new MessagesCenterService9();
                bean.setFromDispName(cgTrustdeedinfo.getSbr_name());
                bean.setMessageItemGuid(UUID.randomUUID().toString());
                bean.setTitle(String.format("收到委托编号为【%s】的ERP返回的定标结果", cgTrustdeedinfo.getTrustdeedno()));
                bean.setMessageType("办理");
                bean.setContent(cgTrustdeedinfo.getPurchaseprojectname() + "已接收到ERP定结果数据");
                bean.setMessagesRemindType("waithandle");
                bean.setSendMode(4);
                bean.setGenerateDate(new Date());
                bean.setTargetUser(cgTrustdeedinfo.getPromanagerguid());
                bean.setIsShow(1);
                bean.setTargetDispName(cgTrustdeedinfo.getPromanager());
                bean.setFromUser(cgTrustdeedinfo.getSbr_code());
                bean.setNoNeedRemind(0);
                // add by wxf 待办事宜list页面中的过滤条件
                bean.setIsDel(0);
                bean.setIsNoHandle(0);
                bean.setHandleType(MessagesCenterService9.MESSAGETYPE_WAIT);
                bean.setHandleUrl("qyztbmis/pages_fz/recommendaward/RecommendAward_Detail?RowGuid="
                        + cgShoubiaotuijian.getRowguid());
                bean.setOuGuid(cgTrustdeedinfo.getSbr_unitguid());
                messagecenetrservice.WaitHandle_Insert(bean, "");
                // 状态更新为已接收待审核，此状态下项目经理可以进行回退z
                cgShoubiaotuijian.set("erpstatus", "1");
                service.update(cgShoubiaotuijian);
                // 业务系统缓存更新
                new CommonUtil(service).updateCacheNum4Remaind("CG_ShouBiaoTuiJian", cgShoubiaotuijian.getRowguid(),
                        "1");
                // 记录日志
                Epoint.ZtbMis.Bizlogic.Sys.DB_OperationLog.Sys_OperationLog_Add_NoTran(cgShoubiaotuijian.getRowguid(),
                        ClsEnum.SubSysName.建设工程, ClsEnum.OperName.修改,
                        "评标结果确定接收ERP定标结果-项目经理审核 采购项目名称： " + cgTrustdeedinfo.getPurchaseprojectname(), bean.getTitle());
            }
            else {
                return "缺少数据";
            }
            if (modifInfo.getJSONObject("files") != null) {
                String sql_attach = "select * from ZHGL_AttachInfo_YeWu where clientguid = ? and clienttype = 'WTRDB' order by row_id desc";
                erpFrameSysUtil.dealFile(modifInfo.getJSONObject("files"), trustGuid, "WTRDB", sql_attach, false,
                        service);
            }
        }
        else if (StringUtil.isNotBlank(formatString(modifInfo.getString("decisionStatus")))
                && "2;3;4;".contains(formatString(modifInfo.getString("decisionStatus")) + ";")) {
            cgShoubiaotuijian.set("handletype", formatString(modifInfo.getString("decisionStatus")));
            cgShoubiaotuijian.set("handleopinion", modifInfo.getString("approveMessage"));
            // 状态更新为已接收待审核，此状态下项目经理可以进行回退z
            cgShoubiaotuijian.set("erpstatus", "1");
            service.update(cgShoubiaotuijian);
            // 业务系统缓存更新
            new CommonUtil(service).updateCacheNum4Remaind("CG_ShouBiaoTuiJian", cgShoubiaotuijian.getRowguid(), "1");
            // 记录日志
            Epoint.ZtbMis.Bizlogic.Sys.DB_OperationLog.Sys_OperationLog_Add_NoTran(cgShoubiaotuijian.getRowguid(),
                    ClsEnum.SubSysName.建设工程, ClsEnum.OperName.修改,
                    "评标结果确定接收ERP定标结果-项目经理审核 采购项目名称： " + cgTrustdeedinfo.getPurchaseprojectname(),
                    String.format("收到委托编号为【%s】的ERP返回的定标结果-流标", cgTrustdeedinfo.getTrustdeedno()));
        }
        else {
            return "数据不符合要求，decisionStatus=" + modifInfo.getString("decisionStatus");
        }
        if (modifInfo.getJSONObject("files") != null) {
            String sql = "select rowguid from CG_ShouBiaoTuiJian where BiaoDuanGuid =(select biaoduanguid from cg_trustdeedinfo ct where TrustGuid=?)";
            String tuiJianGuid = service.find(sql, String.class, trustGuid);
            String sql_attach = "select * from ZHGL_AttachInfo_YeWu where clientguid = ? and clienttype = 'WTRDB' order by row_id desc";
            erpFrameSysUtil.dealFile(modifInfo.getJSONObject("files"), tuiJianGuid, "WTRDB", sql_attach, false,
                    service);
        }
        return "";
    }

    /**
     * 成交信息通知
     */
    public String JJChengJiaoInfo(AGCgZhongbiaojieguo cgZhongbiaojieguo, CgTrustdeedinfo cgTrustdeedinfo,
            JSONObject chengjiaoInfo, ZtbCommonDao service) {
        AGCgBiaoduaninfo bdInfo = service.find(AGCgBiaoduaninfo.class, cgTrustdeedinfo.getBiaoDuanGuid());
        String zbjieguoGuid = UUID.randomUUID().toString();
        cgZhongbiaojieguo.setSbrdate(new Date());
        cgZhongbiaojieguo.setRowguid(zbjieguoGuid);
        cgZhongbiaojieguo.setJieguoguid(zbjieguoGuid);
        cgZhongbiaojieguo.setBiaoduanguid(bdInfo.getBiaoduanguid());
        cgZhongbiaojieguo.setSbrcode(cgTrustdeedinfo.getSbr_code());
        cgZhongbiaojieguo.setSbrname(cgTrustdeedinfo.getSbr_name());
        cgZhongbiaojieguo.setSbrunitguid(cgTrustdeedinfo.getSbr_unitguid());
        cgZhongbiaojieguo.setSbrunitname(cgTrustdeedinfo.getSbr_unitname());
        cgZhongbiaojieguo.setSbrtel(cgTrustdeedinfo.getSbr_tel());
        cgZhongbiaojieguo.setBiaoduanguid(bdInfo.getBiaoduanguid());
        cgZhongbiaojieguo.setProjectno(bdInfo.getProjectno());
        cgZhongbiaojieguo.setProjectname(bdInfo.getProjectname());
        cgZhongbiaojieguo.setFabaono(bdInfo.getFabaono());
        cgZhongbiaojieguo.setFabaoname(bdInfo.getFabaoname());
        cgZhongbiaojieguo.setBiaoduanno(bdInfo.getBiaoduanno());
        cgZhongbiaojieguo.setBiaoduanname(bdInfo.getBiaoduanname());
        cgZhongbiaojieguo.setJianshedanwei(bdInfo.getJianshedanwei());
        cgZhongbiaojieguo.setZhaobiaodaili(bdInfo.getDailiname());
        cgZhongbiaojieguo.setXiaqucode(bdInfo.getXiaqucode());
        cgZhongbiaojieguo.setProjectjiaoyitype(bdInfo.getProjectjiaoyitype());
        cgZhongbiaojieguo.setProjectguid(bdInfo.getProjectguid());
        cgZhongbiaojieguo.setJianshedanweiguid(bdInfo.getJianshedanweiguid());
        cgZhongbiaojieguo.setDailiguid(bdInfo.getDailiguid());

        JSONArray dsnResult = chengjiaoInfo.getJSONArray("dsnResult");
        for (Object object2 : dsnResult) {// 中标供应商子表
            JSONObject item = (JSONObject) object2;
            String danweiguid = service.queryString("select danweiguid from huiyuan_unitcominfo where code=?",
                    item.getString("supplyCode"));
            CgToubiaodanwei toubiaodanwei = service.find(
                    "select * from cg_toubiaodanwei where biaoduanguid=? and danweiguid=?", CgToubiaodanwei.class,
                    bdInfo.getBiaoduanguid(), danweiguid);
            if (toubiaodanwei == null || toubiaodanwei.isEmpty()) {
                return "中标单位有误，未找到报名信息！";
            }
            else {
                toubiaodanwei.setToubiaoprice(item.getDouble("winPrice"));
                toubiaodanwei.set("PingBiaoPrice", item.getDouble("winPrice"));
                toubiaodanwei.setIszhongbiao("1");
                service.update(toubiaodanwei);
                CgZhongbiaodetail zhongbiaodetail = service.find(
                        "select * from cg_zhongbiaodetail where biaoduanguid=? and danweiguid=?",
                        CgZhongbiaodetail.class, bdInfo.getBiaoduanguid(), toubiaodanwei.getDanweiguid());
                boolean isadd = false;
                if (zhongbiaodetail == null) {
                    zhongbiaodetail = new CgZhongbiaodetail();
                    isadd = true;
                    zhongbiaodetail.setRowguid(UUID.randomUUID().toString());
                    zhongbiaodetail.setGonggaoguid(zhongbiaodetail.getRowguid());
                    zhongbiaodetail.setBiaoduanguid(bdInfo.getBiaoduanguid());
                    zhongbiaodetail.setDanweiguid(danweiguid);
                }
                zhongbiaodetail.setDanweiname(item.getString("supplyName"));
                zhongbiaodetail.setDanweitype(ClsEnum.Sys_DanWeiType.投标人.getValue());
                zhongbiaodetail.setZhongbiaomoney(toubiaodanwei.getToubiaoprice());
                zhongbiaodetail.setZhongbiaoprice(toubiaodanwei.getToubiaoprice());
                zhongbiaodetail.setZhongbiaopricedw("1");
                zhongbiaodetail.setZbrunitorgnum(toubiaodanwei.getUnitorgnum());
                zhongbiaodetail.setPmname(toubiaodanwei.getPmname());
                zhongbiaodetail.setPmguid(toubiaodanwei.getPmguid());
                zhongbiaodetail.set("zhongbiaoservicemoney", 0);// 竞价类默认中标服务费0元

                if (isadd) {
                    service.insert(zhongbiaodetail);
                }
                else {
                    service.update(zhongbiaodetail);
                }

                CgZhongbiaotzs zhongbiaotzs = new CgZhongbiaotzs();
                // 发送中标通知书，201094表TZSType=1
                zhongbiaotzs.setTongzhishuno("ZBTZS" + bdInfo.getBiaoduanno());
                zhongbiaotzs.setTzstype("1");
                zhongbiaotzs.setTzscontent("");
                String strMessage = "【中标通知书】标段(包)名称：" + bdInfo.getBiaoduanname();
                // 保存中标价，数据交换用 张佩龙 2014-07-15
                zhongbiaotzs.setZhongbiaoprice(bdInfo.getZhongbiaomoney());
                // 保存价格单位，数据交换用。
                // 同一个标段的价格单位应该是一样的，在发送中标通知书时设置单位后，再发送招标结果通知书时可以不设置了，因为该控件已赋值
                // 张佩龙 2014-07-15
                zhongbiaotzs.setZhongbiaodanwei(bdInfo.getZhongbiaopricedw());
                // 保存中标人/未中标人代码，数据交换用 张佩龙 2014-07-15
                zhongbiaotzs.setZhongbiaorencode(toubiaodanwei.getUnitorgnum());
                zhongbiaotzs.setBiaoduanguid(bdInfo.getBiaoduanguid());
                zhongbiaotzs.setBiaoduanno(bdInfo.getBiaoduanno());
                zhongbiaotzs.setBiaoduanname(bdInfo.getBiaoduanname());
                zhongbiaotzs.setXiaqucode(bdInfo.getXiaqucode());
                zhongbiaotzs.setDanweiguid(toubiaodanwei.getDanweiguid());
                zhongbiaotzs.setSenddanweiguid(toubiaodanwei.getDanweiguid());
                zhongbiaotzs.setDanweiname(toubiaodanwei.getDanweiname());
                zhongbiaotzs.setSenddanweiname(toubiaodanwei.getDanweiname());
                // 代码项“金额币种代码”，人民币 156
                zhongbiaotzs.setChengjiaoremark("156");
                zhongbiaotzs.setSenddate(new Date());
                zhongbiaotzs.setRowguid(UUID.randomUUID().toString());
                service.insert(zhongbiaotzs);
                // 向该单位发送中标通知 向HuiYuan_AlertInfo表中插入数据
                DB_HuiYuan_AlertInfo.HuiYuan_AlertInfo_Add(toubiaodanwei.getDanweiguid(), toubiaodanwei.getDanweiname(),
                        "", strMessage, "", bdInfo.getSbrcode(),
                        DB_AllowTo.IsZBB() ? bdInfo.getSbrname() : ContextHelper.getDisplayName(), new Date(), "",
                        DB_HuiYuan_AlertInfo.MESSAGE_TYPE_TZS, zhongbiaotzs.getTzscontent(),
                        toubiaodanwei.getDanweitype(), bdInfo.getBiaoduanguid(), bdInfo.getProjectjiaoyitype());

                bdInfo.setZhongbiaopmname(toubiaodanwei.getPmname());
                bdInfo.setZhongbiaopmguid(toubiaodanwei.getPmguid());
                bdInfo.setZhongbiaodanweiname(toubiaodanwei.getDanweiname());
                bdInfo.setZhongbiaodanweiguid(toubiaodanwei.getDanweiguid());
                bdInfo.setZhongbiaoprice(toubiaodanwei.getToubiaoprice());
                bdInfo.setZhongbiaomoney(toubiaodanwei.getToubiaoprice());
                if (item.getJSONObject("files") != null) {
                    if (item.getJSONObject("files").getJSONArray("data") != null) {
                        for (Object o : item.getJSONObject("files").getJSONArray("data")) {
                            JSONObject data = (JSONObject) o;
                            erpFrameSysUtil.downloadFile(data, zhongbiaodetail.getRowguid(), GlobalParams.SF_中标通知书,
                                    service);
                        }
                    }
                }
            }
        }
        JSONArray dsnDetail = chengjiaoInfo.getJSONArray("dsnDetail");
        if (dsnDetail != null) {
            List<CgShoubiaoandbditem> old_List = service.findList(
                    "select * from CG_ShouBiaoAndBDItem where biaoduanguid=?", CgShoubiaoandbditem.class,
                    cgTrustdeedinfo.getBiaoDuanGuid());
            String uuid = UUID.randomUUID().toString();
            for (CgShoubiaoandbditem old : old_List) {// 先将老数据加个等备份。，然后删除新增
                AGCgShoubiaoandbditemhistory itemhistory = new AGCgShoubiaoandbditemhistory();
                itemhistory.setRowguid(UUID.randomUUID().toString());
                itemhistory.setBiaoduanguid(cgTrustdeedinfo.getBiaoDuanGuid());
                itemhistory.setTrustguid(cgTrustdeedinfo.getTrustguid());
                itemhistory.setGysdanweiguid(old.getDanweiguid());
                itemhistory.setAwardtype(old.getAwardtype());
                itemhistory.setAwardtotalprice(old.getAwardtotalprice());
                itemhistory.setQuasiawardtotalprice(old.getQuasiawardtotalprice());
                itemhistory.setAwardquantity(old.getAwardquantity());
                itemhistory.setAwardsinprice(old.getAwardsinprice());
                itemhistory.setItemguid(old.getItemguid());
                itemhistory.set("purResultLineId", old.getStr("purResultLineId"));// 采购实施结果明细号
                itemhistory.setShoubiaoguid(old.getShoubiaoguid());
                itemhistory.setOperatedate(new Date());
                itemhistory.setPviguid(uuid);// pviguid拿来当作表示同一批备份的数据
                service.insert(itemhistory);
                service.delete(old);
            }

            for (Object object2 : dsnDetail) {
                JSONObject detail = (JSONObject) object2;
                String sql = "select * from CG_DemandInventory where trustguid=? and bidEntrLineNo=?";
                CgDemandinventory qingdan = service.find(sql, CgDemandinventory.class, cgTrustdeedinfo.getTrustguid(),
                        detail.getString("itemCode"));
                if (qingdan == null || qingdan.isEmpty()) {
                    return "未查询到对应需求清单" + detail.toJSONString();
                }
                else {
                    HuiyuanUnitcominfo gysdw = new ERPFrameSysUtil()
                            .getHuiyuanUnitcominfo(detail.getString("supplierCode"), "", service);
                    CgShoubiaoandbditem item = new CgShoubiaoandbditem();
                    item.setRowguid(UUID.randomUUID().toString());
                    item.setBiaoduanguid(cgTrustdeedinfo.getBiaoDuanGuid());
                    if (gysdw != null) {
                        item.setDanweiguid(gysdw.getDanweiguid());
                    }
                    item.setAwardtype("2");
                    item.setItemguid(qingdan.getRowguid());
                    item.set("purResultLineId", detail.getString("materialNo"));
                    item.setAwardquantity(detail.getDouble("winNum"));// 中标数量
                    item.setAwardsinprice(detail.getDouble("winPrice"));// 中标单价

                    BigDecimal bidnum = new BigDecimal(detail.getString("winNum"));
                    BigDecimal bidPrice = new BigDecimal(detail.getString("winPrice"));
                    Double totaobidprice = bidnum.multiply(bidPrice).doubleValue();
                    item.setAwardtotalprice(totaobidprice);
                    item.setShoubiaoguid(cgTrustdeedinfo.getBiaoDuanGuid());
                    item.setOperatedate(new Date());
                    service.insert(item);
                }
            }
        }
        cgZhongbiaojieguo.setSbrdate(new Date());
        cgZhongbiaojieguo.setShrdate(new Date());
        cgZhongbiaojieguo.setAuditstatus("3");
        service.insert(cgZhongbiaojieguo);

        bdInfo.setZbjieguoisselected("1");
        // 反插标段表，主要是中标公示会插入的信息

        bdInfo.setZhongbiaopricedw("1");
        bdInfo.setDingbiaodate(new Date());
        service.update(bdInfo);

        return "";
    }

    /**
     * 成交公告
     */
    public String JJChengJiaoGGInfo(CgZhongbiaogs cgzhongbiaogs, CgTrustdeedinfo cgTrustdeedinfo,
            JSONObject chengjiaoggInfo, ZtbCommonDao service) {
        try {
            AGCgBiaoduaninfo bdInfo = service.find(AGCgBiaoduaninfo.class, cgTrustdeedinfo.getBiaoDuanGuid());
            String gonggaoguid = UUID.randomUUID().toString();
            cgzhongbiaogs.setSbrdate(new Date());
            cgzhongbiaogs.setRowguid(gonggaoguid);
            cgzhongbiaogs.setGonggaoguid(gonggaoguid);
            cgzhongbiaogs.setAuditstatus("3");
            cgzhongbiaogs.setShrdate(new Date());
            cgzhongbiaogs.setProjectguid(bdInfo.getProjectguid());
            cgzhongbiaogs.setSbrunitguid(cgTrustdeedinfo.getSbr_unitguid());
            cgzhongbiaogs.setSbrunitname(cgTrustdeedinfo.getSbr_unitname());
            cgzhongbiaogs.setGsfromdate(formatDateString(chengjiaoggInfo.getString("publishTime"), "yyyyMMddHHmmss"));
            cgzhongbiaogs.setGonggaotitle(chengjiaoggInfo.getString("title"));
            cgzhongbiaogs.setGonggaocontent(chengjiaoggInfo.getString("noticeUrl"));
            cgzhongbiaogs
                    .setGonggaorealfrom(formatDateString(chengjiaoggInfo.getString("publishTime"), "yyyyMMddHHmmss"));
            cgzhongbiaogs.setFaBuMeiTi(new FrameConfigService9().getFrameConfigValue("Bid_Ag_发布媒体"));
            cgzhongbiaogs.setShowbiaoduanname(bdInfo.getBiaoduanname());
            cgzhongbiaogs.setShowbiaoduanno(bdInfo.getBiaoduanno());
            service.insert(cgzhongbiaogs);

            bdInfo.setZhongbiaoisselected("1");
            service.update(bdInfo);

            CgZbgsandbd cgzbgsandbd = new CgZbgsandbd();
            cgzbgsandbd.setBiaoduanguid(bdInfo.getBiaoduanguid());
            cgzbgsandbd.setGonggaoguid(gonggaoguid);
            cgzbgsandbd.setOperatedate(new Date());
            cgzbgsandbd.setRowguid(UUID.randomUUID().toString());
            service.insert(cgzbgsandbd);

            if (chengjiaoggInfo.getJSONObject("files") != null) {
                if (chengjiaoggInfo.getJSONObject("files").getJSONArray("data") != null) {
                    for (Object o : chengjiaoggInfo.getJSONObject("files").getJSONArray("data")) {
                        JSONObject data = (JSONObject) o;
                        erpFrameSysUtil.downloadFile(data, gonggaoguid, "J115", service);
                    }
                }
            }
        }
        catch (Exception e) {
            return e.getMessage();
        }
        return "";
    }

    /**
     * 流标信息通知
     */
    public String JJLiuBiaoInfo(CgYichangbdhistory cgYichangbdhistory, CgTrustdeedinfo cgTrustdeedinfo,
            JSONObject liubiaoInfo, ZtbCommonDao service) {
        AGCgBiaoduaninfo bdInfo = service.find(AGCgBiaoduaninfo.class, cgTrustdeedinfo.getBiaoDuanGuid());
        String shoubiaoRowguid = UUID.randomUUID().toString();
        cgYichangbdhistory.setSbrdate(new Date());
        cgYichangbdhistory.setRowguid(shoubiaoRowguid);
        cgYichangbdhistory.setSbrcode("");
        cgYichangbdhistory.setSbrname("");
        cgYichangbdhistory.setAuditstatus("3");
        cgYichangbdhistory.setSbrunitguid("companyCode");
        cgYichangbdhistory.setSbrunitname("");
        cgYichangbdhistory.setShrdate(EpointDateUtil.convertDate2String(new Date(), "yyyy-MM-dd HH:mm:ss"));
        cgYichangbdhistory.setIssendmsg("1");// 默认情况通知人
        cgYichangbdhistory.setIssendweb("0");// 默认情况不发布公告
        cgYichangbdhistory.setYichangguid(shoubiaoRowguid);
        cgYichangbdhistory.setRemark("项目流标");
        cgYichangbdhistory.setAuditstatus(AuditStatus.审核通过.getValue());
        cgYichangbdhistory.setProjectno(bdInfo.getProjectno());
        cgYichangbdhistory.setFabaoguid(bdInfo.getFabaoguid());
        cgYichangbdhistory.setProjectname(bdInfo.getProjectname());
        cgYichangbdhistory.setBiaoduanno(bdInfo.getBiaoduanno());
        cgYichangbdhistory.setBiaoduanname(bdInfo.getBiaoduanname());
        cgYichangbdhistory.setFabaoname(bdInfo.getFabaoname());
        cgYichangbdhistory.setOrgguid(bdInfo.getOrgguid());
        cgYichangbdhistory.setXiaqucode(bdInfo.getXiaqucode());
        cgYichangbdhistory.setFabaono(bdInfo.getFabaono());
        cgYichangbdhistory.setBiaoduanguid(bdInfo.getBiaoduanguid());
        cgYichangbdhistory.setProjectjiaoyitype(bdInfo.getProjectjiaoyitype());
        cgYichangbdhistory.setJianshedanwei(bdInfo.getJianshedanwei());
        cgYichangbdhistory.setDailiname(bdInfo.getDailiname());
        cgYichangbdhistory.setOldzhaobiaofangshi(bdInfo.getZhaobiaofangshi());

        String type = liubiaoInfo.getString("type");
        if ("0".equals(type)) {
            cgYichangbdhistory.setChuliresault("6");
            cgYichangbdhistory.set("liubiaotype", "3");
        }
        else if ("1".equals(type)) {
            cgYichangbdhistory.setChuliresault("2");
        }
        service.insert(cgYichangbdhistory);

        // 将标段中选择状态位置为1
        bdInfo.setYichangisselected("1");
        service.update(bdInfo);

        new AGQYZtb().DealYiChangBiaoDuan(shoubiaoRowguid);

        return "";
    }

    private String checkQingDanHasZero(String purchasetype, JSONObject modifInfo, Boolean isJingJia) {
        JSONArray details = null;
        if ("A".equalsIgnoreCase(purchasetype)) {
            details = modifInfo.getJSONArray("cgnEngDetails");
        }
        else if ("M".equalsIgnoreCase(purchasetype)) {
            details = modifInfo.getJSONArray("cgnMaterialDetails");
            if (details == null) {
                details = modifInfo.getJSONArray("cgnDeviceDetails");
            }
        }
        else if ("S".equalsIgnoreCase(purchasetype)) {
            details = modifInfo.getJSONArray("cgnServiceDetail");
        }
        if (details != null && !details.isEmpty()) {
            for (Object object : details) {
                JSONObject detail = (JSONObject) object;
                Double planPrice = detail.getDouble("planPrice");
                if ("S".equalsIgnoreCase(purchasetype)) {
                    planPrice = detail.getDouble("PlannedPrice");
                }
                if ((planPrice == null || planPrice == 0.00) && !isJingJia) {
                    return "委托单存在计划单价为0或为空";
                }
            }
        }
        else {
            return "委托单存在计划单价为0或为空";
        }

        return "";
    }

    private void insertQingDan(String purchasetype, String trustGuid, JSONObject modifInfo, ZtbCommonDao service) {
        JSONArray details;
        Date now = new Date();
        if ("A".equalsIgnoreCase(purchasetype)) {
            details = modifInfo.getJSONArray("cgnEngDetails");
            for (Object object : details) {
                JSONObject detail = (JSONObject) object;
                CgDemandinventory qingdan = new CgDemandinventory();
                qingdan.setRowguid(UUID.randomUUID().toString());
                qingdan.setOperatedate(now);
                qingdan.setTrustguid(trustGuid);
                qingdan.setERPXuHao(formatInt(detail.getString("serialno")));
                qingdan.setBidEntrLineNo(formatString(detail.getString("orderNo")));// 序号
                qingdan.setBidEntrLineCode(formatString(detail.getString("orderNo")));// 序号
                qingdan.setServicecode(formatString(detail.getString("engineeringNo")));// 工程编码
                qingdan.setServicename(formatString(detail.getString("engineeringName")));// 工程名称
                qingdan.setServicestart(formatDateString(detail.getString("startDate"), "yyyyMMddHHmmss"));
                qingdan.setServiceend(EpointDateUtil
                        .convertDate2String(formatDateString(detail.getString("endDate"), "yyyyMMddHHmmss")));
                qingdan.setServicerequest(formatString(detail.getString("standard")));// 标准规范
                qingdan.setForecastprice(detail.getDouble("yucePrice"));// 预测价单价
                qingdan.setBiddingquantity(detail.getDouble("detailCount"));// 数量
                qingdan.setUnits(formatString(detail.getString("measureUnit")));// 计量单位
                qingdan.setCeilingprice(detail.getDouble("highestPrice"));// 最高限价
                qingdan.setBaseprice(detail.getDouble("bidBottom"));// 标底
                qingdan.set("fuwuplace", detail.getString("servicePlace"));// 服务地点
                qingdan.setPlannedprice(detail.getDouble("planPrice"));// 计划价格
                qingdan.setTuzhino(formatString(detail.getString("diagramNo")));// 图纸号
                qingdan.setSuppliersnum(detail.getInteger("SuppliersNum"));// 拟中标家数
                qingdan.setShejizhuanye(formatString(detail.getString("profession")));// 涉及专业
                qingdan.setGongqi(formatString(detail.getString("days")));// 工期
                qingdan.setXuyongdanwei(formatString(detail.getString("assetOrgName")));// 产权单位
                qingdan.setRemark(formatString(detail.getString("notes")));// 备注
                service.insert(qingdan);
            }
        }
        else if ("M".equalsIgnoreCase(purchasetype)) {
            details = modifInfo.getJSONArray("cgnMaterialDetails");
            if (details == null) {
                details = modifInfo.getJSONArray("cgnDeviceDetails");
            }
            if (details != null) {
                for (Object object : details) {
                    JSONObject detail = (JSONObject) object;
                    CgDemandinventory qingdan = new CgDemandinventory();
                    qingdan.setRowguid(UUID.randomUUID().toString());
                    qingdan.setOperatedate(now);
                    qingdan.setTrustguid(trustGuid);
                    qingdan.setERPXuHao(formatInt(detail.getString("serialno")));
                    qingdan.setBidEntrLineNo(formatString(detail.getString("orderNo")));// 序号
                    qingdan.setBidEntrLineCode(formatString(detail.getString("purchaseSolutionNo")));// 采购方案号
                    qingdan.setServicecode(formatString(detail.getString("materialNo")));// 工程编码
                    qingdan.setServicename(formatString(detail.getString("materialName")));// 工程名称
                    qingdan.setGuigexinghao(formatString(detail.getString("materialSpecification")));// 规格
                    qingdan.setCaizhi(formatString(detail.getString("materialIngredient")));// 材质
                    qingdan.setBrand(formatString(detail.getString("materialBrand")));// 品牌
                    qingdan.setQualitystandard(formatString(detail.getString("materialQualifyStandard")));// 质量标准
                    qingdan.setBiddingquantity(detail.getDouble("purchaseCount"));// 采购数量
                    qingdan.setUnits(formatString(detail.getString("measureUnit")));// 计量单位
                    qingdan.setForecastprice(detail.getDouble("referPrice"));// 预测价单价
                    qingdan.setShangqiprice(detail.getDouble("lastPrice"));// 上期价格
                    qingdan.setCeilingprice(detail.getDouble("highestPrice"));// 最高限价
                    qingdan.setBaseprice(detail.getDouble("bidBottom"));// 标底
                    qingdan.setPlannedprice(detail.getDouble("planPrice"));// 计划价格
                    qingdan.setTuijiannum(detail.getInteger("recommendCount"));// 推荐家数
                    qingdan.setSuppliersnum(detail.getInteger("supplyCount"));// 拟中标家数
                    qingdan.setServicestart(formatDateString(detail.getString("startDate"), "yyyyMMddHHmmss"));
                    qingdan.setServiceend(EpointDateUtil
                            .convertDate2String(formatDateString(detail.getString("endDate"), "yyyyMMddHHmmss")));
                    qingdan.setJiaohuoplace(formatString(detail.getString("deliveryPlace")));// 交货地点
                    qingdan.setXuyongdanwei(formatString(detail.getString("demandOrg")));// 需用单位
                    if (StringUtil.isNotBlank(detail.getString("materialDetail"))) {
                        qingdan.setServicecontent(formatString(detail.getString("materialDetail")));// 物料详细描述
                    }
                    else {
                        qingdan.setServicecontent(formatString(detail.getString("materialExplain")));// 描述
                    }
                    qingdan.setRemark(formatString(detail.getString("notes")));// 备注
                    service.insert(qingdan);
                }
            }
        }
        else if ("S".equalsIgnoreCase(purchasetype)) {
            details = modifInfo.getJSONArray("cgnServiceDetail");
            for (Object object : details) {
                JSONObject detail = (JSONObject) object;
                CgDemandinventory qingdan = new CgDemandinventory();
                qingdan.setRowguid(UUID.randomUUID().toString());
                qingdan.setOperatedate(now);
                qingdan.setTrustguid(trustGuid);
                qingdan.setERPXuHao(formatInt(detail.getString("serialno")));
                qingdan.setBidEntrLineNo(formatString(detail.getString("orderNo")));// 序号
                qingdan.setBidEntrLineCode(formatString(detail.getString("purchaseSolutionNo")));// 采购方案号
                qingdan.setServicecode(formatString(detail.getString("serviceNo")));// 工程编码
                qingdan.setServicename(formatString(detail.getString("serviceName")));// 工程名称
                qingdan.setServicestart(formatDateString(detail.getString("startDate"), "yyyyMMddHHmmss"));
                qingdan.setServiceend(EpointDateUtil
                        .convertDate2String(formatDateString(detail.getString("endDate"), "yyyyMMddHHmmss")));
                qingdan.setYanshoubiaozhun(formatString(detail.getString("standard")));// 验收标准
                qingdan.setXiufugongyi(formatString(detail.getString("repairRequirements")));// 修复工艺
                qingdan.setBiddingquantity(detail.getDouble("serviceCount"));// 数量
                qingdan.setUnits(formatString(detail.getString("measureUnit")));// 计量单位
                qingdan.setCeilingprice(detail.getDouble("highestPrice"));// 最高限价
                qingdan.setBaseprice(detail.getDouble("bidBottom"));// 标底价
                qingdan.setForecastprice(detail.getDouble("yucePrice"));// 预测价
                qingdan.setPlannedprice(detail.getDouble("PlannedPrice"));// 计划价
                qingdan.setShangqiprice(detail.getDouble("lastPrice"));// 上期价格
                qingdan.setTuijiannum(detail.getInteger("recommendCount"));// 推荐家数
                qingdan.setSuppliersnum(detail.getInteger("SuppliersNum"));// 保供家数
                qingdan.setBugname(formatString(detail.getString("defectName")));// 缺陷
                qingdan.setYaoqiumarks(formatString(detail.getString("requirement")));// 对承修方的要求
                qingdan.setCaizhi(formatString(detail.getString("materialIngredient")));// 材质
                qingdan.setJiaohuoplace(formatString(detail.getString("deliveryPlace")));// 交货地点
                qingdan.setXiufubiaozhun(formatString(detail.getString("repairStandard")));// 修复标准
                qingdan.setQualitystandard(formatString(detail.getString("materialQualifyStandard")));// 质量标准
                qingdan.setGuigexinghao(formatString(detail.getString("materialSpecification")));// 规格
                qingdan.setXuyongdanwei(formatString(detail.getString("demandOrg")));// 需用单位
                qingdan.setFuwuplace(formatString(detail.getString("servicePlace")));// 服务地点
                qingdan.setServicecontent(formatString(detail.getString("serviceContent")));// 服务内容
                if ("6.11.51".equals(SupplyTypeId)) {
                    qingdan.setServicerequest(formatString(detail.getString("materialQualifyStandard")));// 标准规范
                }
                else {
                    qingdan.setServicerequest(formatString(detail.getString("serviceDemand")));// 标准规范
                }
                qingdan.setRemark(formatString(detail.getString("notes")));// 备注
                service.insert(qingdan);
            }
        }
    }

    private void dealTrustdeedinfoplus(CgTrustdeedinfo cgTrustdeedinfo, CgTrustdeedinfoplus cgTrustdeedinfoplus,
            JSONObject modifInfo, ZtbCommonDao service) {
        JSONObject consignation = modifInfo.getJSONObject("consignation");
        cgTrustdeedinfoplus.setTechnicalrequirements(formatString(consignation.getString("techDemand")));// 技术质量要求
        cgTrustdeedinfo.set("IsTechnicalRequirements",
                StringUtil.isNotBlank(cgTrustdeedinfoplus.getTechnicalrequirements()) ? "1" : "0");
        cgTrustdeedinfoplus.setBusinessrequirements(formatString(consignation.getString("bizDemand")));// 商务要求
        cgTrustdeedinfo.set("IsBusinessRequirements",
                StringUtil.isNotBlank(cgTrustdeedinfoplus.getBusinessrequirements()) ? "1" : "0");
        if (StringUtil.isNotBlank(consignation.getString("registerFund"))) {
            cgTrustdeedinfoplus.setRegisteredcapital(Double.parseDouble(consignation.getString("registerFund").trim()));// 注册资金
        }
        if (StringUtil.isNotBlank(consignation.getString("registerFundLt"))) {
            cgTrustdeedinfoplus
                    .setRegisteredcapitallt(Double.parseDouble(consignation.getString("registerFundLt").trim()));// 注册资金
        }
        cgTrustdeedinfo.set("IsAchieveRequire",
                StringUtil.isNotBlank(consignation.getString("IsAchieveRequire"))
                        ? consignation.getString("IsAchieveRequire")
                        : "0");
        if (StringUtil.isNotBlank(cgTrustdeedinfo.getSupplytypeid())
                && "6.7.22;6.7.23;6.7.25;".contains(cgTrustdeedinfo.getSupplytypeid())) {
            if (StringUtil.isNotBlank(consignation.getString("zjlzige"))) {
                // zjlzige 总监理工程师的资格要求 jlgcsyaoqiu
                cgTrustdeedinfo.set("isjlgcsyaoqiu", "1");

            }
            cgTrustdeedinfoplus.setJlgcsyaoqiu(formatString(consignation.getString("zjlzige")));
            if (StringUtil.isNotBlank(consignation.getString("financial"))) {
                cgTrustdeedinfo.set("IsCaiWuRequire", "1");
                // financial 财务要求 CaiWuRequire

            }
            cgTrustdeedinfoplus.setCaiWuRequire(formatString(consignation.getString("financial")));
            if (StringUtil.isNotBlank(consignation.getString("xinyuyaoqiu"))) {
                cgTrustdeedinfo.set("isxinyuyaoqiu", "1");
                // xinyuyaoqiu 信誉要求 xinyuyaoqiu

            }
            cgTrustdeedinfoplus.setXinyuyaoqiu(formatString(consignation.getString("xinyuyaoqiu")));
            if (StringUtil.isNotBlank(consignation.getString("susongqingkuang"))) {
                cgTrustdeedinfo.set("issusongqingkuang", "1");
                // susongqingkuang 诉讼情况要求 susongqingkuang

            }
            cgTrustdeedinfoplus.setSusongqingkuang(formatString(consignation.getString("susongqingkuang")));
            if (StringUtil.isNotBlank(consignation.getString("fzrzige"))) {
                cgTrustdeedinfo.set("isprofzryaoqiu", "1");
                // fzrzige 项目负责人资格要求 profzryaoqiu

            }
            cgTrustdeedinfoplus.setProfzryaoqiu(formatString(consignation.getString("fzrzige")));
            if (StringUtil.isNotBlank(consignation.getString("othermenyaoqiu"))) {
                cgTrustdeedinfo.set("isothermanyaoqiu", "1");
                // fzrzige 项目负责人资格要求 profzryaoqiu

            }
            cgTrustdeedinfoplus.setProfzryaoqiu(formatString(consignation.getString("othermenyaoqiu")));
            if (StringUtil.isNotBlank(consignation.getString("kcsbyaoqiu"))) {
                cgTrustdeedinfo.set("iskcshebeiyaoqiu", "1");
                // kcsbyaoqiu 勘察设备要求 kcshebeiyaoqiu

            }
            cgTrustdeedinfoplus.setKcshebeiyaoqiu(formatString(consignation.getString("kcsbyaoqiu")));
            if (StringUtil.isNotBlank(consignation.getString("yiqiyaoqiu"))) {
                cgTrustdeedinfo.set("issyjcyiqiyaoqiu", "1");
                // yiqiyaoqiu 试验检测仪器设备要求 syjcyiqiyaoqiu

            }
            cgTrustdeedinfoplus.setSyjcyiqiyaoqiu(formatString(consignation.getString("yiqiyaoqiu")));

        }
        cgTrustdeedinfoplus.setAchieverequire(formatString(consignation.getString("performanceDemand")));// 业绩要求
        cgTrustdeedinfo.set("IsAbilityRequire",
                StringUtil.isNotBlank(consignation.getString("IsAbilityRequire"))
                        ? consignation.getString("IsAbilityRequire")
                        : "0");
        cgTrustdeedinfoplus.setAbilityrequire(formatString(consignation.getString("capabilityDemand")));// 能力要求
        cgTrustdeedinfo.set("IsFinancialSituation",
                StringUtil.isNotBlank(consignation.getString("IsFinancialSituation"))
                        ? consignation.getString("IsFinancialSituation")
                        : "0");
        cgTrustdeedinfoplus.setFinancialsituation(formatString(consignation.getString("financial")));// 财务状况
        cgTrustdeedinfo.set("IsOtherRequires",
                StringUtil.isNotBlank(consignation.getString("IsOtherRequires"))
                        ? consignation.getString("IsOtherRequires")
                        : "0");
        cgTrustdeedinfoplus.setOtherrequires(formatString(consignation.getString("others")));// 其他

        cgTrustdeedinfoplus.setYuCeDescription(formatString(consignation.getString("bidBottomExplain")));// 标底/预测价说明
        cgTrustdeedinfoplus.setBasebiddescription(formatString(consignation.getString("bidBottomExplain")));// 标底/预测价说明
        cgTrustdeedinfoplus
                .setFbyaoqiuandfanwei("发包人要求：" + formatString(consignation.getString("contractorRequirements"))
                        + ";招标范围" + formatString(consignation.getString("bidRange")));// 拼接发包人要求contractorRequirements与招标范围bidRange
        if ("A".equalsIgnoreCase(cgTrustdeedinfo.getPurchasetype())) {
            JSONObject cgnEngProfile = modifInfo.getJSONObject("cgnEngProfile");
            if (StringUtil.isBlank(consignation.getString("bidRange"))
                    && StringUtil.isBlank(consignation.getString("contractorRequirements"))) {
                cgTrustdeedinfoplus
                        .setFbyaoqiuandfanwei("发包人要求：" + formatString(cgnEngProfile.getString("contractorRequirements"))
                                + ";招标范围" + formatString(cgnEngProfile.getString("bidRange")));// 拼接发包人要求contractorRequirements与招标范围bidRange
            }

            if (cgnEngProfile != null) {
                String zhaobiaofanwei = "图纸："
                        + (StringUtil.isBlank(formatString(cgnEngProfile.getString("drawing"))) ? "无"
                                : formatString(cgnEngProfile.getString("drawing")))
                        + ";招标范围：" + (StringUtil.isBlank(formatString(cgnEngProfile.getString("bidScope"))) ? "无"
                                : formatString(cgnEngProfile.getString("bidScope")));
                ;
                cgTrustdeedinfoplus.setZhaobiaofanwei(zhaobiaofanwei);// 拼接图纸drawing及招标范围bidScope
                cgTrustdeedinfoplus.setGcreleaseno(formatString(cgnEngProfile.getString("passFileNo")));// 放行文号
                cgTrustdeedinfoplus.setGcimplementationno(formatString(cgnEngProfile.getString("carryOutFileNo")));// 实施文号
                // FrameOu chanquandw = new ERPFrameSysUtil()
                // .getFrameOuByOuCode(formatString(consignation.getString("assetOrg")),
                // service);
                cgTrustdeedinfoplus.setGcpropertydanwei(formatString(cgnEngProfile.getString("assetOrg")));// 产权单位
                cgTrustdeedinfoplus.setGcprojectno(formatString(cgnEngProfile.getString("engineeringCode")));// 工程概况编码
                cgTrustdeedinfoplus.setGcprojectname(formatString(cgnEngProfile.getString("engineeringName")));// 工程名称
                cgTrustdeedinfoplus.setNonplace(formatString(cgnEngProfile.getString("engineeringPlace")));// 工程地点
                cgTrustdeedinfoplus.setNonservicestart(
                        formatDateString(cgnEngProfile.getString("planStartTime"), "yyyyMMddHHmmss"));// 计划开始时间
                cgTrustdeedinfoplus
                        .setNonserviceend(formatDateString(cgnEngProfile.getString("planEndTime"), "yyyyMMddHHmmss"));// 计划竣工日期
                cgTrustdeedinfoplus.setGcsurveysite(formatString(cgnEngProfile.getString("scoutPlace")));// 踏勘地点
                cgTrustdeedinfoplus
                        .setGcsurveydate(formatDateString(cgnEngProfile.getString("scoutDate"), "yyyyMMddHHmmss"));// 踏勘日期
                cgTrustdeedinfoplus.setGccontractingmode(formatString(cgnEngProfile.getString("undertakeType")));// 承包方式
                cgTrustdeedinfoplus.setNondays(formatString(cgnEngProfile.getString("demandDays")));// 要求工期
                cgTrustdeedinfoplus.setNonpremeetplace(formatString(cgnEngProfile.getString("meetingPlace")));// 预备会议地点
                cgTrustdeedinfoplus
                        .setNonpremeetdate(formatDateString(cgnEngProfile.getString("meetingDate"), "yyyyMMddHHmmss"));// 预备会日期
                cgTrustdeedinfoplus.setGcprojectplanner(formatString(cgnEngProfile.getString("planner")));// 工程计划员
                cgTrustdeedinfoplus.setGcplannerphone(formatString(cgnEngProfile.getString("plannerTel")));// 计划人联系电话
                cgTrustdeedinfoplus.setGcdanweilxr(formatString(cgnEngProfile.getString("assetOrgLinkman")));// 产权单位联系人
                cgTrustdeedinfoplus.setGcdanweilxrphone(formatString(cgnEngProfile.getString("assetOrgLinkmanTel")));// 产权单位联系人电话
                cgTrustdeedinfoplus.setGcprojectfzr(formatString(cgnEngProfile.getString("engineeringManager")));// 工程负责人
                cgTrustdeedinfoplus
                        .setGcprojectfzrphone(formatString(cgnEngProfile.getString("engineeringManagerTel")));// 工程负责人电话
                // cgTrustdeedinfoplus.setSupplymodeofshebei(codeservice.getItemTextByCodeName(
                // "bid_bengangerp_deviceSupplyType",
                // formatString(cgnEngProfile.getString("deviceSupplyType"))));//
                // 设备材料供应方式
                cgTrustdeedinfoplus.setSupplymodeofshebei(formatString(cgnEngProfile.getString("deviceSupplyType")));// 设备材料供应方式
                cgTrustdeedinfoplus.setGccontracttype(codeservice.getItemTextByCodeName("bid_bengangerp_contractType",
                        formatString(cgnEngProfile.getString("contractType"))));// 合同类型
                cgTrustdeedinfoplus.setGcchangesandcost(formatString(cgnEngProfile.getString("designChangeRule")));// 设计变更及费用规定
                cgTrustdeedinfoplus.setGccostanddes(formatString(cgnEngProfile.getString("feeInstruction")));// 施工费用及说明
                cgTrustdeedinfoplus.setGcunforeseenthings(formatString(cgnEngProfile.getString("ventureRule")));// 不可预见风景及规定
                cgTrustdeedinfoplus.setGcotherthings(formatString(cgnEngProfile.getString("otherRule")));// 其他事项说明
                cgTrustdeedinfoplus.setIsGCLQingDanFile(formatString(cgnEngProfile.getString("boq")));// 工程量清单
                cgTrustdeedinfoplus.setIsFaBaoRenFile(formatString(cgnEngProfile.getString("providedContractor")));// 发包人提供的资料
                cgTrustdeedinfoplus.setIsAllowFenBao(formatString(cgnEngProfile.getString("subpackage")));// 是否分包
            }
        }
        else if ("S".equalsIgnoreCase(cgTrustdeedinfo.getPurchasetype())) {
            JSONObject cgnServiceProfile = modifInfo.getJSONObject("cgnServiceProfile");
            if (cgnServiceProfile != null) {
                // 工程类服务，勘察设计监理
                if (StringUtil.isNotBlank(cgTrustdeedinfo.getSupplytypeid())
                        && "6.7.22;6.7.23;6.7.25;".contains(cgTrustdeedinfo.getSupplytypeid())) {
                    FrameOu cqDanWei = erpFrameSysUtil
                            .getFrameOuByOuCode(formatString(cgnServiceProfile.getString("chanquandanwei")), service);
                    if (cqDanWei != null && !cqDanWei.isEmpty()) {
                        cgTrustdeedinfoplus.setGcpropertydanwei(cqDanWei.getOuname());
                        cgTrustdeedinfoplus.setGcpropertydanweiguid(cqDanWei.getOuguid());
                    }
                    cgTrustdeedinfoplus.setGcprojectname(formatString(cgnServiceProfile.getString("projectname")));
                    cgTrustdeedinfoplus.setNonplace(formatString(cgnServiceProfile.getString("projectplace")));
                    cgTrustdeedinfoplus.setProjectguimo(formatString(cgnServiceProfile.getString("projectguimo")));// 项目建设规模
                    cgTrustdeedinfoplus.setNonplace(cgnServiceProfile.getString("servicePlace"));// 服务地点
                    // 计划开工日期
                    if (StringUtil.isNotBlank(cgnServiceProfile.getString("planStartDate"))) {
                        cgTrustdeedinfoplus.setNonservicestart(
                                formatDateString(cgnServiceProfile.getString("planStartDate"), "yyyyMMddHHmmss"));
                    }
                    else {
                        cgTrustdeedinfoplus.setNonservicestart(
                                formatDateString(cgnServiceProfile.getString("planstartdate"), "yyyyMMddHHmmss"));
                    }
                    // 计划竣工日期
                    cgTrustdeedinfoplus.setNonserviceend(
                            formatDateString(cgnServiceProfile.getString("planEndDate"), "yyyyMMddHHmmss"));
                    cgTrustdeedinfoplus.setServiceterm(formatString(cgnServiceProfile.getString("servicedays")));// 勘察/设计/监理服务期限
                    cgTrustdeedinfoplus.setNondays(formatString(cgnServiceProfile.getString("jianshedays")));// 建设周期
                    cgTrustdeedinfoplus
                            .setNonpremeetplace(formatString(cgnServiceProfile.getString("NonPreMeetPlace")));// 预备会地点
                    cgTrustdeedinfoplus.setNonpremeetdate(
                            formatDateString(cgnServiceProfile.getString("NonPreMeetDate"), "yyyyMMddHHmmss"));// 预备会时间
                    cgTrustdeedinfoplus.setGcsurveysite(cgnServiceProfile.getString("takanplace"));// 踏勘地点
                    // 踏勘时间
                    cgTrustdeedinfoplus.setGcsurveydate(
                            formatDateString(cgnServiceProfile.getString("takandate"), "yyyyMMddHHmmss"));
                    cgTrustdeedinfoplus.setGcprojectplanner(formatString(cgnServiceProfile.getString("projihuayuan")));// 工程计划员
                    cgTrustdeedinfoplus.setGcplannerphone(formatString(cgnServiceProfile.getString("projhymobile")));// 工程计划员联系方式
                    cgTrustdeedinfoplus.setGcdanweilxr(formatString(cgnServiceProfile.getString("chanquandanweilxr")));// 产权单位联系人
                    cgTrustdeedinfoplus
                            .setGcdanweilxrphone(formatString(cgnServiceProfile.getString("chanquandanweimobile")));// 产权单位联系人电话
                    cgTrustdeedinfoplus.setIsAllowFenBao(formatString(cgnServiceProfile.getString("isallowfabao")));// 是否允许发包
                    cgTrustdeedinfoplus.setYaoqiuandfanwei(formatString(cgnServiceProfile.getString("weituozbfw")));// 委托人要求及招标范围
                    cgTrustdeedinfoplus.setFbyaoqiuandfanwei(formatString(cgnServiceProfile.getString("faobaozbfw")));// 发包人要求及招标范围
                    if (StringUtil.isNotBlank(cgnServiceProfile.getString("chengguobuchang"))) {
                        cgTrustdeedinfoplus.setIsJishubuchang("1");
                    }
                    else {
                        cgTrustdeedinfoplus.setIsJishubuchang("0");
                    }
                    cgTrustdeedinfoplus.setJishubuchang(formatString(cgnServiceProfile.getString("chengguobuchang")));// 技术成果经济补偿
                    cgTrustdeedinfoplus.setGongchengfei(cgnServiceProfile.getDouble("yusuanjine"));// 建筑安装工程费/工程概算投资额（元）
                    cgTrustdeedinfoplus.setProjectgusuan(cgnServiceProfile.getDouble("touzigusuan"));// 项目投资估算
                    cgTrustdeedinfoplus.setGcprojectfzr(formatString(cgnServiceProfile.getString("profzr")));// 工程负责人
                    cgTrustdeedinfoplus.setGcprojectfzrphone(formatString(cgnServiceProfile.getString("profzrmobile")));// 工程负责人联系方式
                }
                else {// assetOrgId 产权组织
                      // assetOrgName 产权单位
                    FrameOu cqDanWei = erpFrameSysUtil
                            .getFrameOuByOuCode(formatString(cgnServiceProfile.getString("assetOrgId")), service);
                    if (cqDanWei != null && !cqDanWei.isEmpty()) {
                        cgTrustdeedinfoplus.setGcpropertydanwei(cqDanWei.getOuname());
                        cgTrustdeedinfoplus.setGcpropertydanweiguid(cqDanWei.getOuguid());
                    }
                    cgTrustdeedinfoplus.setNonplace(formatString(cgnServiceProfile.getString("servicePlace")));// 服务地点
                    cgTrustdeedinfoplus.setNonservicestart(
                            formatDateString(cgnServiceProfile.getString("planStartDate"), "yyyyMMddHHmmss"));// 计划开始时间
                    cgTrustdeedinfoplus.setNonserviceend(
                            formatDateString(cgnServiceProfile.getString("planEndDate"), "yyyyMMddHHmmss"));// 计划竣工日期
                    // undertakeType承包方式
                    cgTrustdeedinfoplus.setNondays(formatString(cgnServiceProfile.getString("serviceDays")));// 服务工期
                    // comtractType 合同类型
                    cgTrustdeedinfoplus.setNonpremeetplace(
                            formatString(cgnServiceProfile.getString("meetingPlace"), "yyyyMMddHHmmss"));// 预备会议地点
                    cgTrustdeedinfoplus.setNonpremeetdate(
                            formatDateString(cgnServiceProfile.getString("meetingDate"), "yyyyMMddHHmmss"));// 预备会日期
                    // 计划员
                    // 计划人联系电话
                    // 产权单位联系人
                    // 产权单位联系人电话
                    cgTrustdeedinfoplus.setGcprojectplanner(formatString(cgnServiceProfile.getString("planner")));// 工程计划员
                    cgTrustdeedinfoplus.setGcplannerphone(formatString(cgnServiceProfile.getString("plannerTel")));// 工程计划员联系方式
                    cgTrustdeedinfoplus.setGcdanweilxr(formatString(cgnServiceProfile.getString("assetOrgLinkman")));// 产权单位联系人
                    cgTrustdeedinfoplus
                            .setGcdanweilxrphone(formatString(cgnServiceProfile.getString("assetOrgLinkmanTel")));// 产权单位联系人电话
                    // compensation 服务费用承担和设计成果补偿
                    if (StringUtil.isNotBlank(cgnServiceProfile.getString("compensation"))) {
                        cgTrustdeedinfoplus.setIsJishubuchang("1");
                    }
                    else {
                        cgTrustdeedinfoplus.setIsJishubuchang("0");
                    }
                    cgTrustdeedinfoplus.setJishubuchang(formatString(cgnServiceProfile.getString("compensation")));// 技术成果经济补偿
                    // compensationStandard 补偿标准
                    cgTrustdeedinfoplus.setProjectgusuan(cgnServiceProfile.getDouble("invest"));// 计划投资
                    cgTrustdeedinfo.setProjectleader(formatString(cgnServiceProfile.getString("serviceManager")));// 工程负责人
                    cgTrustdeedinfo
                            .setProjectleaderphone(formatString(cgnServiceProfile.getString("serviceManagerTel")));// 工程负责人电话
                }
            }
        }
    }

    public String insertGys(JSONObject cgnSupplier, String trustGuid, ZtbCommonDao service) {
        CgRecommendedsupplier gys = new CgRecommendedsupplier();
        gys.setRowguid(UUID.randomUUID().toString());
        gys.setOperatedate(new Date());
        gys.setTrustguid(trustGuid);
        gys.setNinescode(formatString(cgnSupplier.getString("supplyCode")));// 9s码
        AGHuiyuanUnitcominfo gysuser = new ERPFrameSysUtil()
                .getHuiyuanUnitcominfo(formatString(cgnSupplier.getString("supplyCode")), "", service);
        if (gysuser != null && !gysuser.isEmpty()) {
            gys.setOperateusername("系统管理员");
            gys.setGysDanWeiGuid(gysuser.getDanweiguid());
            gys.setRegisteredcapital(gysuser.getZhuceziben());
            gys.setMdmcode(gysuser.getMdmcode());

            HuiYuanUser huiYuanUser = service.find("select * from HuiYuan_User where danweiguid = ?", HuiYuanUser.class,
                    gysuser.getDanweiguid());
            AGHTBRBasicInfo basicinfo = service.find("select * from h_tbr_basicinfo where danweiguid=?",
                    AGHTBRBasicInfo.class, gysuser.getDanweiguid());
            if (basicinfo != null) {
                gys.setIsgenpayer(basicinfo.getTaxtype());// 是否一般纳税人
            }
            if (huiYuanUser != null) {
                gys.setSupplierlxr(huiYuanUser.getDisplayname());
                gys.setSupplierphone(huiYuanUser.getMobilephone());
            }
        }
        else {
            logger.info("接受传输信息发生异常,异常原因为:供应商获取不到" + cgnSupplier.getString("supplyCode"));
            return "接受传输信息发生异常,异常原因为:供应商获取不到" + cgnSupplier.getString("supplyCode");
        }
        gys.setSuppliercode(formatString(cgnSupplier.getString("supplyCode")));// 主数据码
        gys.setSuppliername(formatString(cgnSupplier.getString("supplyName")));
        service.insert(gys);
        return "";
    }

    /*
     * 修改标底价
     */
    public String updateQingDanBD(JSONObject row, CgTrustdeedinfo cgTrustdeedinfo, String enterpriseCode,
            String enterpriseName, ZtbCommonDao service) {
        String sql = "select * from CG_DemandInventory where trustguid=? and ERPXuHao=?";
        CgDemandinventory qingdan = service.find(sql, CgDemandinventory.class, cgTrustdeedinfo.getTrustguid(),
                formatString(row.getString("serialno")));
        if (qingdan == null || qingdan.isEmpty()) {
            return "未找到对应需求清单！";
        }
        else {
            // 循环插入修改记录
            CgUpbiaodipricelog cgUpbiaodipricelog = new CgUpbiaodipricelog();
            cgUpbiaodipricelog.setRowguid(UUID.randomUUID().toString());
            cgUpbiaodipricelog.setTrustguid(cgTrustdeedinfo.getTrustguid());
            cgUpbiaodipricelog.setDemandguid(qingdan.getRowguid());
            cgUpbiaodipricelog.setOperatedate(new Date());
            cgUpbiaodipricelog.setBiaodiprice(row.getDouble("bidBottom"));// 标底价
            qingdan.setBaseprice(row.getDouble("bidBottom"));
            cgUpbiaodipricelog.setYuCeprice(row.getDouble("referPrice"));// 预测价
            qingdan.setForecastprice(row.getDouble("referPrice"));
            FrameUser wtrMan = erpFrameSysUtil.getFrameUser(enterpriseCode, service);
            cgUpbiaodipricelog.setOperateuserguid(ERPFrameSysUtil.getUserName(enterpriseCode, wtrMan, "userguid"));
            cgUpbiaodipricelog.setOperateusername(ERPFrameSysUtil.getUserName(enterpriseName, wtrMan, "displayname"));
            cgUpbiaodipricelog.setReason("接口推送");
            service.insert(cgUpbiaodipricelog);
            qingdan.setOperatedate(new Date());
            service.update(qingdan);

            // 给项目经理发送消息提醒
            MessagesCenter bean = new MessagesCenter();
            MessagesCenterService9 messagecenetrservice = new MessagesCenterService9();
            if (wtrMan != null && !wtrMan.isEmpty()) {
                bean.setFromDispName(wtrMan.getDisplayName());
                bean.setFromUser(wtrMan.getUserGuid());
                bean.setOuGuid(wtrMan.getOuGuid());
            }
            String infoguid = UUID.randomUUID().toString();
            bean.setMessageItemGuid(infoguid);
            bean.setTitle(String.format("【%s】标底价/预测价录入", cgTrustdeedinfo.getTrustdeedno()));
            bean.setMessageType("阅读");
            bean.setContent(cgTrustdeedinfo.getPurchaseprojectname() + "已录入标底价/预测价");
            bean.setMessagesRemindType("waithandle");
            bean.setSendMode(4);
            bean.setGenerateDate(new Date());
            bean.setTargetUser(cgTrustdeedinfo.getPromanagerguid());
            bean.setIsShow(1);
            bean.setTargetDispName(cgTrustdeedinfo.getPromanager());
            bean.setNoNeedRemind(0);
            // add by wxf 待办事宜list页面中的过滤条件
            bean.setIsDel(0);
            bean.setIsNoHandle(0);
            bean.setHandleType(MessagesCenterService9.MESSAGETYPE_READ);
            bean.setHandleUrl("zhmanagemis/mainpages/WaitHandleInfoGeneral?infoguid=" + infoguid);
            messagecenetrservice.WaitHandle_Insert(bean, "");

            // 标底预测附件至评标系统
            List<ZhglAttachinfoYewu> list = new DB_ZHGL_AttachInfo_YeWu().Select(cgTrustdeedinfo.getRowguid(), "AG010");
            new CommonUtil(service).pushAttach(list, cgTrustdeedinfo);
            // 标底预测价推送至评标系统
            new CommonUtil(service).pushBDYCPrice(cgTrustdeedinfo);

            // 记录日志
            Epoint.ZtbMis.Bizlogic.Sys.DB_OperationLog.Sys_OperationLog_Add_NoTran(cgTrustdeedinfo.getRowguid(),
                    ClsEnum.SubSysName.建设工程, ClsEnum.OperName.增加,
                    "保存委托申请 采购项目名称： " + cgTrustdeedinfo.getPurchaseprojectname(), "erp标底价录入");
        }
        return "";
    }

    public String insertDingBiao(JSONObject row, CgTrustdeedinfo cgTrustdeedinfo, ZtbCommonDao service) {
        SqlBuilder sql = new SqlBuilder().append("select * from CG_DemandInventory where trustguid=? ",
                cgTrustdeedinfo.getTrustguid());
        if ("A".equals(cgTrustdeedinfo.getPurchasetype())) {
            // 定标意见工程类接口文档中没有，清单标识orderNo，所以使用engineeringNo对应
            // 最好是希望工程类定标结果上，加上清单标识orderNo
            sql.append("and Servicecode=?", formatString(row.getString("engineeringNo")));
        }
        else if ("S".equals(cgTrustdeedinfo.getPurchasetype())) {
            sql.append("and bidEntrLineNo=?", formatString(row.getString("orderNo")));
        }
        else if ("M".equals(cgTrustdeedinfo.getPurchasetype())) {
            sql.append("and bidEntrLineNo=?", formatString(row.getString("orderNo")));
        }
        CgDemandinventory qingdan = service.find(sql.getSql(), CgDemandinventory.class, sql.getParams());
        if (qingdan == null || qingdan.isEmpty()) {
            return "未查询到对应需求清单" + row.getString("orderNo");
        }
        else {
            HuiyuanUnitcominfo gysdw = new ERPFrameSysUtil().getHuiyuanUnitcominfo(row.getString("supplyCode"), "",
                    service);
            CgShoubiaoandbditem item = new CgShoubiaoandbditem();
            item.setRowguid(UUID.randomUUID().toString());
            item.setBiaoduanguid(cgTrustdeedinfo.getBiaoDuanGuid());
            if (gysdw != null && !gysdw.isEmpty()) {
                item.setDanweiguid(gysdw.getDanweiguid());
            }
            item.setAwardtype("2");
            item.setItemguid(qingdan.getRowguid());
            if ("A".equals(cgTrustdeedinfo.getPurchasetype())) {
                item.set("purResultLineId", row.getString("engineeringNo"));
            }
            else {
                item.set("purResultLineId", row.getString("orderNo"));
            }
            item.setAwardquantity(row.getDouble("winNum"));
            item.setAwardsinprice(row.getDouble("winPrice"));
            BigDecimal winnum = new BigDecimal(row.getString("winNum"));
            BigDecimal winPrice = new BigDecimal(row.getString("winPrice"));
            Double totaowinprice = winnum.multiply(winPrice).doubleValue();
            item.setAwardtotalprice(totaowinprice);
            // BigDecimal bidNum = new BigDecimal(row.getString("bidNum"));
            // BigDecimal bidPrice = new BigDecimal(row.getString("bidPrice"));
            // Double totaobidprice = bidNum.multiply(bidPrice).doubleValue();
            item.setQuasiawardtotalprice(totaowinprice);
            item.setShoubiaoguid(cgTrustdeedinfo.getBiaoDuanGuid());
            item.setOperatedate(new Date());
            service.insert(item);
        }
        return "";
    }

    public String insertSingle(JSONObject modifInfo, AGCgDanYiLaiYuanLuruInfo cgDanYiLaiYuanLuruInfo,
            ZtbCommonDao service) {
        cgDanYiLaiYuanLuruInfo.setRowguid(UUID.randomUUID().toString());
        String jinezhanbi = modifInfo.getString("jinezhanbi");
        String biaodileibie = modifInfo.getString("biaodileibie");
        if (StringUtil.isNotBlank(jinezhanbi) && StringUtil.isNotBlank(biaodileibie)) {
            String[] biaodileibies = biaodileibie.split(";");

            String[] jinezhanbis = jinezhanbi.split(";");
            Double sum = 0.0;
            for (int i = 0; i < jinezhanbis.length; i++) {
                sum += Double.parseDouble(jinezhanbis[i]);
            }
            if (!sum.equals(100.00)) {
                return "主要标的类别采购金额占比合计不为100%，请校验！";
            }
            for (int i = 0; i < biaodileibies.length; i++) {
                Record record = new Record();
                record.set("rowguid", UUID.randomUUID().toString());
                record.setPrimaryKeys("rowguid");
                record.set("trustguid", cgDanYiLaiYuanLuruInfo.getRowguid());
                record.setSql_TableName("cg_trust_mainbidtype");
                record.set("maintypecode", biaodileibies[i]);
                record.set("proportion", jinezhanbis[i]);
                service.insert(record);
            }
        }
        cgDanYiLaiYuanLuruInfo.set("tanpanfangshi", modifInfo.getString("tanpanfangshi"));
        cgDanYiLaiYuanLuruInfo.set("tanpanshuxing", modifInfo.getString("tanpanshuxing"));
        cgDanYiLaiYuanLuruInfo.set("fromplat", modifInfo.getString("fromplat"));
        cgDanYiLaiYuanLuruInfo.set("ywsysid", modifInfo.getString("ywsysid"));
        cgDanYiLaiYuanLuruInfo.set("ywsysname", modifInfo.getString("ywsysid"));
        cgDanYiLaiYuanLuruInfo.setCgzzdyzsjcode(formatString(modifInfo.getString("caigouunitcode")));
        cgDanYiLaiYuanLuruInfo.setCgzzdyzsjname(formatString(modifInfo.getString("caigouunitname")));
        cgDanYiLaiYuanLuruInfo.setCgzzdyssgszsjcode(formatString(modifInfo.getString("caigougongsicode")));
        cgDanYiLaiYuanLuruInfo.setCgzzdyssgszsjname(formatString(modifInfo.getString("caigougongsiname")));
        cgDanYiLaiYuanLuruInfo.setFromPlatno(formatString(modifInfo.getString("sysName")));
        cgDanYiLaiYuanLuruInfo.setProjectno(formatString(modifInfo.getString("projectCode")));
        cgDanYiLaiYuanLuruInfo.setProjectname(formatString(modifInfo.getString("projectName")));
        cgDanYiLaiYuanLuruInfo.setCompanyorgcode(formatString(modifInfo.getString("companyOrgCode")));
        cgDanYiLaiYuanLuruInfo.setCompanyorgcname(formatString(modifInfo.getString("Companyorgcname")));
        cgDanYiLaiYuanLuruInfo.setWeituoren(formatString(modifInfo.getString("consignorPersonName")));
        cgDanYiLaiYuanLuruInfo.setIsGongKai(formatString(modifInfo.getString("isNotice"))); // 是否公开
        cgDanYiLaiYuanLuruInfo.set("isjizhong", formatString(modifInfo.getString("isUnionCG"))); // 是否集中采购
        cgDanYiLaiYuanLuruInfo.set("purorgform", formatString(modifInfo.getString("purorgform")));
        cgDanYiLaiYuanLuruInfo.set("wttech", formatString(modifInfo.getString("wttech")));
        cgDanYiLaiYuanLuruInfo.set("estmth", formatString(modifInfo.getString("estmth")));
        cgDanYiLaiYuanLuruInfo.set("wtbusiness", formatString(modifInfo.getString("wtbusiness")));
        cgDanYiLaiYuanLuruInfo.set("dtebidissue", formatString(modifInfo.getString("dtebidissue")));
        cgDanYiLaiYuanLuruInfo.set("dtebidopen", formatString(modifInfo.getString("dtebidopen")));
        cgDanYiLaiYuanLuruInfo.set("numsupplier", formatString(modifInfo.getString("numsupplier")));
        String cdesupplier = formatString(modifInfo.getString("cdesupplier"));
        String namesupplier = formatString(modifInfo.getString("namesupplier"));
        String[] namesuppliers = namesupplier.split("/");
        String[] cdesuppliers = cdesupplier.split("/");
        SqlBuilder sbcde = new SqlBuilder();
        sbcde.appendIn("select * from huiyuan_unitcominfo hu where hu.UnitOrgNum in (?)", Arrays.asList(cdesuppliers));
        List<HuiyuanUnitcominfo> findList = service.findList(sbcde.getSql(), HuiyuanUnitcominfo.class,
                sbcde.getParams());
        if (findList != null && findList.size() > 0) {
            for (HuiyuanUnitcominfo huiyuanUnitcominfo : findList) {
                Record record = new Record();
                record.setSql_TableName("cg_tanpanbuludanwei");
                record.setPrimaryKeys("rowguid");
                record.set("rowguid", UUID.randomUUID().toString());
                record.set("danweiguid", huiyuanUnitcominfo.getDanweiguid());
                record.set("orgnum", huiyuanUnitcominfo.getUnitorgnum());
                record.set("buluguid", cgDanYiLaiYuanLuruInfo.getRowguid());
                record.set("danweiname", huiyuanUnitcominfo.getDanweiname());
                service.insert(record);
            }
        }
        else {
            for (int j = 0; j < cdesuppliers.length; j++) {
                Record record = new Record();
                record.setSql_TableName("cg_tanpanbuludanwei");
                record.setPrimaryKeys("rowguid");
                record.set("rowguid", UUID.randomUUID().toString());
                record.set("danweiguid", record.getStr("rowguid"));
                record.set("orgnum", cdesuppliers[j]);
                record.set("buluguid", cgDanYiLaiYuanLuruInfo.getRowguid());
                record.set("danweiname", namesuppliers[j]);
                service.insert(record);
            }
        }

        FrameUser wtr = new FrameUserService9().getUserByUserField("loginid",
                formatString(modifInfo.getString("consignorPersonCode")));
        cgDanYiLaiYuanLuruInfo.setWeituorenguid(wtr != null ? wtr.getUserGuid() : "");
        cgDanYiLaiYuanLuruInfo.setWeituorencode(formatString(modifInfo.getString("consignorPersonCode")));
        cgDanYiLaiYuanLuruInfo.setWeituodanweiname(formatString(modifInfo.getString("consignorOrgName")));
        //zsy 采购单位
        cgDanYiLaiYuanLuruInfo.setCaigoudanweiname(formatString(modifInfo.getString("companyOrgCname")));
        FrameOu wtdw = new FrameOuService9().getOuByOuField("oucode",
                formatString(modifInfo.getString("companyOrgCode")));
        cgDanYiLaiYuanLuruInfo.setWeituodanweiguid(wtdw != null ? wtdw.getOuguid() : "");
        //zsy 委托单位编码
        cgDanYiLaiYuanLuruInfo.setWeituodanweicode(formatString(modifInfo.getString("companyOrgCode")));
        AGCgEscategory agCgEscategory = erpFrameSysUtil
                .getCgEscategory(formatString(modifInfo.getString("supplyClassCode")), service);
        if (agCgEscategory != null && !agCgEscategory.isEmpty()) {
            cgDanYiLaiYuanLuruInfo.setSupplytype(agCgEscategory.getCategoryGuid());// 供应分类ID
            cgDanYiLaiYuanLuruInfo.setSupplytypename(erpFrameSysUtil.getFullName(agCgEscategory));// 供应分类
            String purchaseType = "";
            if (agCgEscategory.getCategoryGuid().startsWith("4.")
                    || agCgEscategory.getCategoryGuid().startsWith("5.")) {
                purchaseType = "A";
            }
            else if (agCgEscategory.getCategoryGuid().startsWith("1.")
                    || agCgEscategory.getCategoryGuid().startsWith("2.")
                    || agCgEscategory.getCategoryGuid().startsWith("3.")) {
                purchaseType = "M";
            }
            else if (agCgEscategory.getCategoryGuid().startsWith("6.")) {
                purchaseType = "S";
            }
            // codeservice.getItemTextByCodeName("bid_bengangerp_purchaseType",formatString(consignation.getString("purchaseType")))
            cgDanYiLaiYuanLuruInfo.setPurchasetype(purchaseType);// 采购类别
        }
        cgDanYiLaiYuanLuruInfo.setPurchasesolutionno(formatString(modifInfo.getString("purchaseSolutionNo")));
        cgDanYiLaiYuanLuruInfo.setZhixingcelue(formatString(modifInfo.getString("executionPolicy")));
        cgDanYiLaiYuanLuruInfo.setDanyitype(formatString(modifInfo.getString("detailType")));
        cgDanYiLaiYuanLuruInfo.setPmname(formatString(modifInfo.getString("companyPersonNname")));
        FrameUser pmUser = new FrameUserService9().getUserByUserField("loginid",
                formatString(modifInfo.getString("companyPersonCode")));
        cgDanYiLaiYuanLuruInfo.setPmguid(pmUser != null ? pmUser.getUserGuid() : "");
        cgDanYiLaiYuanLuruInfo.setPmcode(formatString(modifInfo.getString("companyPersonCode")));
        cgDanYiLaiYuanLuruInfo.setZijinlaiyuan(formatString(modifInfo.getString("moneySource")));
        cgDanYiLaiYuanLuruInfo.setZijinxingzhi(formatString(modifInfo.getString("moneyType")));
        cgDanYiLaiYuanLuruInfo.setProstartdate(formatDateString(modifInfo.getString("startTime"), "yyyyMMddHHmmss"));
        cgDanYiLaiYuanLuruInfo.setProenddate(formatDateString(modifInfo.getString("endTime"), "yyyyMMddHHmmss"));
        cgDanYiLaiYuanLuruInfo.setProjectcontent(formatString(modifInfo.getString("bidContent")));
        cgDanYiLaiYuanLuruInfo.setGysdanweiname(formatString(modifInfo.getString("supplierName")));

        cgDanYiLaiYuanLuruInfo
                .setGysdanweiguid(service.queryString("select danweiguid from huiyuan_unitcominfo where code=?",
                        formatString(modifInfo.getString("supplierCode"))));
        cgDanYiLaiYuanLuruInfo.setGysdanweicode(formatString(modifInfo.getString("supplierCode")));
        cgDanYiLaiYuanLuruInfo.setZhongbiaoprice(modifInfo.getDouble("bidPrice"));
        cgDanYiLaiYuanLuruInfo.setLianxiren(formatString(modifInfo.getString("contactPersonName")));
        cgDanYiLaiYuanLuruInfo.setLianxirentel(formatString(modifInfo.getString("contactPersonTel")));

        service.insert(cgDanYiLaiYuanLuruInfo);
        if (modifInfo.getJSONArray("singleDetail") != null) {
            for (Object o : modifInfo.getJSONArray("singleDetail")) {
                JSONObject singleDetail = (JSONObject) o;
                CgDemandinventory qingdan = new CgDemandinventory();
                qingdan.setRowguid(UUID.randomUUID().toString());
                qingdan.setTrustguid(cgDanYiLaiYuanLuruInfo.getRowguid());
                // TODO 待确认
                qingdan.setBidEntrLineNo(singleDetail.getString("consignationNo"));

                qingdan.setServicecode(formatString(singleDetail.getString("materialNo")));
                qingdan.setServicename(formatString(singleDetail.getString("materialName")));
                qingdan.setGuigexinghao(formatString(singleDetail.getString("standard")));
                qingdan.setCaizhi(formatString(singleDetail.getString("quality")));
                qingdan.setXuyongdanwei(formatString(singleDetail.getString("usedUnit")));
                qingdan.setBiddingquantity(singleDetail.getDouble("detailCount"));
                qingdan.setUnits(formatString(singleDetail.getString("measureUnit")));
                qingdan.setPrice(singleDetail.getDouble("price"));
                qingdan.setAmount(singleDetail.getDouble("amount"));
                qingdan.setCeilingprice(singleDetail.getDouble("highestPrice"));
                qingdan.setBaseprice(singleDetail.getDouble("bidBottom"));
                qingdan.setForecastprice(singleDetail.getDouble("planPrice"));
                qingdan.setServicecontent(formatString(singleDetail.getString("notes")));
                service.insert(qingdan);
            }
        }
        if (modifInfo.getJSONObject("files") != null) {
            // TODO 需确认接收的是否是BL001类型
            erpFrameSysUtil.dealFile(modifInfo.getJSONObject("files"), cgDanYiLaiYuanLuruInfo.getRowguid(), "BL001", "",
                    false, service);
        }
        return "";
    }

    public String formatString(String str) {
        if (StringUtil.isBlank(str)) {
            return "";
        }
        else {
            return str.trim();
        }
    }

    public Date formatDateString(String str, String formatStr) {
        if (StringUtil.isBlank(formatString(str))) {
            return null;
        }
        else {
            return EpointDateUtil.convertString2Date(formatString(str), formatStr);
        }
    }

    public String formatString(String str, String type) {
        switch (StringUtil.toLowerCase(type)) {
            case "iswtdback":
            case "iszhongbiaojieguo":
                break;
            default:
                return formatString(str);
        }
        return str;
    }

    // 需要添加异常信息，处理转换招标方式
    public static String changeZBType(CgTrustdeedinfo cgTrustdeedinfo, JSONObject offlineResult, ZtbCommonDao service) {
        // 首先修改开标状态
        String biaoDuanGuid = cgTrustdeedinfo.getBiaoDuanGuid();
        CgBiaoduaninfo cgBiaoduaninfo = service.find(CgBiaoduaninfo.class, biaoDuanGuid);
        if (cgBiaoduaninfo == null) {
            return "没有获取到对应的标段";
        }
        if (StringUtil.isBlank(cgBiaoduaninfo.getZishenkaibiaodate())
                && StringUtil.isBlank(cgBiaoduaninfo.getKaibiaodate())) {
            return "时间为空，数据异常！";
        }
        cgBiaoduaninfo.setKaibiaoisselected("1");
        String supOpenTime = offlineResult.getString("supOpenTime");
        if (StringUtil.isNotBlank(supOpenTime)) {
            Date kaibiaoDate = EpointDateUtil.convertString2Date(supOpenTime, "yyyyMMddhhmmss");
            if (StringUtil.isBlank(cgBiaoduaninfo.getKaibiaodate())) {
                cgBiaoduaninfo.setZishenkaibiaodate(kaibiaoDate);
            }
            else {
                cgBiaoduaninfo.setKaibiaodate(kaibiaoDate);
                cgBiaoduaninfo.setKaibiaoisend("1");
            }
            service.update(cgBiaoduaninfo);
        }
        else {
            return "没有传输对应的开标时间";
        }

        if (!"0".equals(offlineResult.getString("isChange"))) {
            // 需要改变采购方式
            String bidType = offlineResult.getString("bidType");
            if (StringUtil.isBlank(bidType)) {
                return "获取获取新招标方式失败";
            }
            if ("G".equals(cgTrustdeedinfo.getPurchasemode())) {
                if ("8".equals(offlineResult.getString("bidType"))) {
                    // 转换成竞争性谈判
                    cgTrustdeedinfo.setPurchasemode("J");
                }
                else if ("5".equals(offlineResult.getString("bidType"))) {
                    // 转成单一来源
                    cgTrustdeedinfo.setPurchasemode("");
                }
                // 创建异常存储变化
                CgYichangbdhistory yichang = new CgYichangbdhistory();
                yichang.setBiaoduanguid(cgBiaoduaninfo.getBiaoduanguid());
                yichang.setBiaoduanno(cgBiaoduaninfo.getBiaoduanno());
                yichang.setFabaoguid(cgTrustdeedinfo.getFaBaoGuid());
                yichang.setFabaoname(cgBiaoduaninfo.getFabaoname());
                yichang.setFabaono(cgBiaoduaninfo.getFabaono());
                yichang.setOldzhaobiaofangshi("G");
                yichang.setNowzhaobiaofangshi(cgTrustdeedinfo.getPurchasemode());
                yichang.setPrimaryKeys("rowguid");
                yichang.setRowguid(UUID.randomUUID().toString());
                service.insert(yichang);
            }
        }
        service.update(cgTrustdeedinfo);
        return "";
    }

    public static String clearInfo(CgTrustdeedinfo cgTrustdeedinfo, JSONObject offlineResult, ZtbCommonDao service) {
        AGCgBiaoduaninfo biaoduaninfo = service.find(AGCgBiaoduaninfo.class, cgTrustdeedinfo.getBiaoDuanGuid());
        if (biaoduaninfo == null) {
            return "没有对应的标段信息";
        }
        if ("1".equals(offlineResult.getString("priceWhether"))) {

        }

        List<CgToubiaodanwei> tbdwlist = ZtbCommonDao.getInstance().findList(
                " select ct.*,hu.code from cg_toubiaodanwei ct left join huiyuan_unitcominfo hu on hu.DanWeiGuid = ct.DanWeiGuid where biaoduanguid = ? and isin <>'2' order by row_id desc",
                CgToubiaodanwei.class, cgTrustdeedinfo.getBiaoDuanGuid());
        AGCgBiaoduaninfo bdinfo = ZtbCommonDao.getInstance().find(AGCgBiaoduaninfo.class,
                cgTrustdeedinfo.getBiaoDuanGuid());
        JSONArray cpuClarifyBidderDetails = offlineResult.getJSONArray("cpuClarifyBidderDetails");
        int loop = 0;
        if (cpuClarifyBidderDetails != null) {
            for (Object o : cpuClarifyBidderDetails) {
                for (CgToubiaodanwei tbdw : tbdwlist) {
                    JSONObject cpuClarifyBidderDetail = (JSONObject) o;
                    // 匹配到业务库中的投标单位
                    if (tbdw.get("code").equals(cpuClarifyBidderDetail.getString("supplyCode"))) {
                        logger.info("澄清成功单位guid:" + tbdw.getDanweiguid() + "："
                                + cpuClarifyBidderDetail.getString("supplyCode"));
                        loop++;
                        // 发送会员在线消息提醒
                        Epoint.HuiYuanZtbMis.Bizlogic.HuiYuan.DB_HuiYuan_AlertInfo.HuiYuan_AlertInfo_Add(
                                tbdw.getDanweiguid(), tbdw.getDanweiname(), "",
                                cpuClarifyBidderDetail.getString("clarifyTitle"), "", "", "", new Date(),
                                "netztbmis/pages/zhixun/ZhiXun_DaFu?RowGuid=",
                                Epoint.HuiYuanZtbMis.Bizlogic.HuiYuan.DB_HuiYuan_AlertInfo.MESSAGE_TYPE_CHENGQING,
                                cpuClarifyBidderDetail.getString("clarifyTitle"), tbdw.getDanweitype());
                        // 发送项目负责人短信
                        new Epoint.ZtbMis.Bizlogic.Sys.DB_SmsMessage().smsMessage_Add("", tbdw.getLianxiren(),
                                cpuClarifyBidderDetail.getString("clarifyTitle"), tbdw.getLiximobile(), false);
                    }
                }
            }
            if (loop == 0) {
                return "通过接受的supplyCode和业务库中的投标单位表9s未匹配上";
            }
        }
        else {
            return "接受的单位信息数据为空";
        }
        return "";
    }

    public String changeBDinfoAndDw(JSONObject modifInfo, CgTrustdeedinfo cgTrustdeedinfo, ZtbCommonDao service) {
        AGCgBiaoduaninfo cgBiaoduaninfo = service.find(AGCgBiaoduaninfo.class, cgTrustdeedinfo.getBiaoDuanGuid());
        CgZigeyushen zigeyushen = service.find("select * from CG_zigeyushen where biaoduanguid=? ", CgZigeyushen.class,
                cgTrustdeedinfo.getBiaoDuanGuid());
        boolean isaddd = true;
        if (zigeyushen == null || zigeyushen.isEmpty()) {
            zigeyushen = new CgZigeyushen();
            zigeyushen.setRowguid(UUID.randomUUID().toString());
            zigeyushen.setZishenguid(zigeyushen.getRowguid());
        }
        else {
            isaddd = false;
        }
        zigeyushen.setSbrcode(cgTrustdeedinfo.getSbr_code());
        zigeyushen.setSbrname(cgTrustdeedinfo.getSbr_name());
        zigeyushen.setSbrunitguid(cgTrustdeedinfo.getSbr_unitguid());
        zigeyushen.setSbrunitname(cgTrustdeedinfo.getSbr_unitname());
        zigeyushen.setSbrtel(cgTrustdeedinfo.getSbr_tel());
        zigeyushen.setSbrmoblie(cgTrustdeedinfo.getSbr_moblie());
        zigeyushen.setXiaqucode(cgBiaoduaninfo.getXiaqucode());
        zigeyushen.setBdsecondtype(cgBiaoduaninfo.getBdsecondtype());
        zigeyushen.setBiaoduanname(cgBiaoduaninfo.getBiaoduanname());
        zigeyushen.setProjectname(cgBiaoduaninfo.getProjectname());
        zigeyushen.setBiaoduanguid(cgBiaoduaninfo.getBiaoduanguid());
        zigeyushen.setBiaoduanno(cgBiaoduaninfo.getBiaoduanno());
        zigeyushen.setFabaoguid(cgBiaoduaninfo.getFabaoguid());
        zigeyushen.setFabaono(cgBiaoduaninfo.getFabaono());
        zigeyushen.setFabaoname(cgBiaoduaninfo.getFabaoname());
        zigeyushen.setProjectno(cgBiaoduaninfo.getProjectno());
        zigeyushen.setProjectjiaoyitype(cgBiaoduaninfo.getProjectjiaoyitype());
        zigeyushen.setProjectguid(cgBiaoduaninfo.getProjectguid());
        zigeyushen.setOperatedate(new Date());
        zigeyushen.setAuditstatus("3");
        zigeyushen.setGonggaocontent("");
        zigeyushen.setSbrdate(new Date());
        cgBiaoduaninfo.setZishenisselected("1");
        service.update(cgBiaoduaninfo);
        // 记录日志
        Epoint.ZtbMis.Bizlogic.Sys.DB_OperationLog.Sys_OperationLog_Add_NoTran(zigeyushen.getRowguid(),
                EpointBid_Constant.Enums.ClsEnum.SubSysName.建设工程, ClsEnum.OperName.修改,
                "保存资格预审申请结果 项目名称 : " + cgBiaoduaninfo.getFabaoname(), "新增资格预审申请结果");

        String trustGuid = cgTrustdeedinfo.getTrustguid();
        List<CgToubiaodanwei> tbdwlist = ZtbCommonDao.getInstance().findList(
                " select ct.*,hu.code from cg_toubiaodanwei ct left join huiyuan_unitcominfo hu on hu.DanWeiGuid = ct.DanWeiGuid where biaoduanguid = ? and isin <>'2' order by row_id desc",
                CgToubiaodanwei.class, cgTrustdeedinfo.getBiaoDuanGuid());
        JSONArray mainDataArray = modifInfo.getJSONArray("cgnSuppliers");
        int loop = 0;
        if (mainDataArray != null) {
            for (Object o : mainDataArray) {
                for (CgToubiaodanwei tbdw : tbdwlist) {
                    JSONObject data = (JSONObject) o;
                    // 匹配到业务库中的投标单位
                    if (tbdw.get("code").equals(data.getString("supplyCode"))) {
                        // 2更新业务库
                        // 淘汰了 就是业务库中的 不通过 2
                        // 反之 为通过 1
                        loop++;
                        // erp
                        // 0 正常
                        // 1 淘汰
                        tbdw.setZishenresult("1".equals(data.getString("status")) ? "2" : "1");
                        tbdw.setNotpassreason(data.getString("reason"));
                        tbdw.setIsbaoming("1".equals(data.getString("status")) ? "" : "确认参加");
                        service.update(tbdw);
                        if ("1".equals(tbdw.getZishenresult())) {
                            // 发送资审通过通知
                            String messgae = "您单位通过了" + cgBiaoduaninfo.getBiaoduanname() + "的资格审查，感谢您对我们工作的大力支持。";
                            String strTitle = "【资格预审通过通知书】：" + cgBiaoduaninfo.getBiaoduanname();
                            DB_HuiYuan_AlertInfo.HuiYuan_AlertInfo_Add(tbdw.getDanweiguid(), tbdw.getDanweiname(), "",
                                    strTitle, "", cgTrustdeedinfo.getSbr_code(), cgTrustdeedinfo.getSbr_name(),
                                    new Date(), "", DB_HuiYuan_AlertInfo.MESSAGE_TYPE_INFOMATION, messgae,
                                    tbdw.getDanweitype(), cgBiaoduaninfo.getBiaoduanguid(),
                                    cgBiaoduaninfo.getProjectjiaoyitype());
                        }
                        else {
                            // 发送资审结果通知
                            String messgae2 = "很遗憾，您单位未通过" + cgBiaoduaninfo.getBiaoduanname()
                                    + "的资格审查，感谢贵公司的参与，期待与您的合作！";
                            String strTitle2 = "【资格预审结果通知书】：" + cgBiaoduaninfo.getBiaoduanname();
                            // 向Huiyuan_Message表插入数据
                            DB_HuiYuan_AlertInfo.HuiYuan_AlertInfo_Add(tbdw.getDanweiguid(), tbdw.getDanweiname(), "",
                                    strTitle2, "", cgTrustdeedinfo.getSbr_code(), cgTrustdeedinfo.getSbr_name(),
                                    new Date(), "", DB_HuiYuan_AlertInfo.MESSAGE_TYPE_INFOMATION, messgae2,
                                    tbdw.getDanweitype(), cgBiaoduaninfo.getBiaoduanguid(),
                                    cgBiaoduaninfo.getProjectjiaoyitype());
                        }
                    }
                }
            }
            if (loop == 0) {
                return "通过接受的supplyCode和业务库中的投标单位表9s未匹配上";
            }
        }
        else {
            return "接受的单位信息数据为空";
        }
        com.epoint.JSGCZtb.OutDll.JSGCZtb.Send2DataExchange(zigeyushen, "516");
        return "";
    }

    public Integer formatInt(String str) {
        if (StringUtil.isBlank(str)) {
            return null;
        }
        else {
            try {
                return Integer.parseInt(str);
            }
            catch (Exception e) {
                return null;
            }
        }
    }

    public String checkItemCode(String purchasetype, JSONObject jdata) {
        FrameConfigService9 config = new FrameConfigService9();
        String frameConfigValue = config.getFrameConfigValueByNameWithDefault("ERP接口物料编码位数校验", "^\\w{8}$");
        JSONArray details = null;
        if ("M".equalsIgnoreCase(purchasetype)) {
            details = jdata.getJSONArray("cgnMaterialDetails");
            if (details == null) {
                details = jdata.getJSONArray("cgnDeviceDetails");
            }
        }
        if (details != null) {
            for (Object object : details) {
                JSONObject detail = (JSONObject) object;

                boolean check = RegexValidateUtil.check(detail.getString("materialNo"), frameConfigValue);
                if (!check) {
                    return "物料编码字段数据内容字段位数长度是否为8位字符";
                }
            }
        }
        return "";
    }

}
