
package com.epoint.ztb.enums;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 * 本类定义了无业务属性的所有系统枚举类
 *
 * <AUTHOR>
 * @version 2021年12月11日
 */
public class SysEnums
{

    /**
     * 会员类型
     *
     * <AUTHOR>
     * @version 2017年4月6日
     */
    public enum DanWeiType
    {
        // 新的会员类型
        招标人("11"), 投标人("13"), 招标代理("18"), 专家("59"), 自然人("5"), 竞买人("7"), 个体经营("6"), 供应商("17"), 土地拍卖("44");

        // 旧的会员类型
        // 建设单位("11"), 采购单位("12"), 施工单位("13"), 监理单位("14"), 勘察单位("15"),
        // 设计单位("16"), 供应商("17"), 招标代理("18"), 项目管理("19"), 园林单位(
        // "20"), 政府采购代理("21"), 拍卖代理("33"), 咨询("42"), 土地拍卖("44"), 单位A("51"),
        // 单位B("52"), 专家("59"), 房建及市政施工单位("131"), 交通施工单位(
        // "132"), 水利施工单位("133");
        String value;

        DanWeiType(String s) {
            value = s;
        }

        public boolean equals(String value) {
            return String.valueOf(this.value).equals(value);
        }

        public String getValue() {
            return value;
        }
    }

    public enum UserType
    {
        外部会员("1"), 中心人员("2"), 专家("3"), 临时竞买人("4");

        String value;

        UserType(String s) {
            value = s;
        }

        public String getValue() {
            return value;
        }

        /**
         * 根据值获得实例
         *
         * @param value
         *            枚举值
         * @return 枚举实例
         */
        public static UserType getEnum(String value) {
            UserType e = null;
            for (UserType e1 : UserType.values()) {
                if (e1.getValue().equals(value)) {
                    e = e1;
                    break;
                }
            }
            return e;
        }
    }

    /**
     * 子系统枚举
     *
     * <AUTHOR>
     * @version 2021年12月11日
     */
    public enum SubSysName
    {
        建设工程("1"), 政府采购("2"), 产权交易("3"), 土地交易("4"), 会员管理("5"), 专家系统("6"), 特殊项目("7"), 综合查询("8"), 后台维护("9"), 数据交换(
                "10"), 语音通知("11"), 运维管理("12"), 竞价系统("13"), 林权交易("14"), 矿权交易("15"), 排污权交易("16"), 农村产权("17"), 水权交易(
                        "19"), 框架协议采购("18"), 企业招采(
                                "21"), 数据交易(
                                        "23"), 监督平台("61"), 公共服务平台("71"), 数字见证("81"), 其他("99"), 耕地交易("20"), 复垦交易("22"),海域交易("25");

        String value;

        SubSysName(String s) {
            value = s;
        }

        // 工作台子系统类别map
        public static Map<String, Object> getSystemClassMap() {
            Map<String, Object> map = new LinkedHashMap<>();
            map.put(SubSysName.建设工程.getValue(), SubSysName.建设工程.toString());
            map.put(SubSysName.政府采购.getValue(), SubSysName.政府采购.toString());
            map.put(SubSysName.框架协议采购.getValue(), SubSysName.框架协议采购.toString());
            map.put(SubSysName.产权交易.getValue(), SubSysName.产权交易.toString());
            map.put(SubSysName.土地交易.getValue(), SubSysName.土地交易.toString());
            map.put(SubSysName.林权交易.getValue(), SubSysName.林权交易.toString());
            map.put(SubSysName.矿权交易.getValue(), SubSysName.矿权交易.toString());
            map.put(SubSysName.排污权交易.getValue(), SubSysName.排污权交易.toString());
            map.put(SubSysName.农村产权.getValue(), SubSysName.农村产权.toString());
            map.put(SubSysName.企业招采.getValue(), SubSysName.企业招采.toString());
            map.put(SubSysName.耕地交易.getValue(), SubSysName.耕地交易.toString());
            map.put(SubSysName.复垦交易.getValue(), SubSysName.复垦交易.toString());
            map.put(SubSysName.数据交易.getValue(), SubSysName.数据交易.toString());
            map.put(SubSysName.海域交易.getValue(), SubSysName.海域交易.toString());
            return map;
        }

        public String getValue() {
            return value;
        }

        public boolean equals(Integer value) {
            return value != null && this.value.equals(value.toString());
        }

        /**
         * 根据值获得实例
         *
         * @param value
         *            枚举值
         * @return 枚举实例
         */
        public static SubSysName getEnum(String value) {
            SubSysName e = null;
            for (SubSysName e1 : SubSysName.values()) {
                if (e1.getValue().equals(value)) {
                    e = e1;
                    break;
                }
            }
            return e;
        }
    }

    /**
     * 分类
     *
     * <AUTHOR>
     * @version 2020年8月4日
     */
    public enum ProjectJiaoYiType
    {
        房屋建筑工程("A"), 市政基础设施工程("B"), 交通工程("E"), 水利工程("F"), 电力工程("L"), 政府采购("M"), 框架协议采购("KJXY"), 产权交易("Q"), 土地交易(
                "T"), 其他工程("K"), 服务类("S"), 股权交易("GQ"), 实物交易("SWQ"), 林权交易("LQ"), 排污权交易("PWQ"), 碳排放权交易("TQ"), 矿业权交易(
                        "KQ"), 农权交易("NQ"), 新产权交易(
                                "CQ"), 数据交易(
                                        "SJ"), 土地熔断价(
                                        "TF"), 其他采购("QTM"), 交通水利农田("JSN"), 医疗采购("YC"), 耕地交易("GDJY"), 复垦交易("FKJY"), 海域交易("MQ");

        String value;

        private ProjectJiaoYiType(String s) {
            value = s;
        }

        public boolean equals(String value) {
            return this.value.equals(value);
        }

        public String getValue() {
            return value;
        }
    }

    /**
     * 项目类型
     */
    public enum ProjectTypeEnum
    {
        产权交易("CQ"), 矿权交易("KQ"), 土地交易("T"), 农村产权("NQ"), 林权交易("LQ"), 排污权交易("PWQ"), 耕地交易("GDJY"), 复垦交易("FKJY"), 海域交易("MQ");

        String code;

        ProjectTypeEnum(String code) {
            this.code = code;
        }

        public String getCode() {
            return code;
        }

        /**
         * 根据传入的string转换成对应的枚举类
         *
         * @param code
         *            code
         * @return 对应的枚举类
         */
        public static ProjectTypeEnum getTypeEnumByCode(String code) {
            for (ProjectTypeEnum type : ProjectTypeEnum.values()) {
                if (type.getCode().equals(code)) {
                    return type;
                }
            }
            return null;
        }

        /**
         * 判断传入的code是否再上面的这些范围内
         *
         * @param code
         *            code
         * @return 是否在范围内
         */
        public static boolean checkCode(String code) {
            for (ProjectTypeEnum type : ProjectTypeEnum.values()) {
                if (type.getCode().equals(code)) {
                    return true;
                }
            }
            return false;
        }
    }

    /**
     * 操作步骤
     *
     * <AUTHOR>
     * @version 2017年4月6日
     */
    public enum OperName
    {
        增加("1"), 修改("2"), 删除("3"), 审核("4"), 迁移("5"), 查看("6"), 异常页面扫描("7"), 终止工作流("8"), 回第一步("9"), 工作流初始化("10"), 表单撤回(
                "99"), 需求拆分("11"), 需求分派("11"), 取消需求分派("11");

        String value;

        OperName(String s) {
            value = s;
        }

        public String getValue() {
            return value;
        }

        /**
         * 根据值获得实例
         *
         * @param value
         *            枚举值
         * @return 枚举实例
         */
        public static OperName getEnum(String value) {
            OperName e = null;
            for (OperName e1 : OperName.values()) {
                if (e1.getValue().equals(value)) {
                    e = e1;
                    break;
                }
            }
            return e;
        }
    }

    /**
     * 录入类型
     *
     * <AUTHOR>
     * @version 2017年4月6日
     */
    public enum LuRuType
    {
        外部会员(1), 中心人员(2), 系统导入(3), 投标工具(4), 招标工具(5);

        int type;

        LuRuType(int type) {
            this.type = type;
        }

        public boolean equals(Integer value) {
            return value != null && type == value;
        }

        public boolean equals(String value) {
            return String.valueOf(type).equals(value);
        }

        public String getValue() {
            return String.valueOf(type);
        }

        public int getIntValue() {
            return type;
        }
    }

    /**
     * 场地类型
     *
     * <AUTHOR>
     * @version 2017年4月6日
     */
    public enum ChangDiType
    {
        资格审查接收室("1"), 开标室("2"), 评标室("3"), 特殊用途("4"), 抽签室("5"), 资格审查评审室("6"), 会议室("7"), 不见面开标室("8");

        String value;

        ChangDiType(String s) {
            value = s;
        }

        public String getValue() {
            return value;
        }
    }

    /**
     * 场地生效和取消类型 [一句话功能简述]
     *
     * <AUTHOR>
     * @version 2022年1月20日
     */
    public enum MTRStatusCode
    {
        未生效("0"), 有效("3"), 取消("-1");

        String value;

        MTRStatusCode(String s) {
            value = s;
        }

        public String getValue() {
            return value;
        }
    }

    /**
     * 是否
     *
     * <AUTHOR>
     * @version 2021年12月14日
     */
    public enum TrueOrFalse
    {
        是("1"), 否("0");

        String value;

        TrueOrFalse(String s) {
            value = s;
        }

        public String getValue() {
            return value;
        }

        public boolean equals(Integer value) {
            return value != null && this.value.equals(value.toString());
        }

        public boolean equals(String value) {
            return this.value.equals(value);
        }
    }

    /**
     * 是否
     *
     * <AUTHOR>
     * @version 2021年12月14日
     */
    public enum TrueOrFalseEn
    {
        是("true"), 否("false");

        String value;

        TrueOrFalseEn(String s) {
            value = s;
        }

        public String getValue() {
            return value;
        }
    }

    /**
     * 账号状态
     *
     * <AUTHOR>
     *
     */
    public enum ZhangHaoStatus
    {
        注销("1"), 启用("2"), 暂停("3"), 处罚中("5"), 商品下架("6");

        String str;

        ZhangHaoStatus(String str) {
            this.str = str;
        }

        public String getValue() {
            return str;
        }

        /**
         * 通过状态值获取状态描述
         *
         * @param value
         *            状态值
         * @return 状态描述
         */
        public static String getEnumString(String value) {
            if (value == null) {
                return "未启用";
            }
            switch (value) {
                case "1":
                    return "注销";
                case "2":
                    return "启用";
                case "3":
                    return "暂停";
                case "5":
                    return "处罚中";
                default:
                    return "未启用";
            }
        }
    }

    /**
     * 开关
     *
     * <AUTHOR>
     * @version 2022年1月11日
     */
    public enum Switch
    {
        开("1"), 关("0");

        String value;

        Switch(String s) {
            value = s;
        }

        public String getvalue() {
            return value;
        }
    }

    /**
     * 控件类型
     *
     * <AUTHOR>
     * @version 2022年1月12日
     */
    public enum ControlTypes
    {
        文本框("textbox"), 多行文本框("textarea"), 复选框("checkboxlist"), 列表("datagrid");

        String value;

        ControlTypes(String s) {
            value = s;
        }

        public String getvalue() {
            return value;
        }
    }

    public enum ChangeType
    {
        基本信息("1"), 职业人员("2"), 总监("3"), 企业获奖("4"), 投标业绩("5"), 经营资质("6"), 从业人员("7"), 企业财务("8"), 信息披露("9"), 安全生产考核合格证(
                "10"), 机械设备("11"), 项目管理人员("12"), 各类证书("13"), 信用手册("14"), 专职交易人员("15"), 项目经理证("16"), 勘察工程师("17"), 设计工程师(
                        "18"), 设计工程师证("19"), 勘察工程师证("20"), 人员职业资格("21"), 招标业绩("22"), 咨询工程师("31"), 咨询工程师证(
                                "32"), 总监证("33"), 奖惩记录("34"), 人员调动("35"), 评委信息("59"), 产品与服务("60"), 主体类型("61"),入库申请("62");

        String value;

        ChangeType(String s) {
            value = s;
        }

        public String getValue() {
            return value;
        }

        public static ChangeType getEnum(String value) {// 根据值获得实例
            ChangeType e = null;
            for (ChangeType e1 : ChangeType.values()) {
                if (e1.getValue().equals(value)) {
                    e = e1;
                    break;
                }
            }
            return e;
        }
    }

    /**
     * 新增抗抵赖签名加密业务环节 by gx 2020-7-8
     */
    public enum SignatureStep
    {
        银行账号维护("0"), 投标报名("1"), 投标文件撤销("2"), 区块链数据同步("3");

        String value;

        SignatureStep(String s) {
            value = s;
        }

        public String getValue() {
            return value;
        }
    }

    public enum OnlineUserActionType
    {
        Login, Refresh, Logout;
    }

    /**
     * 语音通知单位评委录入类型
     *
     * <AUTHOR>
     * @version 2017年4月6日
     */
    public enum Yytz_DanWei_PWLuRuType
    {
        语音系统自建("1"), 会员单位导入("2");

        String value;

        Yytz_DanWei_PWLuRuType(String s) {
            value = s;
        }

        public String getValue() {
            return value;
        }
    }

}
