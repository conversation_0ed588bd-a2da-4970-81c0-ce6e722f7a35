package com.epoint.ztb.log;

import java.util.Date;
import java.util.UUID;

import com.epoint.core.dao.ICommonDao;
import com.epoint.core.utils.collection.EpointCollectionUtils;
import com.epoint.core.utils.container.ContainerFactory;
import com.epoint.core.utils.web.WebUtil;
import com.epoint.frame.service.metadata.systemparameters.api.IFrameConfigService;
import com.epoint.logcenter.collector.LogCollector;
import com.epoint.ztb.bidencrypt.EpZtbEncrypt;
import com.epoint.ztb.bidflow.core.TaskConfig;
import com.epoint.ztb.ca.common.StringUtil;
import com.epoint.ztb.common.database.ZtbCommonDao;
import com.epoint.ztb.configmanage.setting.constants.JyztbSettingKeyNames;
import com.epoint.ztb.context.TaskContext;
import com.epoint.ztb.entity.MemberVO;
import com.epoint.ztb.entity.SysOperationLog;
import com.epoint.ztb.entity.ZtbMisEnity;
import com.epoint.ztb.enums.SysEnums;
import com.epoint.ztb.enums.SysEnums.LuRuType;
import com.epoint.ztb.enums.SysEnums.SubSysName;

/**
 * 操作日志类.
 * 不推荐用此类记录错误日志
 *
 * <AUTHOR>
 * @version 2022年1月6日
 */
public class DB_OperationLog
{

    /**
     * 操作日志安全级别
     *
     * <AUTHOR>
     * @version 2022年1月6日
     */
    public enum SafeLevel
    {
        /**
         * 最高安全级别(a)，涉及敏感操作的需记录此级别日志，如开标时间修改、资审方式修改、评委专业/手机号修改等等
         */
        TOP("a"),
        /**
         * 较高安全级别(b)
         */
        HIGH("b"),
        /**
         * 无安全级别
         */
        NONE("");

        String value;

        SafeLevel(String value) {
            this.value = value;
        }

        public String getValue() {
            return this.value;
        }
    }

    private ICommonDao dao;

    public DB_OperationLog(MemberVO memberVO) {
        this.memberVO = memberVO;
        dao = ZtbCommonDao.getInstance();
    }

    public DB_OperationLog(MemberVO memberVO, ICommonDao dao) {
        this.memberVO = memberVO;
        this.dao = dao;
    }

    private SysOperationLog logBean;

    private MemberVO memberVO;

    /**
     * 记录操作日志
     *
     * @param projectGuid
     *            关联数据唯一标识
     * @param subSystem
     *            子系统
     * @param operateType
     *            操作类型
     * @param luRuType
     *            操作人员类型
     * @param operateContent
     *            具体日志内容
     * @param operateStep
     *            操作步骤
     * @param safeLevel
     *            安全级别
     * @return 本实例
     */
    public DB_OperationLog create(String projectGuid, SysEnums.SubSysName subSystem, SysEnums.OperName operateType,
            SysEnums.LuRuType luRuType, String operateContent, String operateStep, SafeLevel safeLevel) {
        this.create("", "", projectGuid, subSystem, operateType, luRuType, operateContent, operateStep, safeLevel);
        return this;
    }

    /**
     * 记录操作日志
     * 对接日志中心，添加biaoduanguid和clientguid两个字段赋值
     *
     * @param clientGuid
     *            业务数据唯一标识
     * @param biaoDuanGuid
     *            标段数据唯一标识
     * @param projectGuid
     *            关联数据唯一标识
     * @param subSystem
     *            子系统
     * @param operateType
     *            操作类型
     * @param luRuType
     *            操作人员类型
     * @param operateContent
     *            具体日志内容
     * @param operateStep
     *            操作步骤
     * @param safeLevel
     *            安全级别
     * @return 本实例
     */
    public DB_OperationLog create(String clientGuid, String biaoDuanGuid, String projectGuid,
            SysEnums.SubSysName subSystem, SysEnums.OperName operateType, SysEnums.LuRuType luRuType,
            String operateContent, String operateStep, SafeLevel safeLevel) {
        if (luRuType == null) {
            luRuType = StringUtil.isBlank(memberVO.getCurrentdanweitype()) ? LuRuType.中心人员 : LuRuType.外部会员;
        }
        this.logBean = new SysOperationLog();
        logBean.setProjectGuid(projectGuid);
        logBean.setXiaQuCode(memberVO.getOucode());
        logBean.setSubSystemCode(subSystem == null ? SubSysName.其他.getValue() : subSystem.getValue());
        // fromip通过框架方法获取外网的ip，和7.x保持一致
        logBean.setFromIP(WebUtil.getClientIP());
        logBean.setUserType(luRuType.getValue());
        logBean.setOperateType(operateType == null ? null : operateType.getValue());
        logBean.setOperateContent(operateContent);
        logBean.setUserGuid(memberVO.getUserguid());
        logBean.setOperateUserName(memberVO.getDisplayname());
        logBean.setOperateUnit(memberVO.getDanweiname());
        logBean.setOperateTime(new Date());
        logBean.setRowGuid(UUID.randomUUID().toString());
        logBean.setOperateStep(operateStep);
        logBean.setSafeLevel(safeLevel.getValue());
        logBean.setBiaoDuanGuid(biaoDuanGuid);
        logBean.setClientGuid(StringUtil.isBlank(clientGuid) ? projectGuid : clientGuid);
        return this;
    }

    /**
     * 加密
     *
     * @return 本实例
     */
    public DB_OperationLog encrypt() {
        EpZtbEncrypt.getInstance().EncryoRow(logBean, logBean.getProjectGuid(),
                SysEnums.SubSysName.getEnum(logBean.getSubSystemCode()));
        return this;
    }

    /**
     * 记录操作日志
     *
     * @param ProjectGuid
     *            关联数据唯一标识
     * @param subSystem
     *            子系统
     * @param operateType
     *            操作类型
     * @param luRuType
     *            操作人员类型
     * @param operateContent
     *            具体日志内容
     * @param operateStep
     *            操作步骤
     */
    @Deprecated
    public void addOperationLog(String ProjectGuid, SysEnums.SubSysName subSystem, SysEnums.OperName operateType,
            SysEnums.LuRuType luRuType, String operateContent, String operateStep) {
        this.create(ProjectGuid, subSystem, operateType, luRuType, operateContent, operateStep, SafeLevel.NONE)
                .insert();
        // 推送日志中心
        sendLogCenter(logBean);
    }

    /**
     * 重载记录操作日志方法：
     * 1.增加clientguid和biaoduanguid参数
     * 2.增加推送日志中心逻辑
     *
     * @param ClientGuid
     * @param BiaoDuanGuid
     * @param ProjectGuid
     * @param subSystem
     * @param operateType
     * @param luRuType
     * @param operateContent
     * @param operateStep
     */
    @Deprecated
    public void addOperationLog(String ClientGuid, String BiaoDuanGuid, String ProjectGuid,
            SysEnums.SubSysName subSystem, SysEnums.OperName operateType, SysEnums.LuRuType luRuType,
            String operateContent, String operateStep) {
        this.create(ClientGuid, BiaoDuanGuid, ProjectGuid, subSystem, operateType, luRuType, operateContent,
                operateStep, SafeLevel.NONE).insert();
        // 推送日志中心
        sendLogCenter(logBean);
    }

    /**
     * @param projectGuid
     *            关联数据唯一标识
     * @param subSystem
     *            子系统
     * @param operateType
     *            操作类型
     * @param operateContent
     *            具体日志内容
     * @param operateStep
     *            操作步骤
     * @deprecated {@link #addOperationLog(MemberVO, String, SubSysName, com.epoint.ztb.enums.SysEnums.OperName, String, String)}
     *             记录操作日志
     */
    public static void addOperationLog(String projectGuid, SysEnums.SubSysName subSystem, SysEnums.OperName operateType,
            String operateContent, String operateStep) {
        DB_OperationLog operationLog = new DB_OperationLog(new MemberVO());
        operationLog.create(projectGuid, subSystem, operateType, null, operateContent, operateStep, SafeLevel.NONE)
                .insert();
        // 推送日志中心
        operationLog.sendLogCenter(operationLog.getLogBean());
    }

    /**
     * 记录操作日志
     *
     * @param memberVO
     *            用户实体
     * @param projectGuid
     *            关联数据唯一标识
     * @param subSystem
     *            子系统
     * @param operateType
     *            操作类型
     * @param operateContent
     *            具体日志内容
     * @param operateStep
     *            操作步骤
     */
    public static void addOperationLog(MemberVO memberVO, String projectGuid, SysEnums.SubSysName subSystem,
            SysEnums.OperName operateType, String operateContent, String operateStep) {
        DB_OperationLog operationLog = new DB_OperationLog(memberVO);
        operationLog.create(projectGuid, subSystem, operateType, null, operateContent, operateStep, SafeLevel.NONE)
                .insert();
        // 推送日志中心
        operationLog.sendLogCenter(operationLog.getLogBean());
    }

    /**
     * 记录操作日志
     *
     * @param context
     *            TaskContext
     * @param operateType
     *            操作类型
     */
    public static void addOperationLog(TaskContext<? extends ZtbMisEnity> context, SysEnums.OperName operateType) {
        // 做一下容错，还真的有环节是没有表的 例如招标公告与文件 没主表
        if (context == null || context.getRootEntity() == null
                || StringUtil.isBlank(context.getRootEntity().getSql_TableName())) {
            return;
        }
        addOperationLog(context, context.getRootEntity(), context.getMemberVO(), operateType);
    }

    /**
     * 记录操作日志
     *
     * @param context
     *            TaskContext，主要用于获取systemname
     * @param entity
     *            操作实体，用于日志内容拼接
     * @param memberVO
     *            用户信息
     * @param operateType
     *            操作类型
     */
    public static void addOperationLog(TaskContext<? extends ZtbMisEnity> context, ZtbMisEnity entity,
            MemberVO memberVO, SysEnums.OperName operateType) {
        TaskConfig taskConfig = context.getClass().getAnnotation(TaskConfig.class);
        SubSysName subSysName = SubSysName.getEnum(taskConfig.SystemClass());
        EntityForLog entityForLog = entity.getClass().getAnnotation(EntityForLog.class);
        if (entityForLog == null) {
            throw new RuntimeException("日志记录" + entity.getClass().getName() + "需要增加EntityForLog注解！");
        }
        if (entityForLog.log_field().length != entityForLog.log_name().length) {
            throw new RuntimeException("日志记录" + entity.getClass().getName() + "的注解log_field应该与log_name个数一致！");
        }
        String operateStep = taskConfig.TaskName();
        StringBuilder content = new StringBuilder(operateStep).append(operateType.name());
        for (int i = 0; i < entityForLog.log_field().length; i++) {
            content.append(",").append(entityForLog.log_name()[i]).append(":")
                    .append(entity.getStr(entityForLog.log_field()[i]));
        }

        // 标段唯一标识
        String strBiaoDuanGuid = "";
        // 项目表唯一标识
        String strProjectGuid = "";
        if (context.getBiaoDuanVO() != null && !context.getBiaoDuanVO().isEmpty()) {
            strBiaoDuanGuid = context.getBiaoDuanVO().getBiaoduanguid();
            strProjectGuid = context.getBiaoDuanVO().getProjectguid();
        }
        else if (EpointCollectionUtils.isNotEmpty(context.getBiaoDuanVOList())) {
            strBiaoDuanGuid = context.getBiaoDuanVOList().get(0).getBiaoduanguid();
            strProjectGuid = context.getBiaoDuanVOList().get(0).getProjectguid();
        }
        DB_OperationLog operationLog = new DB_OperationLog(memberVO);
        operationLog.create(entity.getRowguid(), strBiaoDuanGuid,
                StringUtil.isNotBlank(strProjectGuid) ? strProjectGuid : entity.getRowguid(), subSysName, operateType,
                null, content.toString(), operateStep, SafeLevel.NONE).insert();
        // 推送日志中心
        operationLog.sendLogCenter(operationLog.getLogBean());
    }

    /**
     * @param projectGuid
     *            关联数据唯一标识
     * @param subSystem
     *            子系统
     * @param operateType
     *            操作类型
     * @param operateContent
     *            具体日志内容
     * @param operateStep
     *            操作步骤
     * @param safeLevel
     *            安全级别
     * @deprecated 记录带安全级别的操作日志
     */
    public static void addOperationLog(String projectGuid, SysEnums.SubSysName subSystem, SysEnums.OperName operateType,
            String operateContent, String operateStep, SafeLevel safeLevel) {
        addOperationLog(new MemberVO(), projectGuid, subSystem, operateType, operateContent, operateStep, safeLevel);
    }

    /**
     * 记录带安全级别的操作日志
     *
     * @param memberVO
     *            用户对象
     * @param projectGuid
     *            关联数据唯一标识
     * @param subSystem
     *            子系统
     * @param operateType
     *            操作类型
     * @param operateContent
     *            具体日志内容
     * @param operateStep
     *            操作步骤
     * @param safeLevel
     *            安全级别
     */
    public static void addOperationLog(MemberVO memberVO, String projectGuid, SysEnums.SubSysName subSystem,
            SysEnums.OperName operateType, String operateContent, String operateStep, SafeLevel safeLevel) {
        DB_OperationLog operationLog = new DB_OperationLog(memberVO);
        operationLog.create(projectGuid, subSystem, operateType, null, operateContent, operateStep, safeLevel).insert();
        // 推送日志中心
        operationLog.sendLogCenter(operationLog.getLogBean(), safeLevel);
    }

    /**
     * 记录加密操作日志
     *
     * @param memberVO
     *            用户对象
     * @param projectGuid
     *            关联数据唯一标识
     * @param subSystem
     *            子系统
     * @param operateType
     *            操作类型
     * @param operateContent
     *            具体日志内容
     * @param operateStep
     *            操作步骤
     * @param safeLevel
     *            安全级别
     */
    public static void addEncryptOperationLog(MemberVO memberVO, String projectGuid, SysEnums.SubSysName subSystem,
            SysEnums.OperName operateType, String operateContent, String operateStep, SafeLevel safeLevel) {
        DB_OperationLog operationLog = new DB_OperationLog(memberVO);
        operationLog.create(projectGuid, subSystem, operateType, null, operateContent, operateStep, safeLevel).encrypt()
                .insert();
        // 推送日志中心
        operationLog.sendLogCenter(operationLog.getLogBean(), safeLevel);
    }

    /**
     * frame_logerror表新增记录
     *
     * @param sourceGuid
     *            关联数据标识
     * @param errorMessage
     *            错误信息
     * @param note
     *            备注
     */
    public static void insertFrameLogError(String sourceGuid, String errorMessage, String note) {
        String strSql = "insert into frame_logerror (rowguid,sourceguid,errormessage,note,adddatetime) values (?,?,?,?,?)";
        ZtbCommonDao.getInstance().execute(strSql, UUID.randomUUID().toString(), sourceGuid, errorMessage, note,
                new Date());
    }

    /**
     * 推送日志中心
     *
     * @param sysOperationLog
     *            日志实体对象
     */
    public void sendLogCenter(SysOperationLog sysOperationLog) {
        sendLogCenter(sysOperationLog, SafeLevel.NONE);
    }

    /**
     * 推送日志中心
     *
     * @param sysOperationLog
     *            日志实体对象
     * @param safeLevel
     *            安全级别
     */
    public void sendLogCenter(SysOperationLog sysOperationLog, SafeLevel safeLevel) {
        // 增加配置是否开启日志推送
        if ("1".equals(ContainerFactory.getContainInfo().getComponent(IFrameConfigService.class)
                .getFrameConfigValue(JyztbSettingKeyNames.BID_ISPUSHLOGCENTER))) {
            // 当SafeLevel等级不为HIGH、TOP时才调用日志中心推送方法sendLogCenter
            if (!SafeLevel.TOP.getValue().equals(safeLevel.getValue())
                    && !SafeLevel.HIGH.getValue().equals(safeLevel.getValue())) {
                LogCollector.getInstance().addBriefCustomLog(sysOperationLog, sysOperationLog.getClientGuid());
                // 推送日志中心后，反填日志表推送状态字段
                updateIsSend(sysOperationLog);
            }
        }
    }

    /**
     * 推送日志中心后，反填日志表推送状态字段
     *
     * @param sysOperationLog
     *            日志实体对象
     */
    public void updateIsSend(SysOperationLog sysOperationLog) {
        ZtbCommonDao.getInstance().execute("update Sys_OperationLog set issend='1',sendtime=? where rowguid=?",
                new Date(), sysOperationLog.getRowguid());
    }

    /**
     * 入库
     *
     * @return 入库影响行数
     */
    public int insert() {
        return dao.insert(logBean);
    }

    public SysOperationLog getLogBean() {
        return logBean;
    }

    public void setLogBean(SysOperationLog logBean) {
        this.logBean = logBean;
    }

    public MemberVO getMemberVO() {
        return memberVO;
    }

    public void setMemberVO(MemberVO memberVO) {
        this.memberVO = memberVO;
    }
}
