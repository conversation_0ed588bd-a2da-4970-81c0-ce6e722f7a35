package com.epoint.cnbm.ztb.erpandprocurementplatform.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.epoint.cnbm.ztb.erpandprocurementplatform.api.ICnbmTradePlatApi;
import com.epoint.cnbm.ztb.supplierplatform.service.CnbmSuppilerPlatformWebUtil;
import com.epoint.core.grammar.Record;
import com.epoint.core.utils.code.Base64Util;
import com.epoint.core.utils.collection.EpointCollectionUtils;
import com.epoint.core.utils.container.ContainerFactory;
import com.epoint.core.utils.date.EpointDateUtil;
import com.epoint.core.utils.httpclient.HttpClientUtil;
import com.epoint.core.utils.httpclient.HttpUtil;
import com.epoint.core.utils.security.TokenUtil;
import com.epoint.core.utils.string.StringUtil;
import com.epoint.frame.service.message.api.IMessagesCenterService;
import com.epoint.frame.service.message.entity.MessagesCenter;
import com.epoint.frame.service.metadata.code.api.IFrameCodeItemsService;
import com.epoint.frame.service.metadata.code.entity.CodeItems;
import com.epoint.frame.service.metadata.systemparameters.api.IFrameConfigService;
import com.epoint.frame.service.organ.ou.api.IFrameOuService;
import com.epoint.frame.service.organ.ou.entity.FrameOu;
import com.epoint.frame.service.organ.user.api.IFrameUserService;
import com.epoint.frame.service.organ.user.entity.FrameUser;
import com.epoint.workflow.service.common.entity.execute.WorkflowPvi;
import com.epoint.workflow.service.common.runtime.WorkflowParameter9;
import com.epoint.workflow.service.core.api.IWFEngineAPI9;
import com.epoint.workflow.service.external.api.IWFInstanceDataAPI9;
import com.epoint.ztb.apibase.api.IBiaoDuanAPI;
import com.epoint.ztb.apibase.entity.BdExcuteDTO;
import com.epoint.ztb.apibase.entity.BiaoDuanVO;
import com.epoint.ztb.bidflow.BidflowKeyNames;
import com.epoint.ztb.bidflow.api.IBidflowHandleService;
import com.epoint.ztb.bidflow.entity.BidflowExtVO;
import com.epoint.ztb.bidflow.entity.BidflowProcessVersion;
import com.epoint.ztb.bidflow.entity.WbBidflowLinkconfigs;
import com.epoint.ztb.cnbm.configmanage.ou.ICnbmFrameOuService;
import com.epoint.ztb.cnbm.configmanage.user.ICnbmFrameUserService;
import com.epoint.ztb.common.database.SqlBuilder;
import com.epoint.ztb.common.database.ZtbCommonDao;
import com.epoint.ztb.constants.TaskCodes;
import com.epoint.ztb.dataexchange.service.DataExService;
import com.epoint.ztb.enums.SysEnums;
import com.epoint.ztb.enums.TradeEnums;
import com.epoint.ztb.huiyuan.proxy.DynHuiYuanService;
import com.epoint.ztb.huiyuan.service.DB_HuiYuan_AlertInfo;
import com.epoint.ztb.jsgc.biddecision.zhongbiaogongshi.api.IZhongBiaoGongShiAPI;
import com.epoint.ztb.jsgc.biddecision.zhongbiaogongshi.entity.ZhongbiaodetailVO;
import com.epoint.ztb.log.DB_OperationLog;
import com.epoint.ztb.qy.configmanage.product.api.CnbmIProductDataService;
import com.epoint.ztb.qy.sysuserorgmap.api.SysUserOrgMap;
import com.epoint.ztb.qy.utils.CommonHandleUtils;
import com.epoint.ztb.workflow.api.IWorkflowApi;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import sun.misc.BASE64Encoder;

import javax.annotation.Resource;
import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.text.DecimalFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @author: dfwang
 * @Description: Erp系统和中建材招采系统接口对接Api服务类
 * @date: 2024-01-15
 */
@Service
public class ErpAndProcurementPlatformService
{

    private static final Logger logger = Logger.getLogger(ErpAndProcurementPlatformService.class);

    @Autowired
    protected IFrameUserService frameUserService;

    protected ZtbCommonDao service;

    @Autowired
    protected IFrameOuService frameOuService;

    @Autowired
    protected SysUserOrgMap sysUserOrgMap;

    @Autowired
    protected ICnbmFrameOuService cnbmFrameOuService;

    @Autowired
    protected ICnbmFrameUserService cnbmFrameUserService;

    @Autowired
    protected CnbmIProductDataService cnbmIProductDataService;

    @Autowired
    IFrameOuService iFrameOuService;

    @Autowired
    protected ICnbmFrameUserService iCnbmFrameUserService;

    @Resource
    protected IBiaoDuanAPI biaoduanApi;

    @Autowired
    protected ICnbmTradePlatApi iCnbmTradePlatApi;

    @Autowired
    protected IZhongBiaoGongShiAPI zhongBiaoGongShiAPI;

    @Autowired
    protected IBidflowHandleService bidflowHandleService;

    @Resource
    protected IWFInstanceDataAPI9 instanceDataApi;

    @Autowired
    protected IWorkflowApi workflowApi;

    @Autowired
    protected IFrameCodeItemsService frameCodeItemsService;

    /**
     * 处理Erp系统将业务审批结果推送给招采系统数据
     *
     * @param requestJson
     *            ：
     * @return ：
     */
    public String dealErpToTradeplatData(String requestJson) {
        JSONObject requestJsonObject = JSONObject.parseObject(requestJson);
        String tradestep = StringUtil.isNotBlank(requestJsonObject.getString("tradestep"))
                ? requestJsonObject.getString("tradestep")
                : "";
        // 描述：根据环节业务处理不同的业务
        switch (tradestep) {
            case "1":
                // 只有新疆调用文件（招标项目）备案通过接口，更新状态，非招项目暂不考虑
                return approvalBiddingDocumentsHandle(requestJsonObject);
            case "2":
                return approvalWinningBidResultsHandle(requestJsonObject);
            case "3":
                return approvalHeTongData(requestJsonObject);
            case "5":
                return approvalBidsectioncAndidateHandle(requestJsonObject);
            default:
                return "不存在该业务环节，请选择正确的业务环节！";
        }
    }

    /**
     * 处理招标文件审批数据
     *
     * @param requestJsonObject
     *            ：
     * @return ：
     */
    private String approvalBiddingDocumentsHandle(JSONObject requestJsonObject) {
        // 描述：进行数据必填校验
        String msg = dataRequiredVerificationHandle(requestJsonObject);
        if (StringUtil.isNotBlank(msg)) {
            return msg;
        }
        String bidsectionno = requestJsonObject.getString("bidsectionno");
        String projectno = requestJsonObject.getString("projectno");

        System.out.println("bidsectionno" + bidsectionno);

        // 描述：查询招标文件信息
        Record record = queryBiddingDocumentInformationByBdno(bidsectionno);
        if (record == null) {
            List<Record> lstFileinfo = queryBiddingDocumentInformationByProno(projectno);
            if (lstFileinfo.size() > 1) {
                return "该项目编号下对应多标段包信息，请在传递标段包编号！";
            }
            else if (lstFileinfo.size() == 1) {
                record = lstFileinfo.get(0);
            }
            else {
                return "没有查询到对应的项目，请传递正确的数据！";
            }
        }
        if (!"2".equals(record.getStr("AuditStatus"))) {
            return "项目的备案状态异常,请在招采系统提交备案！";
        }
        // 描述：将招标文件数据修改为审核通过
        updateBiddingDocumentsToApproved(record,requestJsonObject);
        return "";
    }

    /**
     * 处理中标结果审批数据
     *
     * @param requestJsonObject
     *            ：
     * @return ：
     */
    private String approvalWinningBidResultsHandle(JSONObject requestJsonObject) {
        ZtbCommonDao instance = ZtbCommonDao.getInstance();
        try {
            // 描述：进行数据必填校验
            // 西南使用该接口，单标段接收
            String msg = dataRequiredVerificationHandle(requestJsonObject);
            if (StringUtil.isNotBlank(msg)) {
                return msg;
            }
            // 描述：进行数据准备
            String bidsectionno = requestJsonObject.getString("bidsectionno");
            String projectno = requestJsonObject.getString("projectno");

            boolean isfz = true;
            String strfs = instance.queryString(
                    "select zhaobiaofangshi from cg_biaoduaninfo where biaoduanno =? and statuscode = '3'",
                    bidsectionno);
            if ("G".equalsIgnoreCase(strfs) || "Q".equalsIgnoreCase(strfs)) {
                isfz = false;
            }

            // 描述：查询中标通知书数据，先用标段编号查询，查询不到，使用项目编号查询
            Record record = queryBidAnnouncementDataByBdno(bidsectionno, isfz);
            // record = record != null ? record :
            // queryBidAnnouncementDataByProno(projectno);
            if (record == null) {
                return "没有查询到对应的项目，请传递正确的数据！";
            }

            if (!"2".equals(record.getStr("auditstatus"))) {
                return "请先在招采系统中进行中标结果备案提交操作！";
            }

            // 描述：将中标通知书数据修改为审核通过
            updateBidAnnouncementToApproved(record,requestJsonObject,isfz);
            return "";
        }
        finally {
            if (instance != null) {
                instance.close();
            }
        }

    }

    /**
     * 处理合同签署业务环节审批数据
     *
     * @param requestJsonObject
     *            ：
     * @return ：
     */
    private String approvalHeTongData(JSONObject requestJsonObject) {
        ZtbCommonDao instance = ZtbCommonDao.getInstance();
        try {
            // 描述：进行数据必填校验
            // 西南使用该接口，单标段接收
            String msg = dataRequiredVerificationHandle(requestJsonObject);
            if (StringUtil.isNotBlank(msg)) {
                return msg;
            }
            // 描述：进行数据准备
            String bidsectionno = requestJsonObject.getString("bidsectionno");
            String htguid = requestJsonObject.getString("htguid");

            if (StringUtil.isBlank(htguid)) {
                return "合同唯一标识必传！";
            }

            // 描述：查询数据，先用标段编号查询，查询不到，使用项目编号查询
            Record record = queryHeTongDataByBdno(bidsectionno,htguid);
            if (record == null) {
                return "没有查询到对应的项目，请传递正确的数据！";
            }

            if (!"2".equals(record.getStr("auditstatus"))) {
                return "请先在招采系统中进行合同签署提交操作！";
            }

            // 描述：将合同签署数据修改为审核通过
            updateHeTongData(record,requestJsonObject);
            return "";
        }
        finally {
            if (instance != null) {
                instance.close();
            }
        }

    }

    /**
     * 根据标段标编号查询合同签署数据
     *
     * @return ：
     */
    protected Record queryHeTongDataByBdno(String biaoduanno,String htguid) {
        ZtbCommonDao instance = ZtbCommonDao.getInstance();
        try {
            String sqlByBdGuid = "select * from CG_HeTongInfo cz  where biaoduanguid in (select biaoduanguid from cg_biaoduaninfo cb where biaoduanno = ? and statuscode  = '3') and rowguid = ?";
            return instance.find(sqlByBdGuid, Record.class, biaoduanno,htguid);
        }
        finally {
            if (instance != null) {
                instance.close();
            }
        }
    }

    /**
     * 合同签署修改为审核通过
     *
     * @param record
     *            ：
     */
    protected void updateHeTongData(Record record,JSONObject requestJsonObject) {
        ZtbCommonDao instance = ZtbCommonDao.getInstance();
        if("0".equals(requestJsonObject.getString("tradestatus"))){//审核不通过
            // 重置工作台数据(进行中)
            bidflowHandleService.resetMenuNodeitemByRowguid(record.getStr("rowguid"));
            instance.execute("update CG_HeTongInfo set auditstatus=?,currentstepname=?,operatedate = ? where rowguid=? ",
                    TradeEnums.AuditStatus.审核不通过.getValue(), "初始信息录入",new Date(), record.getStr("rowguid"));
            workflowApi.resetWorkflow(record.getStr("pvi_guid"), record.getStr("sbr_code"), requestJsonObject.getString("tradereason"));
            DB_OperationLog.addOperationLog(record.getStr("rowguid"), SysEnums.SubSysName.企业招采,
                    SysEnums.OperName.回第一步, "Erp审核不通过更新合同签署状态，合同签署唯一标志@" + record.getStr("rowguid") + "审核不通过时间@"
                            + EpointDateUtil.convertDate2String(new Date(), "yyyy-MM-dd HH:mm:ss"),
                    "合同签署撤回修改");
        }
        else {
            try {
                String sqlUpdateZbjg = "update CG_HeTongInfo set auditstatus = '3',operatedate = ? where rowguid = ? ";
                instance.execute(sqlUpdateZbjg, new Date(), record.getStr("rowguid"));
                //非招的合同签署 判断 如果是框架协议 要将中标单位数据填入 cg_hetonginfo中的cjgysid,cjgys
                if("2".equals(record.getStr("kjxytype"))){
                    List<ZhongbiaodetailVO> infoList = zhongBiaoGongShiAPI.checkIsZhongBiao(record.getStr("biaoduanguid"),record.getStr("zbdanweiguid"))
                            ? zhongBiaoGongShiAPI.findListByBDGuidAndDWGuid(record.getStr("biaoduanguid"),record.getStr("zbdanweiguid")) : zhongBiaoGongShiAPI.findListByBiaoDuanGuid(record.getStr("biaoduanguid"));
                    for (ZhongbiaodetailVO cgZhongbiaodetail : infoList) {
                        record.set("cjgys",cgZhongbiaodetail.getDanweiname());
                        record.set("cjgysid",cgZhongbiaodetail.getDanweiguid());
                    }
                    // 更新采购item的东西
                    String rowguid = record.getStr("biaoduanguid");
                    String sql = " select rowguid from cg_caigouitem where biaoduanguid = ?";
                    List<String> list = instance.findList(sql, String.class, rowguid);
                    // 这里还是填授标数量
                    String updateSql = " update cg_caigouitem set contractid = ?,sysl = AwardQuantity  where rowguid = ?";
                    if (EpointCollectionUtils.isNotEmpty(list)) {
                        for (String id : list) {
                            instance.execute(updateSql,record.getStr("rowguid"),id);
                        }
                    }
                }
                DataExService.Send2DataExchange(record, TradeEnums.DataExChangeJSGC.合同.getValue());
                if ("Z".equals(biaoduanApi.findByBiaoDuanGuid(record.getStr("biaoduanguid")).getZhaobiaofangshi())) {
                    String strSql = "update CG_TouBiaoDanWei set BZJIsBack='1' where BiaoDuanGuid=? and DanWeiGuid=? and isnull(BZJIsBack,' ') =' ' ";
                    String strSql2 = "update CG_BiaoDuanInfo set IsZBRTuibzj='1',zbrtuibzjdate = ?  where BiaoDuanGuid=? ";
                    ZtbCommonDao.getInstance().execute(strSql, new Object[]{record.getStr("biaoduanguid"), record.getStr("zbdanweiguid")});
                    ZtbCommonDao.getInstance().execute(strSql2, new Object[]{new Date(), record.getStr("biaoduanguid")});
                    String bdsecondtype = biaoduanApi.findByBiaoDuanGuid(record.getStr("biaoduanguid")).getBdsecondtype();
                    int intervalDays = 0;
                    Date htqsTime = null;
                    Date heTongEnd = null;
                    if("2".equals(record.getStr("kjxytype"))) {
                        htqsTime = record.get("htqstime");
                        heTongEnd = record.get("htcompletetime");
                        intervalDays = EpointDateUtil.getIntervalDays(heTongEnd, htqsTime);
                        instance.execute("update CG_HeTongInfo set Htqixian = ? where rowguid = ? ", String.valueOf(intervalDays), record.getStr("rowguid"));
                    } else {
                        // 对合同结算金额和合同完成时间进行赋值（根据合同签署时间和合同期限计算完成时间），施工和监理的标段需要销号完成后反写，
                        htqsTime = record.getDate("htqstime");
                        if (htqsTime == null) {
                            heTongEnd = new Date();
                        } else {
                            int addDay = 1;
                            String heqx = record.getStr("htqixian");
                            if (StringUtil.isNotBlank(heqx)) {
                                addDay = Integer.parseInt(heqx);
                            }
                            heTongEnd = EpointDateUtil.addDay(htqsTime, addDay);
                        }
                        instance.execute("update CG_HeTongInfo set htcompletetime = ? where rowguid = ? ",heTongEnd, record.getStr("rowguid"));
                    }

                    instance.execute("update CG_HeTongInfo set Htjsjine = ? where rowguid = ? ", record.getStr("htjine"), record.getStr("rowguid"));
                    String strDanWeiType = "";
                    if (TradeEnums.getExactBDSecondType(SysEnums.DanWeiType.招标代理.getValue()).equals(bdsecondtype)) {
                        strDanWeiType = SysEnums.DanWeiType.招标代理.getValue();
                    }
                    else {
                        strDanWeiType = SysEnums.DanWeiType.投标人.getValue();
                    }

                    // 合同签署完成后更新业绩表的合同金额、合同期限、合同签署时间等值
                    new DynHuiYuanService().UpdateYeJiAfterHeTong(strDanWeiType, record.getStr("Biaoduanguid"),
                            record.getStr("Zbdanweiguid"), record.get("Htjine"),
                            record.getStr("Htqixian"), record.get("Htqstime"));
                }
                //推送合同备案完成消息给第三方系统
                iCnbmTradePlatApi.pushhtmeesage(record.getStr("rowguid"),"1","3");

                try{
                    // 增加事件调用，埋点
                    logger.info("合同审核结果同步接口开始推送消息给三方平台：" + record.getStr("biaoduanguid"));
                    push_noticeinfo_cnbm(record.getStr("biaoduanguid"),"3",record.getStr("rowguid"),"btnEnd");
                    logger.info("合同审核结果同步接口开始推送消息给三方平台完成：" + record.getStr("biaoduanguid"));
                }catch (Exception ex){
                    ex.printStackTrace();
                }

                // 清除代办事宜
                try {
                    IMessagesCenterService iMessagesCenterService = ContainerFactory.getContainInfo()
                            .getComponent(IMessagesCenterService.class);

                    List<MessagesCenter> messagesCenterList = iMessagesCenterService
                            .listWaitHandleByPviguid(record.getStr("pvi_guid"));
                    for (MessagesCenter message : messagesCenterList) {
                        iMessagesCenterService.deleteMessageForEver(message.getMessageItemGuid(), message.getTargetUser());
                    }

                } catch (Exception ex) {
                    logger.info("合同签署审核通过清除待办报错：" + ex);
                }

                // 更新工作流
                try {
                    IWFEngineAPI9 operationApi = ContainerFactory.getContainInfo().getComponent(IWFEngineAPI9.class);
                    WorkflowParameter9 workflowParameter9 = new WorkflowParameter9();
                    workflowParameter9.setOperateType(45);
                    workflowParameter9.setSendGuid("");
                    workflowParameter9.setOpinion("Erp审核通过自动同步");
                    workflowParameter9.setProcessVersionInstanceGuid(record.getStr("pvi_guid"));
                    operationApi.operate(JSON.toJSONString(workflowParameter9));

                } catch (Exception ex) {
                    logger.info("合同签署审核通过推进工作流报错：" + ex);
                }

            } finally {
                if (instance != null) {
                    instance.close();
                }
            }
        }
    }

    /**
     * 处理授标方案审批数据
     *
     * @param requestJsonObject
     *            ：
     * @return ：
     */
    private String approvalBidsectioncAndidateHandle(JSONObject requestJsonObject) {
        ZtbCommonDao instance = ZtbCommonDao.getInstance();
        try {
            // 描述：进行数据必填校验
            // 西南使用该接口，单标段接收
            String msg = dataRequiredVerificationHandle(requestJsonObject);
            if (StringUtil.isNotBlank(msg)) {
                return msg;
            }
            // 描述：进行数据准备
            String bidsectionno = requestJsonObject.getString("bidsectionno");

            boolean isfz = true;
            String strfs = instance.queryString(
                    "select zhaobiaofangshi from cg_biaoduaninfo where biaoduanno =? and statuscode = '3'",
                    bidsectionno);
            if ("G".equalsIgnoreCase(strfs) || "Q".equalsIgnoreCase(strfs)) {
                isfz = false;
            }

            // 描述：查询数据，先用标段编号查询，查询不到，使用项目编号查询
            Record record = queryBidsectioncAndidateByBdno(bidsectionno, isfz);
            if (record == null) {
                return "没有查询到对应的项目，请传递正确的数据！";
            }

            if (!"2".equals(record.getStr("auditstatus"))) {
                if(isfz){
                    return "请先在招采系统中进行推荐成交提交操作！";
                }
                else{
                    return "请先在招采系统中进行中标候选人公示提交操作！";
                }
            }

            // 描述：将中标候选人公示数据修改为审核通过
            updateBidsectioncAndidate(record,requestJsonObject, isfz);
            return "";
        }
        finally {
            if (instance != null) {
                instance.close();
            }
        }

    }


    public List<JSONObject> dealBidsectionResultData2(JSONObject requestJsonObject) {

        ArrayList<JSONObject> returnResultList = new ArrayList<>();

        // 查询 公招 -> 中标结果公告(CG_ZhongBiaoGS)
        List<Record> gzresoultList = getCGZhongBiaoGSInfo(requestJsonObject);
        gzresoultList = EpointCollectionUtils.isEmpty(gzresoultList) ? new ArrayList<>() : gzresoultList;

        // 描述：进行数据处理
        for (Record gzrecord : gzresoultList) {
            // 只有中标结果公告审核通过才会拼接 , 西南水泥则待审核和审核通过都拼
            if ("3".equals(gzrecord.getStr("auditstatus")) || ("西南水泥".equals(gzrecord.getStr("companycode"))
                    && ("2".equals(gzrecord.getStr("auditstatus"))))) {
                // 描述：数据获取
                JSONObject gzjsonObject = gzobtainTenderingDataHandle(gzrecord,requestJsonObject);
                returnResultList.add(gzjsonObject);
            }
        }

        //非招(不包含单源，多源) -> 成交结果通知/成交通知(CG_ZhongBiaoJieGuo)
        List<Record> fzresoultList = getCGZhongBiaoJieGuoInfo(requestJsonObject);
        fzresoultList = EpointCollectionUtils.isEmpty(fzresoultList) ?  new ArrayList<>() : fzresoultList;

        // 描述：进行数据处理
        for (Record fzrecord : fzresoultList) {
            // 只有中标结果公告审核通过才会拼接 , 西南水泥则待审核和审核通过都拼
            if ("3".equals(fzrecord.getStr("auditstatus")) || ("西南水泥".equals(fzrecord.getStr("companycode"))
                    && ("2".equals(fzrecord.getStr("auditstatus"))))) {
                // 描述：数据获取
                JSONObject fzjsonObject = fzobtainTenderingDataHandle(fzrecord,requestJsonObject);
                returnResultList.add(fzjsonObject);
            }
        }

        //非招(不包含单源，多源) -> 协商报告
        List<Record>  dyresoultList = getDyresoultList(requestJsonObject);
        dyresoultList = EpointCollectionUtils.isEmpty(dyresoultList) ?  new ArrayList<>() : dyresoultList;

        // 描述：进行数据处理
        for (Record dyrecord : dyresoultList) {
            //只有中标结果公告审核通过才会拼接 , 西南水泥则待审核和审核通过都拼
            if("1".equals(dyrecord.getStr("pingbiaostatus"))){
                // 描述：数据获取
                JSONObject dyjsonObject = dyobtainTenderingDataHandle(dyrecord,requestJsonObject);
                returnResultList.add(dyjsonObject);
            }
        }

        return returnResultList;
    }

    protected List<Record> getDyresoultList(JSONObject requestJsonObject) {
        SqlBuilder dyBuilder = new SqlBuilder();
        dyBuilder.append(
                "select c.SBR_Code, b.pingbiaostatus,d.companycode,b.projectname,b.projectno,c.jsdwlxr,c.jsdwlxrtel,b.biaoduanguid,b.biaoduanname,b.biaoduanno,b.fabaocontent "
                        + ",b.zhaobiaofangshi,b.isusewebztb,b.touzigusuan,d.extinfo,b.bjfanganguid,d.jianshedanwei,b.sbr_name ,b.bjlunci,b.operateusername,b.sbr_code,b.sbr_unitguid,c.ppexecute,d.sbr_code as memberUserCode, c.sbr_unitguid as memberOrgCode"
                        + " from  cg_biaoduaninfo b  " + "left join cg_chubufabao c on b.fabaoguid = c.fabaoguid "
                        + "left join cg_projectinfo d on b.projectguid =d.projectguid " + "where b.statuscode ='3'  "
                        + "and b.zhaobiaofangshi in ('DanY','DuoY') ");
        addFilterConditions3(requestJsonObject, dyBuilder);
        // 描述：进行中标通知书数据查询
        return ZtbCommonDao.getInstance().findList(dyBuilder.getSql(), Record.class, dyBuilder.getParams());

    }

    protected List<Record> getCGZhongBiaoJieGuoInfo(JSONObject requestJsonObject) {
        SqlBuilder fzBuilder = new SqlBuilder();
        fzBuilder.append(
                "select c.SBR_Code, a.auditstatus,d.companycode,b.projectname,b.projectno,c.jsdwlxr,c.jsdwlxrtel,b.biaoduanguid,b.biaoduanname,b.biaoduanno,b.fabaocontent "
                        + ",b.zhaobiaofangshi,b.isusewebztb,b.touzigusuan,d.extinfo,b.bjfanganguid,d.jianshedanwei,d.jianshedanweiguid,b.sbr_name ,b.bjlunci,a.operateusername,a.sbr_code,a.sbr_unitguid,c.ppexecute,d.sbr_code as memberUserCode, c.sbr_unitguid as memberOrgCode"
                        + " from cg_zhongbiaojieguo a  join cg_biaoduaninfo b  "
                        + " on a.biaoduanguid = b.biaoduanguid  left join cg_chubufabao c on b.fabaoguid = c.fabaoguid "
                        + " left join cg_projectinfo d on b.projectguid =d.projectguid " + " where b.statuscode ='3'  "
                        + " and b.zhaobiaofangshi not in ('G','Q','DanY','DuoY') ");
        addFilterConditions2(requestJsonObject, fzBuilder);
        // 描述：进行中标通知书数据查询
        return ZtbCommonDao.getInstance().findList(fzBuilder.getSql(), Record.class, fzBuilder.getParams());

    }

    protected List<Record> getCGZhongBiaoGSInfo(JSONObject requestJsonObject) {
        SqlBuilder gzBuilder = new SqlBuilder();
        gzBuilder.append(
                "select c.SBR_Code, a.auditstatus,d.companycode, b.projectname,b.projectno,c.jsdwlxr,c.jsdwlxrtel,b.biaoduanguid,b.biaoduanname,b.biaoduanno,b.fabaocontent  "
                        + ",b.zhaobiaofangshi,b.isusewebztb,b.touzigusuan,d.extinfo,d.jianshedanwei,d.jianshedanweiguid,b.bjfanganguid,b.sbr_name ,b.bjlunci,a.operateusername,a.sbr_code,c.ppexecute,a.sbr_unitguid,d.sbr_code as memberUserCode, c.sbr_unitguid as memberOrgCode"
                        + " from CG_ZhongBiaoGS a  join cg_biaoduaninfo b on a.showbiaoduanno = b.biaoduanno  "
                        + " left join cg_chubufabao c on b.fabaoguid = c.fabaoguid "
                        + " left join cg_projectinfo d on b.projectguid =d.projectguid " + "where b.statuscode ='3'  "
                        + " and b.zhaobiaofangshi in ('G','Q') ");
        // info.set("companycode", "西南水泥");
        // and a.auditstatus ='3'
        addFilterConditions2(requestJsonObject, gzBuilder);
        // 描述：进行中标通知书数据查询
        return ZtbCommonDao.getInstance().findList(gzBuilder.getSql(), Record.class, gzBuilder.getParams());
    }

    /**
     * 获取数据处理
     *
     * @param jibeninfo
     *            ：
     * @return ：
     */
    private JSONObject gzobtainTenderingDataHandle(Record jibeninfo,JSONObject requestJsonObject) {
        // 描述：数据分装项目基本信息
        JSONObject projectData = obtainEncapsulationProjectInformation2(jibeninfo,requestJsonObject);

        // 描述：数据封装中标单位信息
        List<JSONObject> bidderDataList = gzobtainEncapsulationInformationOnWinningBidder(jibeninfo);

        projectData.put("bidder", bidderDataList);

        return projectData;
    }

    /**
     * 获取数据处理
     * @param jibeninfo
     * @param requestJsonObject
     * @return
     */
    private JSONObject fzobtainTenderingDataHandle(Record jibeninfo,JSONObject requestJsonObject) {
        // 描述：数据分装项目基本信息
        JSONObject projectData = obtainEncapsulationProjectInformation2(jibeninfo,requestJsonObject);

        // 描述：数据封装中标单位信息
        List<JSONObject> bidderDataList = fzobtainEncapsulationInformationOnWinningBidder(jibeninfo);

        projectData.put("bidder", bidderDataList);

        return projectData;
    }

    /**
     * 单源多源获取数据处理
     * @param jibeninfo
     * @param requestJsonObject
     * @return
     */
    private JSONObject dyobtainTenderingDataHandle(Record jibeninfo,JSONObject requestJsonObject) {
        // 描述：数据分装项目基本信息
        JSONObject projectData = obtainEncapsulationProjectInformation2(jibeninfo,requestJsonObject);

        // 描述：数据封装中标单位信息
        List<JSONObject> bidderDataList = dyobtainEncapsulationInformationOnWinningBidder(jibeninfo);

        projectData.put("bidder", bidderDataList);

        return projectData;
    }

    /**
     * 封装单位信息
     *
     * @param jibeninfo
     *            ：
     * @return ：
     */
    private List<JSONObject> gzobtainEncapsulationInformationOnWinningBidder(Record jibeninfo) {

        // 描述：进行数据准备
        List<JSONObject> bidderDataList = new ArrayList<>();
        String biaoduanguid = jibeninfo.getStr("biaoduanguid");
        String bjlunci = StringUtil.isBlank(jibeninfo.getStr("bjlunci")) ? "1" : jibeninfo.getStr("bjlunci");

        List<Record> dataBean_BJFangAnlst = getBaoJiaoFangAnItem(jibeninfo.getStr("bjfanganguid"));

        // 描述：获取所有单位信息
        List<Record> allUnitInformationList = obtainAllBiddingUnitInformation(biaoduanguid);

        // 描述：查询中标单位信息
        List<Record> zbInfoList = gzobtainInformationOnAllWinningBidders(biaoduanguid);

        // 描述：收集中标单位的单位guid
        HashSet<String> zbDanWeiGuidSet = collectIdentificationWinningBidder(zbInfoList);

        // 查询文件上传记录
        List<Record> tbfileinfoList = ZtbCommonDao.getInstance().findList(
                "select danweiguid from cg_toubiaofileinfo ct where biaoduanguid  = ?  ", Record.class, biaoduanguid);
        if (EpointCollectionUtils.isEmpty(tbfileinfoList)) {
            tbfileinfoList = new ArrayList<>();
        }

        if (allUnitInformationList != null) {
            for (Record record : allUnitInformationList) {
                // 获取单位编码(单位私库)
                JSONObject memberData = new JSONObject();
                JSONObject supplierCodes = new JSONObject();
                memberData.put("orgCode", frameOuService.getFrameOu(jibeninfo.getStr("sbr_unitguid")).getOucode());
                supplierCodes.put("supplierCodes", record.getStr("standardgyscode"));
                memberData.put("requestBody", supplierCodes);
                CnbmSuppilerPlatformWebUtil spWebUtil = new CnbmSuppilerPlatformWebUtil();
                // 调用中建材供应商中台接口获取相关数据
                String responseDataJson = spWebUtil.getSupplierMemberCodes(memberData.toJSONString());
                JSONObject responseDataObject = JSONObject.parseObject(responseDataJson);
                JSONArray params = CommonHandleUtils.getJsonArrayValueDefault(responseDataObject,"data",null);
                String memberCode = "";
                if (EpointCollectionUtils.isNotEmpty(params)) {
                    for (int i = 0; i < params.size(); i++) {
                        JSONObject jsonObject = params.getJSONObject(i);
                        memberCode =  CommonHandleUtils.getStringValueDefault(jsonObject,"memberCode","");
                    }
                }
                logger.info("erp02接口公招供应商中台返回数据memberCode:" + memberCode);

                // 描述：进行数据准备
                String danweiguid = record.getStr("danweiguid");
                String unitorgnum = record.getStr("standardgyscode");
                String danweiname = record.getStr("danweiname");
                String danweicode = record.getStr("unitorgnum");
                String membercode = memberCode;// 单位编码(单位私库)

                // 描述：判断单位是否中标
                // String isIn = determineWhetherUnitHasWonBid(zbDanWeiGuidSet, danweiguid);
                String isIn = "0";
                // 描述：进行变量准备
                String huizongmark = "0";
                String toubiaoprice = "0";
                String zhongbiaoprice = "0";
                if (zbDanWeiGuidSet.contains(danweiguid)) {
                    isIn = "1";
                    zhongbiaoprice = record.getStr("zhongbiaoprice");
                }

                // 描述：在评标情况里边查询汇总分和投标报价
                Record pbRecord = queryEvaluationData(biaoduanguid, danweiguid);
                // 描述：进行判空处理
                huizongmark = CommonHandleUtils.getStringValueDefault(pbRecord, huizongmark, "0");
                toubiaoprice = CommonHandleUtils.getStringValueDefault(pbRecord, toubiaoprice, "0");

                // 描述：封装行项目信息
                List<JSONObject> lineiteminfoList = gzobtainEncapsulationLineItemInformation(jibeninfo, record, isIn);

                // 增加报价项
                JSONObject tempbjdata = gettouBiaoBaoJia(biaoduanguid,danweiguid,"", bjlunci, dataBean_BJFangAnlst);

                // 描述：数据封装
                JSONObject bidder = basicInformationAssemblyUnit2(danweiguid, unitorgnum, danweiname, danweicode,
                        membercode, isIn, huizongmark, toubiaoprice, zhongbiaoprice, "0",EpointCollectionUtils.isEmpty(lineiteminfoList) ? tempbjdata : new JSONObject());


                if (EpointCollectionUtils.isNotEmpty(lineiteminfoList)) {
                    bidder.put("lineiteminfo", lineiteminfoList);
                }
                else {
                    bidder.put("lineiteminfo", new ArrayList<>());
                }

                // 增加报价和文件是否上传标签 2024-04-23
                String istbbj = "0";
                String isuploadfile = "0";
                for (Record rec : tbfileinfoList) {
                    if (danweiguid.equals(rec.getStr("danweiguid"))) {
                        isuploadfile = "1";
                        break;
                    }
                }
                bidder.put("istbbj", istbbj); // 是否报价
                bidder.put("isuploadfile", isuploadfile); // 是否上传投标文件

                bidderDataList.add(bidder);
            }
        }
        return bidderDataList;
    }

    /**
     * 封装单位信息
     *
     * @param jibeninfo
     *            ：
     * @return ：
     */
    private List<JSONObject> fzobtainEncapsulationInformationOnWinningBidder(Record jibeninfo) {

        // 描述：进行数据准备
        List<JSONObject> bidderDataList = new ArrayList<>();
        String biaoduanguid = jibeninfo.getStr("biaoduanguid");
        String zhaobiaofangshi = jibeninfo.getStr("zhaobiaofangshi");
        String bjlunci = StringUtil.isBlank(jibeninfo.getStr("bjlunci")) ? "1" : jibeninfo.getStr("bjlunci");
        List<Record> dataBean_BJFangAnlst = getBaoJiaoFangAnItem(jibeninfo.getStr("bjfanganguid"));

        // 描述：获取所有单位信息
        List<Record> allUnitInformationList = new ArrayList<>();
        if ("HG".equals(zhaobiaofangshi)) {
            allUnitInformationList = obtainAllBiddingUnitInformation_HG(biaoduanguid);
        }
        else {
            allUnitInformationList = obtainAllBiddingUnitInformation(biaoduanguid);
        }

        if (EpointCollectionUtils.isEmpty(allUnitInformationList)) {
            allUnitInformationList = new ArrayList<>();
        }
        // 描述：查询中标单位信息
        List<Record> zbInfoList = new ArrayList<>();
        // 合作 , 竞价 走特殊赋值逻辑
        if ("HG".equals(zhaobiaofangshi) || "B".equals(zhaobiaofangshi)) {
            zbInfoList = fzobtainInformationOnAllWinningBidders_teshu(biaoduanguid);
        }
        else {
            zbInfoList = fzobtainInformationOnAllWinningBidders(biaoduanguid, bjlunci);
        }
        if (EpointCollectionUtils.isEmpty(zbInfoList)) {
            zbInfoList = new ArrayList<>();
        }
        // 查询最后一轮报价总信息
        List<Record> lastbjinfoList = ZtbCommonDao.getInstance().findList(
                "select * from CG_TouBiaoBaoJia a where  ifnull(ItemGuid,'')='' and BIAODUANGUID =?  and BJLunCi= ? ",
                Record.class, biaoduanguid, bjlunci);
        if (EpointCollectionUtils.isEmpty(lastbjinfoList)) {
            lastbjinfoList = new ArrayList<>();
        }

        // 查询报价列表
        List<Record> bjinfoList = ZtbCommonDao.getInstance().findList(
                "select danweiguid from CG_TouBiaoBaoJia a where  ifnull(ItemGuid,'')='' and BIAODUANGUID =?  and BJLunCi= '1' and  IsSubmit  = '1' ",
                Record.class, biaoduanguid);
        if (EpointCollectionUtils.isEmpty(bjinfoList)) {
            bjinfoList = new ArrayList<>();
        }

        // 查询文件上传记录
        List<Record> tbfileinfoList = ZtbCommonDao.getInstance().findList(
                "select danweiguid from cg_toubiaofileinfo ct where biaoduanguid  = ?  ", Record.class, biaoduanguid);
        if (EpointCollectionUtils.isEmpty(tbfileinfoList)) {
            tbfileinfoList = new ArrayList<>();
        }

        // 查询竞价项目的竞价记录
        List<Record> jjdwList = new ArrayList<>();
        JSONArray jsonArrayJJHis = new JSONArray();
        JSONObject jsonJJHis = new JSONObject();
        if ("B".equals(zhaobiaofangshi)) {

            IFrameConfigService frameConfigService = ContainerFactory.getContainInfo()
                    .getComponent(IFrameConfigService.class);
            String url = frameConfigService.getFrameConfigValue("cnbm_jj_baojiaresulturl");

            jjdwList = ZtbCommonDao.getInstance().findList(
                    "select toubiaorenguid,toubiaorenname,toubiaorenno from JQXT_TouBiaoRen where biaodiguid =  ?  ",
                    Record.class, biaoduanguid);
            Map<String, Object> param = new HashMap();
            try {
                param.put("token", TokenUtil.createToken("epointbid_jingjia"));
                param.put("data", biaoduanguid);
                param.put("num", "1");
                // String info =
                // HttpUtil.doPost("http://211.93.5.252:8080/epointbid-jingjia/rest/jingjiaresultinteractive/getbaojiaresultlist",
                // param);
                String info = HttpUtil.doPost(url, param);

                jsonJJHis = JSONObject.parseObject(info);
                if (StringUtil.isNotBlank(jsonJJHis.getJSONObject("responseData").getJSONObject("userArea")
                        .getJSONArray("jqxtbaojiahistroy"))) {
                    jsonArrayJJHis = jsonJJHis.getJSONObject("responseData").getJSONObject("userArea")
                            .getJSONArray("jqxtbaojiahistroy");
                }
            }
            catch (Exception var11) {
                logger.error("竞价接口调用出错:", var11);
            }
        }

        // 描述：收集中标单位的单位guid
        HashSet<String> zbDanWeiGuidSet = collectIdentificationWinningBidder(zbInfoList);
        if (EpointCollectionUtils.isNotEmpty(allUnitInformationList)) {
            for (Record record : allUnitInformationList) {
                // 获取单位编码(单位私库)
                JSONObject memberData = new JSONObject();
                JSONObject supplierCodes = new JSONObject();
                memberData.put("orgCode", frameOuService.getFrameOu(jibeninfo.getStr("sbr_unitguid")).getOucode());
                supplierCodes.put("supplierCodes", record.getStr("standardgyscode"));
                memberData.put("requestBody", supplierCodes);
                CnbmSuppilerPlatformWebUtil spWebUtil = new CnbmSuppilerPlatformWebUtil();
                // 调用中建材供应商中台接口获取相关数据
                String responseDataJson = spWebUtil.getSupplierMemberCodes(memberData.toJSONString());
                JSONObject responseDataObject = JSONObject.parseObject(responseDataJson);
                JSONArray params = responseDataObject != null ? responseDataObject.getJSONArray("data") : null;
                String memberCode = "";
                if(EpointCollectionUtils.isNotEmpty(params)) {
                    for (int i = 0; i < params.size(); i++) {
                        JSONObject jsonObject = params.getJSONObject(i);
                        memberCode = jsonObject != null ? jsonObject.getString("memberCode") : "";
                    }
                }
                logger.info("erp02接口非招供应商中台返回数据memberCode:"+memberCode);

                // 描述：进行数据准备
                String danweiguid = record.getStr("danweiguid");
                String unitorgnum = record.getStr("standardgyscode");
                String danweiname = record.getStr("danweiname");
                String danweicode = record.getStr("unitorgnum");
                String membercode = memberCode;// 单位编码(单位私库)

                // 描述：判断单位是否中标
                String isIn = "0";
                // 描述：进行变量准备
                String huizongmark = "0";
                String toubiaoprice = "0";
                String zhongbiaoprice = "0";
                double zhongbiaonum = 0.0;

                // 合作 , 竞价 走特殊赋值逻辑
                if ("HG".equals(zhaobiaofangshi) || "B".equals(zhaobiaofangshi)) {
                    // 投标金额赋值
                    toubiaoprice = record.getStr("toubiaoprice");
                    // 中标价赋值
                    if (zbDanWeiGuidSet.contains(danweiguid)) {
                        isIn = "1";
                        for (Record zbinfo : zbInfoList) {
                            if (danweiguid.equals(zbinfo.getStr("danweiguid"))) {
                                zhongbiaoprice = zbinfo.getStr("zhongbiaomoney");
                                break;
                            }
                        }
                    }

                }
                else {
                    if (zbDanWeiGuidSet.contains(danweiguid)) {
                        isIn = "1";
                        for (Record zbinfo : zbInfoList) {
                            if (danweiguid.equals(zbinfo.getStr("danweiguid"))) {
                                zhongbiaoprice = zbinfo.getStr("zhongbiaomoney");
                                zhongbiaonum += StringUtil.isNotBlank(zbinfo.getStr("awardquantity"))
                                        ? zbinfo.getDouble("awardquantity")
                                        : 0.0;
                            }
                        }
                    }
                    // 投标金额赋值
                    for (Record rec : lastbjinfoList) {
                        if (danweiguid.equals(rec.getStr("danweiguid"))) {
                            toubiaoprice = rec.getStr("itemtotalprice");
                            break;
                        }
                    }
                }

                // 描述：数据封装
                DecimalFormat df = new DecimalFormat("#####0.0000");
                String format = df.format(zhongbiaonum);

                // 描述：封装行项目信息
                List<JSONObject> lineiteminfoList = fzobtainEncapsulationLineItemInformation(jibeninfo, record, isIn,
                        zbInfoList);

                // 增加报价项
                JSONObject tempbjdata = gettouBiaoBaoJia(biaoduanguid,danweiguid,"", bjlunci, dataBean_BJFangAnlst);

                JSONObject bidder = basicInformationAssemblyUnit2(danweiguid, unitorgnum, danweiname, danweicode,
                        membercode, isIn, huizongmark, toubiaoprice, zhongbiaoprice, format,EpointCollectionUtils.isEmpty(lineiteminfoList) ? tempbjdata : new JSONObject());


                if (EpointCollectionUtils.isNotEmpty(lineiteminfoList)) {
                    bidder.put("lineiteminfo", lineiteminfoList);
                }
                else {
                    bidder.put("lineiteminfo", new ArrayList<>());
                }

                if ("HG".equals(zhaobiaofangshi)) {
                    // 合作谈判项目默认都是报价单位
                    bidder.put("istbbj", "1"); // 是否报价
                    bidder.put("isuploadfile", "1"); // 是否上传投标文件
                }
                else if ("B".equals(zhaobiaofangshi)) {
                    String istbbj = "0";
                    // 查看报价历史
                    if (jjdwList == null) {
                        bidder.put("istbbj", "0"); // 是否报价
                    }
                    else {
                        List<Record> jjdw = jjdwList.stream().filter(d -> d.getStr("toubiaorenguid").equals(danweiguid))
                                .collect(Collectors.toList());
                        if (jjdw.size() > 0) {
                            if (jsonArrayJJHis == null) {
                                bidder.put("istbbj", "0"); // 是否报价
                            }
                            else {
                                for (int i = 0; i < jsonArrayJJHis.size(); i++) {
                                    if (jsonArrayJJHis.getJSONObject(i).getString("toubiaorenno")
                                            .equalsIgnoreCase(jjdw.get(0).getStr("toubiaorenno"))) {
                                        istbbj = "1";
                                        break;
                                    }
                                }
                                bidder.put("istbbj", istbbj); // 是否报价
                            }
                        }
                        else {
                            bidder.put("istbbj", "0"); // 是否报价
                        }
                    }
                    // 竞价项目是默认是0
                    bidder.put("isuploadfile", "0"); // 是否上传投标文件
                }
                else {
                    String istbbj = "0";
                    String isuploadfile = "0";
                    for (Record rec : bjinfoList) {
                        if (danweiguid.equals(rec.getStr("danweiguid"))) {
                            istbbj = "1";
                            break;
                        }
                    }
                    for (Record rec : tbfileinfoList) {
                        if (danweiguid.equals(rec.getStr("danweiguid"))) {
                            isuploadfile = "1";
                            break;
                        }
                    }
                    bidder.put("istbbj", istbbj); // 是否报价
                    bidder.put("isuploadfile", isuploadfile); // 是否上传投标文件
                }
                bidderDataList.add(bidder);
            }
        }
        return bidderDataList;
    }

    /**
     * 封装单位信息
     *
     * @param jibeninfo
     *            ：
     * @return ：
     */
    private List<JSONObject> dyobtainEncapsulationInformationOnWinningBidder(Record jibeninfo) {

        // 描述：进行数据准备
        List<JSONObject> bidderDataList = new ArrayList<>();
        String biaoduanguid = jibeninfo.getStr("biaoduanguid");
        String zhaobiaofangshi = jibeninfo.getStr("zhaobiaofangshi");
        String bjlunci = StringUtil.isBlank(jibeninfo.getStr("bjlunci")) ? "1" : jibeninfo.getStr("bjlunci");
        List<Record> dataBean_BJFangAnlst = getBaoJiaoFangAnItem(jibeninfo.getStr("bjfanganguid"));

        // 描述：获取所有单位信息
        String jsdwSql = "select distinct (tb.DanWeiGuid),tb.*,hu.standardgyscode from cg_toubiaodanwei tb join huiyuan_unitcominfo hu on tb.DanWeiGuid  = hu.danweiguid where tb.biaoduanguid=?  ";
        List<Record> allUnitInformationList = ZtbCommonDao.getInstance().findList(jsdwSql, Record.class, biaoduanguid);
        if (EpointCollectionUtils.isEmpty(allUnitInformationList)) {
            allUnitInformationList = new ArrayList<>();
        }
        // 描述：查询中标单位信息
        List<Record> zbInfoList = new ArrayList<>();
        // 单源 , 多源 走特殊赋值逻辑
        String zbdwSql = "select a.DANWEINAME ,a.DANWEIGUID ,a.zhongbiaomoney from CG_ZhongBiaoDetail a where a.BIAODUANGUID =? ";
        zbInfoList = ZtbCommonDao.getInstance().findList(zbdwSql, Record.class, biaoduanguid);
        if (EpointCollectionUtils.isEmpty(zbInfoList)) {
            zbInfoList = new ArrayList<>();
        }
        // 描述：收集中标单位的单位guid
        HashSet<String> zbDanWeiGuidSet = collectIdentificationWinningBidder(zbInfoList);

        if (EpointCollectionUtils.isNotEmpty(allUnitInformationList)) {
            for (Record record : allUnitInformationList) {
                // 获取单位编码(单位私库)
                JSONObject memberData = new JSONObject();
                JSONObject supplierCodes = new JSONObject();
                memberData.put("orgCode", frameOuService.getFrameOu(jibeninfo.getStr("sbr_unitguid")).getOucode());
                supplierCodes.put("supplierCodes", record.getStr("standardgyscode"));
                memberData.put("requestBody", supplierCodes);
                CnbmSuppilerPlatformWebUtil spWebUtil = new CnbmSuppilerPlatformWebUtil();
                // 调用中建材供应商中台接口获取相关数据
                String responseDataJson = spWebUtil.getSupplierMemberCodes(memberData.toJSONString());
                JSONObject responseDataObject = JSONObject.parseObject(responseDataJson);
                JSONArray params = responseDataObject != null ? responseDataObject.getJSONArray("data") : null;
                String memberCode = "";
                if(EpointCollectionUtils.isNotEmpty(params)) {
                    for (int i = 0; i < params.size(); i++) {
                        JSONObject jsonObject = params.getJSONObject(i);
                        memberCode = jsonObject != null ? jsonObject.getString("memberCode") : "";
                    }
                }
                logger.info("erp02接口非招供应商中台返回数据memberCode:"+memberCode);

                // 描述：进行数据准备
                String danweiguid = record.getStr("danweiguid");
                String unitorgnum = record.getStr("standardgyscode");
                String danweiname = record.getStr("danweiname");
                String danweicode = record.getStr("unitorgnum");
                String membercode = memberCode;// 单位编码(单位私库)

                // 描述：判断单位是否中标
                // String isIn = determineWhetherUnitHasWonBid(zbDanWeiGuidSet, danweiguid);
                String isIn = "0";
                // 描述：进行变量准备
                String huizongmark = "0";
                String toubiaoprice = "0";
                String zhongbiaoprice = "0";
                double zhongbiaonum = 0.0;

                // 单源、多源
                if (zbDanWeiGuidSet.contains(danweiguid)) {
                    // 增加判断，如果无行项目中标视为未中标
                    if (ZtbCommonDao.getInstance().queryInt(
                            "select count(1) from CG_KaiPingBiaoResult_Item where biaoduanguid = ?  and danweiguid = ? and iszhongbiao  = '1'",
                            biaoduanguid, danweiguid) > 0) {
                        isIn = "1";
                    }

                    for (Record zbinfo : zbInfoList) {
                        if (danweiguid.equals(zbinfo.getStr("danweiguid"))) {
                            zhongbiaoprice = zbinfo.getStr("zhongbiaomoney");
                            toubiaoprice = zbinfo.getStr("zhongbiaomoney");
                        }
                    }
                }

                // 描述：数据封装
                DecimalFormat df = new DecimalFormat("#####0.0000");
                String format = df.format(zhongbiaonum);

                // 描述：封装行项目信息
                List<JSONObject> lineiteminfoList = dyobtainEncapsulationLineItemInformation(jibeninfo, record, isIn,
                        zbInfoList);

                // 增加报价项
                JSONObject tempbjdata = gettouBiaoBaoJia(biaoduanguid,danweiguid,"", bjlunci, dataBean_BJFangAnlst);

                JSONObject bidder = basicInformationAssemblyUnit2(danweiguid, unitorgnum, danweiname, danweicode,
                        membercode, isIn, huizongmark, toubiaoprice, zhongbiaoprice, format,EpointCollectionUtils.isEmpty(lineiteminfoList) ? tempbjdata : new JSONObject());


                if (EpointCollectionUtils.isNotEmpty(lineiteminfoList)) {
                    bidder.put("lineiteminfo", lineiteminfoList);
                }
                else {
                    bidder.put("lineiteminfo", new ArrayList<>());
                }
                // 单源和多源默认增加节点
                bidder.put("istbbj", "1"); // 是否报价
                bidder.put("isuploadfile", "1"); // 是否上传投标文件
                bidderDataList.add(bidder);
            }
        }
        return bidderDataList;
    }

    private HashSet<String> obtainingBiddingMethods() {
        HashSet<String> pbZhangBiaoFangShi = new HashSet<>();
        pbZhangBiaoFangShi.add("J");
        pbZhangBiaoFangShi.add("X");
        pbZhangBiaoFangShi.add("BX");
        pbZhangBiaoFangShi.add("G");
        pbZhangBiaoFangShi.add("Q");
        return pbZhangBiaoFangShi;
    }

    /**
     * 封装行项目信息
     *
     * @return ：
     */
    private List<JSONObject> gzobtainEncapsulationLineItemInformation(Record jibeninfo, Record record, String isIn) {
        // 描述：进行数据准备
        List<JSONObject> lineiteminfoList = new ArrayList<>();
        String biaoduanguid = record.getStr("biaoduanguid");

        // 描述：查询行项目信息
        List<Record> ItemList = queryLineItemInformation2(biaoduanguid);

        FrameOu ouinfo = iFrameOuService.getFrameOu(jibeninfo.getStr("jianshedanweiguid"));

        // 描述：进行行项目的封装
        if (EpointCollectionUtils.isNotEmpty(ItemList)) {
            for (Record itemRow : ItemList) {

                // 中标数量
                String awardquantity = "";
                // 中标单价
                String itemunitprice = "";
                // 采购需求编号
                String requirementnum = "";
                if ("1".equals(isIn)) {
                    awardquantity = itemRow.getStr("needquantity");
                    itemunitprice = itemRow.getStr("singprice");
                }

                String loginId = "";
                // operateid取立项操作人的guid
                FrameUser zbfzruser = frameUserService.getUserByUserField("userguid", jibeninfo.getStr("SBR_Code"));
                // 描述：查询采购需求的数据
                Record purchaseReq = obtainProcurementDemandData(itemRow.getStr("reqguid"));
                if (zbfzruser != null) {
                    loginId = zbfzruser.getLoginId();
                }
                else {
                    String prcompany = CommonHandleUtils.getStringValueDefault(purchaseReq,"prcompany","");
                    String prfzr = CommonHandleUtils.getStringValueDefault(purchaseReq,"prfzr","");
                    // 描述：进行用户信息查询
                    List<FrameUser> frameUserList = frameUserService.listFrameUser(prcompany, null, null, null);
                    for (FrameUser frameUser : frameUserList) {
                        if (prfzr.equals(frameUser.getDisplayName())) {
                            loginId = frameUser.getLoginId();
                            break;
                        }
                    }
                }
                requirementnum = CommonHandleUtils.getStringValueDefault(purchaseReq,"PRNO","");
                String productcodefix = cnbmIProductDataService.getOuProductPrefix(itemRow.getStr("sbr_unitguid"));
                Record cgESProduct = null;
                if (StringUtil.isNotBlank(itemRow.getStr("esproductguid"))) {
                    cgESProduct = cnbmIProductDataService.findByRowguid(itemRow.getStr("esproductguid"));
                }
                else {
                    cgESProduct = cnbmIProductDataService
                            .findByProductcode(productcodefix + itemRow.getStr("productcode"));
                }

                // 描述：封装行项目信息
                JSONObject item = new JSONObject();

                // 描述：组装操作人所在企业的企业名称和企业的社会统一信用代码
                HashMap<String, String> itemInfo = new HashMap<>();
                getParentOuInfo(itemInfo, jibeninfo.get("sbr_unitguid"));
                item.put("operateDanweiName", itemInfo.get("operateDanweiName"));
                item.put("operateDanweiCode", itemInfo.get("operateDanweiCode"));

                // 描述：封装字段信息
                item.put("purchaseunitname", jibeninfo.getStr("jianshedanwei"));
                item.put("operateid", loginId);
                item.put("requirementnum", requirementnum);// 采购需求编号
                item.put("lineitemno", itemRow.getStr("lineitemno"));

                if (cgESProduct != null) {
                    if(StringUtil.isNotBlank(productcodefix) && "X".equals(productcodefix)){
                        if("x".equals(itemRow.getStr("productcode").substring(0,1))){
                            item.put("itemno", itemRow.getStr("productcode").replace("x",""));// 叶片的物料编码不给前缀
                        }
                        else if("X".equals(itemRow.getStr("productcode").substring(0,1))){
                            item.put("itemno", itemRow.getStr("productcode").replace("X",""));// 叶片的物料编码不给前缀
                        }
                        else{
                            item.put("itemno", itemRow.getStr("productcode"));
                        }
                    }
                    else{
                        item.put("itemno",CommonHandleUtils.getStringValueDefault(cgESProduct,"productcode",""));// 物料编码-私库
                    }
                    item.put("erpitemno",CommonHandleUtils.getStringValueDefault(cgESProduct,"source_id",""));// 物料编码-ERP
                    item.put("zhtitemno",CommonHandleUtils.getStringValueDefault(cgESProduct,"rowguid",""));// 物料编码-中台
                    if(ouinfo != null) {
                        logger.info("ouinfo.getOuname():" + ouinfo.getOuname());
                        logger.info("ouinfo.getOucodeLevel():" + ouinfo.getOucodeLevel());
                        if (ouinfo.getOuname().contains("天山材料股份有限公司") || ouinfo.getOucodeLevel().contains("天山材料股份有限公司")) {
                            item.put("itemno", StringUtil.isNotBlank(cgESProduct.getStr("material_code")) ? cgESProduct.getStr("material_code") : itemRow.getStr("productcode"));// 物料编码-私库
                        }
                    }
                }
                else {
                    if(StringUtil.isNotBlank(productcodefix) && "X".equals(productcodefix)){
                        if("x".equals(itemRow.getStr("productcode").substring(0,1))){
                            item.put("itemno", itemRow.getStr("productcode").replace("x",""));// 叶片的物料编码不给前缀
                        }
                        else if("X".equals(itemRow.getStr("productcode").substring(0,1))){
                            item.put("itemno", itemRow.getStr("productcode").replace("X",""));// 叶片的物料编码不给前缀
                        }
                        else{
                            item.put("itemno", itemRow.getStr("productcode"));
                        }
                    }
                    else{
                        item.put("itemno", itemRow.getStr("productcode"));// 物料编码-私库
                    }
                    item.put("erpitemno", "");// 物料编码-ERP
                    item.put("zhtitemno", "");// 物料编码-中台
                }
                item.put("productcommodity", itemRow.getStr("itemname"));
                item.put("productcategory", itemRow.getStr("productcategory"));
                // item.put("productcategoryname", itemRow.getStr("productcategoryname"));
                item.put("productclass", itemRow.getStr("pinpaitype"));
                item.put("unit", itemRow.getStr("shangpindw"));
                item.put("sfzb", isIn);
                item.put("zbsl", awardquantity);
                item.put("zbdj", itemunitprice);
                item.put("specifications", itemRow.getStr("specifications"));
                item.put("model", itemRow.getStr("xinghaoname"));
                item.put("quality", itemRow.getStr("quality"));
                item.put("brand", itemRow.getStr("pinpainame"));
                item.put("productdescription", itemRow.getStr("xiangxipz"));
                item.put("unitprice", itemRow.getStr("singprice"));
                item.put("num", itemRow.getStr("needquantity"));
                //解决科学计数法
                DecimalFormat decimalFormat = new DecimalFormat("###0.0000");
                item.put("totalprice", StringUtil.isNotBlank(itemRow.getStr("yusuanprice")) ? decimalFormat.format(new BigDecimal(itemRow.getStr("yusuanprice"))) :"0");
                item.put("currency", itemRow.getStr("currency"));
                item.put("taxrate", StringUtil.isNotBlank(itemRow.getStr("taxrate")) ? itemRow.getStr("taxrate") : "0");
                item.put("controlprice", itemRow.getStr("controlprice"));
                // 公开邀请对于行项目的报价传空
                item.put("quotationinfos", new ArrayList<JSONObject>());

                // 描述：在单位里边增加行项目信息
                lineiteminfoList.add(item);
            }
        }
        return lineiteminfoList;
    }

    /**
     * 封装行项目信息
     *
     * @return ：
     */
    private List<JSONObject> fzobtainEncapsulationLineItemInformation(Record jibeninfo, Record record, String isIn,
                                                                      List<Record> zbInfoList) {
        // 描述：进行数据准备
        List<JSONObject> lineiteminfoList = new ArrayList<>();
        String biaoduanguid = record.getStr("biaoduanguid");
        String zhaobiaofangshi = jibeninfo.getStr("zhaobiaofangshi");
        // 描述：查询行项目信息
        List<Record> ItemList = queryLineItemInformation2(biaoduanguid);
        // 获取报价方案
        List<Record> dataBean_BJFangAnlst = getBaoJiaoFangAnItem(jibeninfo.getStr("bjfanganguid"));

        // 描述：进行行项目的封装
        if (EpointCollectionUtils.isNotEmpty(ItemList)) {
            for (Record itemRow : ItemList) {
                // 中标数量
                String awardquantity = "";
                // 中标单价
                String itemunitprice = "";
                // 是否中标
                String iszb = "0";
                // 合作 , 竞价 不用赋值
                if (!"HG".equals(zhaobiaofangshi) && !"B".equals(zhaobiaofangshi)) {
                    // 通过行guid和单位guid找到对应的中标报价信息
                    List<Record> linzb = zbInfoList.stream().filter(d -> {
                        if (itemRow.getStr("itemguid").equals(d.getStr("itemguid"))
                                && record.getStr("danweiguid").equals(d.getStr("danweiguid"))) {
                            return true;
                        }
                        return false;
                    }).collect(Collectors.toList());
                    if (EpointCollectionUtils.isNotEmpty(linzb)) {
                        awardquantity = linzb.get(0).getStr("awardquantity");
                        itemunitprice = linzb.get(0).getStr("itemunitprice");
                        iszb = "1";
                    }
                }
                else if ("HG".equalsIgnoreCase(zhaobiaofangshi)) {
                    // 获取中标数量
                    String sqlzbitem = "select * from CG_KaiPingBiaoResult_Item where biaoduanguid = ? and itemguid= ? and danweiguid = ? and danweiguid  in (select danweiguid from cg_zhongbiaodetail where biaoduanguid =?) ";
                    Record recordzbitem = ZtbCommonDao.getInstance().find(sqlzbitem, Record.class,
                            jibeninfo.getStr("biaoduanguid"), itemRow.getStr("itemguid"), record.getStr("danweiguid"),
                            jibeninfo.getStr("biaoduanguid"));
                    if (recordzbitem != null) {
                        iszb = "1";
                        awardquantity = recordzbitem.getStr("zhongbiaocount");
                        itemunitprice = recordzbitem.getStr("zhongbiaoprice");
                    }
                }
                else {
                    // 合作 , 竞价 是否中标取传来的isIn
                    iszb = isIn;
                }

                // 描述：进行用户信息查询
                String loginId = "";
                // operateid取立项操作人的guid
                FrameUser zbfzruser = frameUserService.getUserByUserField("userguid", jibeninfo.getStr("SBR_Code"));
                if (zbfzruser != null) {
                    loginId = zbfzruser.getLoginId();
                }
                else {
                    // 描述：查询采购需求的数据
                    Record purchaseReq = obtainProcurementDemandData(itemRow.getStr("reqguid"));
                    String prcompany = purchaseReq != null && StringUtil.isNotBlank(purchaseReq.getStr("prcompany"))
                            ? purchaseReq.getStr("prcompany")
                            : "";
                    String prfzr = purchaseReq != null && StringUtil.isNotBlank(purchaseReq.getStr("prfzr"))
                            ? purchaseReq.getStr("prfzr")
                            : "";
                    List<FrameUser> frameUserList = frameUserService.listFrameUser(prcompany, null, null, null);
                    for (FrameUser frameUser : frameUserList) {
                        if (prfzr.equals(frameUser.getDisplayName())) {
                            loginId = frameUser.getLoginId();
                            break;
                        }
                    }
                }
                // 描述：封装行项目信息
                JSONObject item = new JSONObject();
                // 描述：组装操作人所在企业的企业名称和企业的社会统一信用代码
                HashMap<String, String> itemInfo = new HashMap<>();
                getParentOuInfo(itemInfo, jibeninfo.get("sbr_unitguid"));
                item.put("operateDanweiName", itemInfo.get("operateDanweiName"));
                item.put("operateDanweiCode", itemInfo.get("operateDanweiCode"));


                item.put("purchaseunitname", jibeninfo.getStr("jianshedanwei"));
                item.put("operateid", loginId);
                item.put("lineitemno", itemRow.getStr("lineitemno"));
                String strtemno = itemRow.getStr("itemno");
                if (strtemno.startsWith("A") || strtemno.startsWith("a")) {
                    strtemno = strtemno.substring(1, strtemno.length());
                }
                item.put("itemno", strtemno);
                item.put("productcommodity", itemRow.getStr("itemname"));
                item.put("productcategory", itemRow.getStr("productcategory"));
                // item.put("productcategoryname", itemRow.getStr("productcategoryname"));
                item.put("productclass", itemRow.getStr("pinpaitype"));
                item.put("unit", itemRow.getStr("shangpindw"));
                item.put("sfzb", iszb);
                item.put("zbsl", awardquantity);
                item.put("zbdj", itemunitprice);
                item.put("specifications", itemRow.getStr("specifications"));
                item.put("model", itemRow.getStr("xinghaoname"));
                item.put("quality", itemRow.getStr("quality"));
                item.put("brand", itemRow.getStr("pinpainame"));
                item.put("productdescription", itemRow.getStr("xiangxipz"));
                item.put("unitprice", itemRow.getStr("singprice"));
                item.put("num", itemRow.getStr("needquantity"));
                //解决科学计数法
                DecimalFormat decimalFormat = new DecimalFormat("###0.0000");
                item.put("totalprice", StringUtil.isNotBlank(itemRow.getStr("yusuanprice")) ? decimalFormat.format(new BigDecimal(itemRow.getStr("yusuanprice"))) :"0");
                item.put("currency", itemRow.getStr("currency"));

                item.put("controlprice", itemRow.getStr("controlprice"));

                // 增加报价项 lyb 2024-06-24
                JSONObject tempbjdata = gettouBiaoBaoJia(biaoduanguid, record.getStr("danweiguid"),
                        itemRow.getStr("itemguid"), jibeninfo.getStr("bjlunci"), dataBean_BJFangAnlst);
                if (StringUtil.isNotBlank(tempbjdata.getString("hshuilv"))) {
                    item.put("taxrate", tempbjdata.getString("hshuilv"));
                }
                else if (StringUtil.isNotBlank(tempbjdata.getString("hshuilvbaifenshu"))) {
                    item.put("taxrate", tempbjdata.getString("hshuilvbaifenshu"));
                }
                else {
                    item.put("taxrate",
                            StringUtil.isNotBlank(itemRow.getStr("taxrate")) ? itemRow.getStr("taxrate") : "0");
                }
                item.put("bjdata", tempbjdata);

                List<Record> bjlist = ZtbCommonDao.getInstance().findList(
                        "select bjlunci,itemtotalprice from CG_TouBiaoBaoJia where BiaoDuanGuid =? and DanWeiGuid =? and ItemGuid =?  order by bjlunci asc",
                        Record.class, record.getStr("biaoduanguid"), record.getStr("danweiguid"),
                        itemRow.getStr("itemguid"));

                if (EpointCollectionUtils.isNotEmpty(bjlist)) {
                    List<JSONObject> quotationinfoList = new ArrayList<>();
                    for (Record bJRecord : bjlist) {
                        JSONObject quotationinfo = new JSONObject();
                        quotationinfo.put("lunci", bJRecord.getStr("bjlunci"));
                        quotationinfo.put("luncibj", bJRecord.getStr("itemtotalprice"));
                        quotationinfoList.add(quotationinfo);
                    }
                    item.put("quotationinfos", quotationinfoList);
                }
                else {
                    item.put("quotationinfos", new ArrayList<JSONObject>());
                }

                // 描述：在单位里边增加行项目信息
                lineiteminfoList.add(item);
            }
        }
        return lineiteminfoList;
    }

    /**
     * 封装行项目信息
     *
     * @return ：
     */
    private List<JSONObject> dyobtainEncapsulationLineItemInformation(Record jibeninfo, Record record, String isIn,
                                                                      List<Record> zbInfoList) {
        // 描述：进行数据准备
        List<JSONObject> lineiteminfoList = new ArrayList<>();
        String biaoduanguid = record.getStr("biaoduanguid");
        String zhaobiaofangshi = record.getStr("zhaobiaofangshi");
        // 描述：查询行项目信息
        List<Record> ItemList = queryLineItemInformation2(biaoduanguid);

        // 描述：进行行项目的封装
        if (EpointCollectionUtils.isNotEmpty(ItemList)) {
            for (Record itemRow : ItemList) {

                // 描述：进行用户信息查询
                String loginId = "";
                // operateid取立项操作人的guid
                FrameUser zbfzruser = frameUserService.getUserByUserField("userguid", jibeninfo.getStr("SBR_Code"));
                if (zbfzruser != null) {
                    loginId = zbfzruser.getLoginId();
                }
                else {
                    // 描述：查询采购需求的数据
                    Record purchaseReq = obtainProcurementDemandData(itemRow.getStr("reqguid"));
                    String prcompany = purchaseReq != null && StringUtil.isNotBlank(purchaseReq.getStr("prcompany"))
                            ? purchaseReq.getStr("prcompany")
                            : "";
                    String prfzr = purchaseReq != null && StringUtil.isNotBlank(purchaseReq.getStr("prfzr"))
                            ? purchaseReq.getStr("prfzr")
                            : "";
                    List<FrameUser> frameUserList = frameUserService.listFrameUser(prcompany, null, null, null);
                    for (FrameUser frameUser : frameUserList) {
                        if (prfzr.equals(frameUser.getDisplayName())) {
                            loginId = frameUser.getLoginId();
                            break;
                        }
                    }
                }

                // 获取中标数量
                String sqlzbitem = "select * from CG_KaiPingBiaoResult_Item where biaoduanguid = ? and itemguid= ? and danweiguid = ? ";

                String isin = "0";
                String zbsl = "";
                String zbdj = "";
                Record recordzbitem = ZtbCommonDao.getInstance().find(sqlzbitem, Record.class,
                        jibeninfo.getStr("biaoduanguid"), itemRow.getStr("itemguid"), record.getStr("danweiguid"));

                if (recordzbitem != null) {
                    isin = recordzbitem.getStr("iszhongbiao");
                    zbsl = recordzbitem.getStr("zhongbiaocount");
                    zbdj = recordzbitem.getStr("zhongbiaoprice");
                }

                // 描述：封装行项目信息
                JSONObject item = new JSONObject();
                // 描述：组装操作人所在企业的企业名称和企业的社会统一信用代码
                HashMap<String, String> itemInfo = new HashMap<>();
                getParentOuInfo(itemInfo, jibeninfo.get("sbr_unitguid"));
                item.put("operateDanweiName", itemInfo.get("operateDanweiName"));
                item.put("operateDanweiCode", itemInfo.get("operateDanweiCode"));

                item.put("purchaseunitname", jibeninfo.getStr("jianshedanwei"));
                item.put("operateid", loginId);
                item.put("lineitemno", itemRow.getStr("lineitemno"));
                String strtemno = itemRow.getStr("itemno");
                if (strtemno.startsWith("A") || strtemno.startsWith("a")) {
                    strtemno = strtemno.substring(1, strtemno.length());
                }
                item.put("itemno", strtemno);
                item.put("productcommodity", itemRow.getStr("itemname"));
                item.put("productcategory", itemRow.getStr("productcategory"));
                // item.put("productcategoryname", itemRow.getStr("productcategoryname"));
                item.put("productclass", itemRow.getStr("pinpaitype"));
                item.put("unit", itemRow.getStr("shangpindw"));
                item.put("sfzb", isin);
                item.put("zbsl", zbsl);
                item.put("zbdj", zbdj);
                item.put("specifications", itemRow.getStr("specifications"));
                item.put("model", itemRow.getStr("xinghaoname"));
                item.put("quality", itemRow.getStr("quality"));
                item.put("brand", itemRow.getStr("pinpainame"));
                item.put("productdescription", itemRow.getStr("xiangxipz"));
                item.put("unitprice", itemRow.getStr("singprice"));
                item.put("num", itemRow.getStr("needquantity"));
                //解决科学计数法
                DecimalFormat decimalFormat = new DecimalFormat("###0.0000");
                item.put("totalprice", StringUtil.isNotBlank(itemRow.getStr("yusuanprice")) ? decimalFormat.format(new BigDecimal(itemRow.getStr("yusuanprice"))):"0");
                item.put("currency", itemRow.getStr("currency"));
                item.put("taxrate", StringUtil.isNotBlank(itemRow.getStr("taxrate")) ? itemRow.getStr("taxrate") : "0");
                item.put("controlprice", itemRow.getStr("controlprice"));
                item.put("quotationinfos", new ArrayList<JSONObject>());

                // 描述：在单位里边增加行项目信息
                lineiteminfoList.add(item);
            }
        }
        return lineiteminfoList;
    }

    /**
     * 获取采购需求数据
     *
     * @param reqguid
     * @return
     */
    private Record obtainProcurementDemandData(String reqguid) {
        String cgxqSql = "select * from PR_PurchaseReq where RowGuid = ?";
        return ZtbCommonDao.getInstance().find(cgxqSql, Record.class, reqguid);
    }

    /**
     * 封装项目的基本信息
     *
     * @param zhongBiaoJieGuoInfo
     *            ：
     * @return ：
     */
    private JSONObject obtainEncapsulationProjectInformation(Record zhongBiaoJieGuoInfo) {
        // 描述：进行数据准备
        JSONObject projectData = new JSONObject();
        String biaoduanguid = zhongBiaoJieGuoInfo.getStr("biaoduanguid");

        // 描述：查询项目立项和标段的数据
        Record fabaoInfo = queryProjectInitiation(biaoduanguid);
        Record bdInfo = queryBidInformation(biaoduanguid);

        // 描述：项目信息组装
        projectData.put("projectname", zhongBiaoJieGuoInfo.getStr("projectname"));
        projectData.put("projectno", zhongBiaoJieGuoInfo.getStr("projectno"));
        projectData.put("jsdwlxr", fabaoInfo.getStr("jsdwlxr"));
        projectData.put("jsdwlxrtel", fabaoInfo.getStr("jsdwlxrtel"));
        projectData.put("extinfo", biaoduanguid);
        projectData.put("biaoduanname", zhongBiaoJieGuoInfo.getStr("biaoduanname"));
        projectData.put("biaoduanno", zhongBiaoJieGuoInfo.getStr("biaoduanno"));
        projectData.put("bidsectionguid", biaoduanguid);
        projectData.put("fabaocontent", bdInfo.getStr("fabaocontent"));
        projectData.put("zhaobiaofangshi", zhongBiaoJieGuoInfo.getStr("zhaobiaofangshi"));
        projectData.put("isusewebztb", bdInfo.getStr("isusewebztb"));
        //解决科学计数法
        DecimalFormat decimalFormat = new DecimalFormat("###0.0000");
        projectData.put("touzigusuan",StringUtil.isNotBlank(bdInfo.getStr("touzigusuan")) ? decimalFormat.format(new BigDecimal(bdInfo.getStr("touzigusuan"))):"0");
        return projectData;
    }



    /**
     * 封装项目的基本信息
     *
     * @param jibeninfo
     *            ：
     * @return ：
     */
    private JSONObject obtainEncapsulationProjectInformation2(Record jibeninfo,JSONObject requestJsonObject) {
        // 描述：进行数据准备
        JSONObject projectData = new JSONObject();

        // 描述：组装操作人所在企业的企业名称和企业的社会统一信用代码
        HashMap<String, String> operateDanweiInfo = new HashMap<>();
        getParentOuInfo(operateDanweiInfo, jibeninfo.getStr("sbr_unitguid"));
        projectData.put("operateDanweiName", operateDanweiInfo.get("operateDanweiName"));
        projectData.put("operateDanweiCode", operateDanweiInfo.get("operateDanweiCode"));

        // 描述：查询操作人信息
        HashMap<String, String> frameUserInfo = new HashMap<>();
        getFrameUserInfo(frameUserInfo, jibeninfo.getStr("sbr_code"));
        projectData.put("operateUserTelephone", frameUserInfo.get("operateUserTelephone"));
        projectData.put("operateOuName", frameUserInfo.get("operateOuName"));
        projectData.put("operateUserName", frameUserInfo.get("operateUserName"));

        // 描述：组装组织人员映射关系信息
        HashMap<String, String> memberinfo = new HashMap<>();
        getSysMemberInfo(memberinfo, jibeninfo.getStr("memberUserCode"),jibeninfo.getStr("memberOrgCode"),requestJsonObject.getString("sysCode"));
        projectData.put("memberUserCode", memberinfo.get("memberUserCode"));
        projectData.put("memberOrgCode", memberinfo.get("memberOrgCode"));
        projectData.put("sysCode", memberinfo.get("sysCode"));

        String biaoduanguid = jibeninfo.getStr("biaoduanguid");
        // 描述：项目信息组装
        projectData.put("projectname", jibeninfo.getStr("projectname"));
        projectData.put("projectno", jibeninfo.getStr("projectno"));
        projectData.put("jsdwlxr", jibeninfo.getStr("jsdwlxr"));
        projectData.put("jsdwlxrtel", jibeninfo.getStr("jsdwlxrtel"));
        projectData.put("ppexecute", jibeninfo.getStr("ppexecute"));// 采购执行模式
        projectData.put("extinfo", jibeninfo.getStr("extinfo"));
        projectData.put("biaoduanname", jibeninfo.getStr("biaoduanname"));
        projectData.put("biaoduanno", jibeninfo.getStr("biaoduanno"));
        projectData.put("bidsectionguid", biaoduanguid);
        projectData.put("fabaocontent", jibeninfo.getStr("fabaocontent"));
        projectData.put("zhaobiaofangshi", jibeninfo.getStr("zhaobiaofangshi"));
        projectData.put("isusewebztb", jibeninfo.getStr("isusewebztb"));
        //解决科学计数法
        DecimalFormat decimalFormat = new DecimalFormat("###0.0000");
        projectData.put("touzigusuan",StringUtil.isNotBlank(jibeninfo.getStr("touzigusuan")) ? decimalFormat.format(new BigDecimal(jibeninfo.getStr("touzigusuan"))):"0");
        return projectData;
    }

    protected void getSysMemberInfo(HashMap<String, String> memberinfo, String memberUserCode, String memberOrgCode,String sysCode) {
        if (StringUtil.isNotBlank(memberUserCode)) {
            Record sysUserOrgInfo = queryMappingOrganizationalPersonnelData(memberUserCode,memberOrgCode,sysCode);
            if (sysUserOrgInfo != null) {
                memberinfo.put("memberUserCode", sysUserOrgInfo.get("map_user_account"));
                memberinfo.put("memberOrgCode", sysUserOrgInfo.get("mapping_organization_code"));
                memberinfo.put("sysCode", sysUserOrgInfo.getStr("map_app_code"));
            }
            else {
                memberinfo.put("memberUserCode", "");
                memberinfo.put("memberOrgCode", "");
                memberinfo.put("sysCode", "");
            }
        }
        else {
            memberinfo.put("memberUserCode", "");
            memberinfo.put("memberOrgCode", "");
            memberinfo.put("sysCode", "");
        }
    }

    protected void getFrameUserInfo(HashMap<String, String> frameUserInfo, String sbrCode) {
        if (StringUtil.isNotBlank(sbrCode)) {
            FrameUser frameUser = cnbmFrameUserService.getFrameUserByUserId(sbrCode);
            frameUserInfo.put("operateUserTelephone", CommonHandleUtils.getStringValueDefault(frameUser,"mobile",""));
            frameUserInfo.put("operateUserName", CommonHandleUtils.getStringValueDefault(frameUser,"displayname",""));
            FrameOu frameOu = cnbmFrameOuService.getFrameOuByOuGuid(frameUser.getOuGuid());
            if (frameOu != null) {
                frameUserInfo.put("operateOuName", CommonHandleUtils.getStringValueDefault(frameOu,"ouname",""));
            }
        }
        else {
            frameUserInfo.put("operateUserTelephone", "");
            frameUserInfo.put("operateUserName", "");
            frameUserInfo.put("operateOuName", "");
        }
    }

    protected void getParentOuInfo(HashMap<String, String> operateDanweiInfo, String sbrCode) {
        List<FrameOu> ParentOu = iFrameOuService.listParentOu(sbrCode, true);
//        logger.info("ParentOu:" + ParentOu);
        if (ParentOu != null && ParentOu.size() > 0) {
            for (int i = 0; i < ParentOu.size(); i++) {
                if ("1".equals(ParentOu.get(i).get("danweiquality"))) {
                    operateDanweiInfo.put("operateDanweiName", CommonHandleUtils.getStringValueDefault(ParentOu.get(i),"ouname",""));
                    operateDanweiInfo.put("operateDanweiCode", CommonHandleUtils.getStringValueDefault(ParentOu.get(i),"creditcode",""));
                    break;
                }
            }
        }
        else {
            operateDanweiInfo.put("operateDanweiName", "");
            operateDanweiInfo.put("operateDanweiCode", "");
        }
    }

    protected Record queryMappingOrganizationalPersonnelData(String memberUserCode, String memberOrgCode,String sysCode) {

        FrameUser frameUser =  cnbmFrameUserService.getFrameUserByLoginIdAndOugid(memberUserCode,memberOrgCode);
        if (frameUser == null) {
            frameUser = cnbmFrameUserService.getFrameUserByLoginIdAndOtherouguid(memberUserCode, memberOrgCode);
        }
        String loginid = CommonHandleUtils.getStringValueDefault(frameUser, "loginid", "");
        String ouguid = CommonHandleUtils.getStringValueDefault(frameUser, "ouguid", "");
        Record record = StringUtil.isNotBlank(sysCode) ? sysUserOrgMap.queryMappingOrgByUserCodeAndOrgCodeAndMapOrgCode(loginid,ouguid,sysCode) : sysUserOrgMap.queryMappingOrganizationalPersonnelByUserCodeAndOrgCode(loginid,ouguid);
        if (record == null || record.isEmpty())
        {
            FrameOu frameOu = frameOuService.getFrameOu(ouguid);
            String oucode = CommonHandleUtils.getStringValueDefault(frameOu, "oucode", "");
            record = StringUtil.isNotBlank(sysCode) ? sysUserOrgMap.queryMappingOrgByUserCodeAndOrgCodeAndMapOrgCode(loginid,oucode,sysCode) : sysUserOrgMap.queryMappingOrganizationalPersonnelByUserCodeAndOrgCode(loginid,oucode);
        }

        return record;
    }

    protected Record findChuBuFaBaoInfo(String biaoduanguid) {
        SqlBuilder sqlBuilder = new SqlBuilder();
        sqlBuilder.append("select a.* from cg_chubufabao a inner join cg_biaoduaninfo b on a.FaBaoGuid = b.fabaoguid ");
        sqlBuilder.append("where b.biaoduanguid = ?  ",biaoduanguid);
        return ZtbCommonDao.getInstance().find(sqlBuilder.getSql(),Record.class,sqlBuilder.getParams());
    }

    /**
     * 获取中标结果信息校验处理
     *
     * @param requestJsonObject
     *            ：
     * @return ：
     */
    public String verificationHandle(JSONObject requestJsonObject) {
        String projectno = requestJsonObject.getString("projectno");
        String bidsectionno = requestJsonObject.getString("biaoduanno");
        String fromdateStr = requestJsonObject.getString("fromdate");
        String enddateStr = requestJsonObject.getString("enddate");

        // 描述：校验项目编号
        if (StringUtil.isBlank(projectno)) {
            return "项目编号不能为空";
        }

        if (StringUtil.isNotBlank(fromdateStr) || StringUtil.isNotBlank(enddateStr)) {

            Date fromdate = EpointDateUtil.convertString2Date(fromdateStr, "yyyy-MM-dd HH:mm:ss");
            Date enddate = EpointDateUtil.convertString2Date(enddateStr, "yyyy-MM-dd HH:mm:ss");
            if (EpointDateUtil.compareDateOnDay(enddate, fromdate) < 0) {
                return "截止时间不能小于开始时间！";
            }
            Date fromTodate = EpointDateUtil.addMinute(fromdate, 60 * 8);
            if (EpointDateUtil.compareDateOnDay(enddate, fromTodate) > 0) {
                return "开始时间和截止时间不超过8小时";
            }
        }
        return "";
    }

    /**
     * 搜索条件
     *
     * @param requestJsonObject
     *            ：
     * @param sqlBuilder
     *            ：
     * @return ：
     */
    private void addFilterConditions(JSONObject requestJsonObject, SqlBuilder sqlBuilder) {

        sqlBuilder.append(" where 1 = 1  and auditstatus = '3'  ");

        // 描述：非必填项查询
        String biaoduanno = requestJsonObject.getString("biaoduanno");
        if (StringUtil.isNotBlank(biaoduanno)) {
            sqlBuilder.append(" and biaoduanno = ? ", biaoduanno);
        }

        // 项目编号可以是多个，用逗号分开
        String projectno = requestJsonObject.getString("projectno");
        List<String> projectList = Arrays.stream(projectno.split(",")).collect(Collectors.toList());
        if (EpointCollectionUtils.isNotEmpty(projectList)) {
            sqlBuilder.appendIn(" and projectno in ( ? )", projectList);
        }

        String fromdate = requestJsonObject.getString("fromdate");
        String enddate = requestJsonObject.getString("enddate");
        if (StringUtil.isNotBlank(fromdate) && StringUtil.isBlank(enddate)) {
            String endDate1 = getEndDate(fromdate);
            sqlBuilder.append(" and and shr_date between ? and ?", fromdate, endDate1);
        }
        else if (StringUtil.isBlank(fromdate) && StringUtil.isNotBlank(enddate)) {
            String fromDate1 = getFromDate(enddate);
            sqlBuilder.append(" and and shr_date between ? and ?", fromDate1, enddate);
        }
        else if (StringUtil.isNotBlank(fromdate) && StringUtil.isNotBlank(enddate)) {
            sqlBuilder.append(" and shr_date between ? and ?", fromdate, enddate);
        }
    }

    /**
     * 搜索条件
     *
     * @param requestJsonObject
     *            ：
     * @param sqlBuilder
     *            ：
     * @return ：
     */
    private void addFilterConditions2(JSONObject requestJsonObject, SqlBuilder sqlBuilder) {

        // 标段编号,项目编号可以是多个，用逗号分开
        String biaoduanno = requestJsonObject.getString("biaoduanno");
        String projectno = requestJsonObject.getString("projectno");

        if (StringUtil.isNotBlank(biaoduanno)) {
            List<String> bdnoList = Arrays.stream(biaoduanno.split(",")).collect(Collectors.toList());
            if (EpointCollectionUtils.isNotEmpty(bdnoList)) {
                sqlBuilder.appendIn(" and b.biaoduanno in ( ? )", bdnoList);
            }
        }
        else if (StringUtil.isNotBlank(projectno)) {
            List<String> projectList = Arrays.stream(projectno.split(",")).collect(Collectors.toList());
            if (EpointCollectionUtils.isNotEmpty(projectList)) {
                sqlBuilder.appendIn(" and b.projectno in ( ? )", projectList);
            }
        }
        else {
            String fromdate = requestJsonObject.getString("fromdate");
            String enddate = requestJsonObject.getString("enddate");
            if (StringUtil.isNotBlank(fromdate) && StringUtil.isBlank(enddate)) {
                String endDate1 = getEndDate(fromdate);
                sqlBuilder.append(" and a.shr_date between ? and ?", fromdate, endDate1);
            }
            else if (StringUtil.isBlank(fromdate) && StringUtil.isNotBlank(enddate)) {
                String fromDate1 = getFromDate(enddate);
                sqlBuilder.append(" and a.shr_date between ? and ?", fromDate1, enddate);
            }
            else if (StringUtil.isNotBlank(fromdate) && StringUtil.isNotBlank(enddate)) {
                sqlBuilder.append(" and a.shr_date between ? and ?", fromdate, enddate);
            }
        }
    }

    /**
     * 搜索条件
     *
     * @param requestJsonObject
     *            ：
     * @param sqlBuilder
     *            ：
     * @return ：
     */
    private void addFilterConditions3(JSONObject requestJsonObject, SqlBuilder sqlBuilder) {

        // 标段编号,项目编号可以是多个，用逗号分开
        String biaoduanno = requestJsonObject.getString("biaoduanno");
        String projectno = requestJsonObject.getString("projectno");

        if (StringUtil.isNotBlank(biaoduanno)) {
            List<String> bdnoList = Arrays.stream(biaoduanno.split(",")).collect(Collectors.toList());
            if (EpointCollectionUtils.isNotEmpty(bdnoList)) {
                sqlBuilder.appendIn(" and b.biaoduanno in ( ? )", bdnoList);
            }
        }
        else if (StringUtil.isNotBlank(projectno)) {
            List<String> projectList = Arrays.stream(projectno.split(",")).collect(Collectors.toList());
            if (EpointCollectionUtils.isNotEmpty(projectList)) {
                sqlBuilder.appendIn(" and b.projectno in ( ? )", projectList);
            }
        }
        else {
            String fromdate = requestJsonObject.getString("fromdate");
            String enddate = requestJsonObject.getString("enddate");
            if (StringUtil.isNotBlank(fromdate) && StringUtil.isBlank(enddate)) {
                String endDate1 = getEndDate(fromdate);
                sqlBuilder.append(" and b.OperateDate between ? and ?", fromdate, endDate1);
            }
            else if (StringUtil.isBlank(fromdate) && StringUtil.isNotBlank(enddate)) {
                String fromDate1 = getFromDate(enddate);
                sqlBuilder.append(" and b.OperateDate between ? and ?", fromDate1, enddate);
            }
            else if (StringUtil.isNotBlank(fromdate) && StringUtil.isNotBlank(enddate)) {
                sqlBuilder.append(" and b.OperateDate between ? and ?", fromdate, enddate);
            }
        }
    }

    /**
     * 数据必填校验
     *
     * @param requestJsonObject
     *            ：
     * @return ：
     */
    protected String dataRequiredVerificationHandle(JSONObject requestJsonObject) {
        String projectno = requestJsonObject.getString("projectno");
        String bidsectionno = requestJsonObject.getString("bidsectionno");
        if (StringUtil.isBlank(projectno) && StringUtil.isBlank(bidsectionno)) {
            return "请求参数异常,项目编号和标段包编号不能全为空！";
        }
        return "";
    }

    /**
     * 获取所有的投标单位信息
     *
     * @param biaoduanguid
     *            ：
     * @return ：
     */
    protected List<Record> obtainAllBiddingUnitInformation(String biaoduanguid) {
        String jsdwSql = "select distinct (tb.DanWeiGuid),tb.*,hu.standardgyscode from cg_toubiaodanwei tb join cg_filedownloadinfo fl on tb.DanWeiGuid  = fl.danweiguid join huiyuan_unitcominfo hu on tb.DanWeiGuid  = hu.danweiguid where tb.BiaoDuanGuid = ? and fl.biaoduanguid = ? and fl.clienttype ='J070' ";
        return ZtbCommonDao.getInstance().findList(jsdwSql, Record.class, biaoduanguid, biaoduanguid);
    }

    protected List<Record> obtainAllBiddingUnitInformation_HG(String biaoduanguid) {
        String jsdwSql = "select distinct (tb.DanWeiGuid),tb.*,hu.standardgyscode from cg_toubiaodanwei tb join huiyuan_unitcominfo hu on tb.DanWeiGuid  = hu.danweiguid where tb.biaoduanguid=? and tb.isin='1' ";
        return ZtbCommonDao.getInstance().findList(jsdwSql, Record.class, biaoduanguid);
    }

    /**
     * 获取所有中标单位信息
     *
     * @param biaoduanguid
     *            ：
     * @return ：
     */
    protected List<Record> obtainInformationOnAllWinningBidders(String biaoduanguid) {
        ZtbCommonDao instance = ZtbCommonDao.getInstance();
        try {
            // 描述：查询中标单位信息
            String zbdwSql = "select * from CG_ZhongBiaoDetail where biaoduanguid = ?";
            return instance.findList(zbdwSql, Record.class, biaoduanguid);
        }
        finally {
            if (instance != null) {
                instance.close();
            }
        }

    }

    /**
     * 获取所有中标单位信息
     *
     * @param biaoduanguid
     *            ：
     * @return ：
     */
    protected List<Record> gzobtainInformationOnAllWinningBidders(String biaoduanguid) {
        ZtbCommonDao instance = ZtbCommonDao.getInstance();
        try {
            // 描述：查询中标单位信息
            String zbdwSql = "select * from CG_GGZhongBiaoDetail where biaoduanguid = ?";
            return instance.findList(zbdwSql, Record.class, biaoduanguid);
        }
        finally {
            if (instance != null) {
                instance.close();
            }
        }
    }

    /**
     * 获取所有中标单位信息
     *
     * @param biaoduanguid
     *            ：
     * @return ：
     */
    protected List<Record> fzobtainInformationOnAllWinningBidders(String biaoduanguid, String bjlunci) {
        ZtbCommonDao instance = ZtbCommonDao.getInstance();
        List<Record> list;
        try {
            // 描述：查询中标单位信息
            String zbdwSql = "select a.DANWEINAME ,a.DANWEIGUID ,a.zhongbiaomoney,b.ItemGuid ,b.awardquantity,c.itemunitprice from CG_ZhongBiaoDetail a "
                    + "left join CG_ShouBiaoAndBDItem b on a.BIAODUANGUID =b.BiaoDuanGuid and a.DANWEIGUID =b.DanWeiGuid "
                    + "left join CG_TouBiaoBaoJia c on a.BIAODUANGUID =c.BiaoDuanGuid  and a.DANWEIGUID=c.DANWEIGUID and c.itemguid=b.ItemGuid and c.bjlunci=? "
                    + "where b.awardtype in ('2','3') and ifnull(b.ItemGuid ,'')!='' and  a.BIAODUANGUID =? ";
            list = instance.findList(zbdwSql, Record.class, bjlunci, biaoduanguid);
        }
        finally {
            if (instance != null) {
                instance.close();
            }
        }
        return list;
    }

    protected List<Record> fzobtainInformationOnAllWinningBidders_teshu(String biaoduanguid) {
        ZtbCommonDao instance = ZtbCommonDao.getInstance();
        List<Record> list;
        try {
            // 描述：查询中标单位信息
            String zbdwSql = "select * from CG_ZhongBiaoDetail a where BIAODUANGUID = ? ";
            list = instance.findList(zbdwSql, Record.class, biaoduanguid);
        }
        finally {
            if (instance != null) {
                instance.close();
            }
        }
        return list;
    }

    /**
     * 收集中标单位的单位guid
     *
     * @param zbInfoList
     *            ：
     * @return ：
     */
    protected HashSet<String> collectIdentificationWinningBidder(List<Record> zbInfoList) {
        HashSet<String> zbDanWeiGuidSet = new HashSet<>();
        for (Record zbInfo : zbInfoList) {
            zbDanWeiGuidSet.add(zbInfo.getStr("danweiguid"));
        }
        return zbDanWeiGuidSet;
    }

    /**
     * 获取中标价格
     *
     * @param biaoduanguid
     *            ：
     * @param danweiguid
     *            ：
     * @return ：
     */
    protected String queryWinningBidPrice(String biaoduanguid, String danweiguid) {
        String zbmSql = "select zhongbiaomoney from cg_zhongbiaodetail where biaoduanguid = ? and danweiguid = ?";
        return ZtbCommonDao.getInstance().find(zbmSql, String.class, biaoduanguid, danweiguid);
    }

    /**
     * 组装基础单位信息
     * ：
     *
     * @param unitorgnum
     *            ：
     * @param danweiname
     *            ：
     * @param isIn
     *            ：
     * @param zhdf
     *            ：
     * @param toubiaoprice
     *            ：
     * @param zhongbiaoprice
     *            ：
     * @param zhongbiaonum
     *            ：
     * @return ：
     */
    protected JSONObject basicInformationAssemblyUnit(String unitorgnum, String danweiname, String isIn, String zhdf,
                                                      String toubiaoprice, String zhongbiaoprice, String zhongbiaonum) {
        JSONObject bidder = new JSONObject();
        bidder.put("danweiguid", unitorgnum);
        bidder.put("danweicode", unitorgnum);
        bidder.put("danweiname", danweiname);
        bidder.put("sfzbdw", isIn);
        bidder.put("zhdf", zhdf);
        // 描述：科学计数法处理
        if (StringUtil.isNotBlank(toubiaoprice)) {
            toubiaoprice = new BigDecimal(toubiaoprice).toPlainString();
        }
        if (StringUtil.isNotBlank(zhongbiaoprice)) {
            zhongbiaoprice = new BigDecimal(zhongbiaoprice).toPlainString();
        }
        bidder.put("toubiaoprice", toubiaoprice);
        bidder.put("zhongbiaoprice", zhongbiaoprice);
        bidder.put("zhongbiaonum", zhongbiaonum);
        return bidder;
    }

    protected JSONObject basicInformationAssemblyUnit2(String danweiguid, String unitorgnum, String danweiname,
                                                       String danweicode, String membercode, String isIn, String zhdf, String toubiaoprice, String zhongbiaoprice,
                                                       String zhongbiaonum,JSONObject tempbjdata) {

        JSONObject bidder = new JSONObject();
        bidder.put("danweiguid", danweiguid);
        bidder.put("danweicode", danweicode);
        bidder.put("membercode", membercode);
        bidder.put("unitorgnum", unitorgnum);
        bidder.put("danweiname", danweiname);
        bidder.put("sfzbdw", isIn);
        bidder.put("zhdf", zhdf);
        // 描述：科学计数法处理
        if (StringUtil.isNotBlank(toubiaoprice)) {
            toubiaoprice = new BigDecimal(toubiaoprice).toPlainString();
        }
        if (StringUtil.isNotBlank(zhongbiaoprice)) {
            zhongbiaoprice = new BigDecimal(zhongbiaoprice).toPlainString();
        }
        bidder.put("toubiaoprice", toubiaoprice);
        bidder.put("zhongbiaoprice", zhongbiaoprice);
        bidder.put("zhongbiaonum", zhongbiaonum);
        bidder.put("baojiadata", tempbjdata);
        return bidder;
    }

    /**
     * 判断单位是否中标
     *
     * @return ：
     */
    protected String determineWhetherUnitHasWonBid(HashSet<String> zbDanWeiGuidSet, String danweiguid) {
        // 描述：判断单位是否中标
        String isIn = "0";
        if (zbDanWeiGuidSet.contains(danweiguid)) {
            isIn = "1";
        }
        return isIn;
    }

    /**
     * 获取授标方式
     *
     * @param biaoduanguid
     *            ：
     * @return ：
     */
    protected String obtainingAwardMethod(String biaoduanguid) {
        ZtbCommonDao instance = ZtbCommonDao.getInstance();
        try {
            String sbfsSql = "select awardtype from cg_shoubiaotuijian where BIAODUANGUID = ? ";
            return instance.find(sbfsSql, String.class, biaoduanguid);
        }
        finally {
            if (instance != null) {
                instance.close();
            }
        }
    }

    /**
     * 获取评标数据
     *
     * @param biaoduanguid
     *            ：
     * @param danweiguid
     *            ：
     * @return ：
     */
    protected Record queryEvaluationData(String biaoduanguid, String danweiguid) {
        String pbSql = "select * from cg_kaipingbiao where biaoduanguid = ? and DanWeiGuid = ?";
        return ZtbCommonDao.getInstance().find(pbSql, Record.class, biaoduanguid, danweiguid);
    }

    /**
     * 根据标段标编号查询中标公告数据
     *
     * @return ：
     */
    protected Record queryBidAnnouncementDataByBdno(String biaoduanno, boolean isfz) {
        ZtbCommonDao instance = ZtbCommonDao.getInstance();
        try {
            if (isfz) {
                String sqlByBdGuid = "select * from cg_zhongbiaojieguo cz  where biaoduanguid = (select biaoduanguid from cg_biaoduaninfo cb where biaoduanno = ? and statuscode  = '3')";
                return instance.find(sqlByBdGuid, Record.class, biaoduanno);
            }
            else {
                String sqlByBdGuid = "select * from view_cg_zhongbiaogs  where biaoduanno = ?  and bdauditstatus ='3' ";
                return instance.find(sqlByBdGuid, Record.class, biaoduanno);
            }
        }
        finally {
            if (instance != null) {
                instance.close();
            }
        }
    }

    /**
     * 根据项目编号查询中标公告数据
     *
     * @param projectno
     *            ：
     * @return ：
     */
    protected Record queryBidAnnouncementDataByProno(String projectno) {
        ZtbCommonDao instance = ZtbCommonDao.getInstance();
        try {
            String sqlByBdGuid = "select * from view_cg_zhongbiaogs  where projectno = ?  and bdauditstatus ='3' ";
            return instance.find(sqlByBdGuid, Record.class, projectno);
        }
        finally {
            if (instance != null) {
                instance.close();
            }
        }
    }

    /**
     * 中标公告修改为审核通过
     *
     * @param record
     *            ：
     */
    protected void updateBidAnnouncementToApproved(Record record,JSONObject requestJsonObject, boolean isfz) {
        ZtbCommonDao instance = ZtbCommonDao.getInstance();
        try {
            if (isfz) {
                if("0".equals(requestJsonObject.getString("tradestatus"))){//审核不通过
                    // 重置工作台数据(进行中)
                    bidflowHandleService.resetMenuNodeitemByRowguid(record.getStr("rowguid"));
                    instance.execute("update cg_zhongbiaojieguo set auditstatus=?,currentstepname=?,operatedate = ? where rowguid=? ",
                            TradeEnums.AuditStatus.审核不通过.getValue(), "初始信息录入",new Date(), record.getStr("rowguid"));
                    workflowApi.resetWorkflow(record.getStr("pvi_guid"), record.getStr("sbr_code"), requestJsonObject.getString("tradereason"));
                    DB_OperationLog.addOperationLog(record.getStr("rowguid"), SysEnums.SubSysName.企业招采,
                            SysEnums.OperName.回第一步, "Erp审核不通过中标公告状态，中标公告唯一标志@" + record.getStr("rowguid") + "审核不通过时间@"
                                    + EpointDateUtil.convertDate2String(new Date(), "yyyy-MM-dd HH:mm:ss"),
                            "中标公告撤回修改");
                }
                else {
                    // 非招使用的中标结果表
                    String sqlUpdateZbjg = "update cg_zhongbiaojieguo set auditstatus = '3' where rowguid = ? ";
                    instance.execute(sqlUpdateZbjg, record.getStr("RowGuid"));

                    // 非招推送中标结果公告
                    try {
                        push_fz_zbgg_cnbm(record);
                    }
                    catch (Exception e) {
                        e.printStackTrace();
                    }

                    // 非招完成后事件逻辑
                    try {
                        if (!"B".equals(record.getStr("zhaobiaofangshi"))) {

                            String where = " 1=1 and a.biaoduanguid=?  and a.IsIn='1'";
                            String sql = "select a.*,b.rowguid as detailrowguid from cg_pingbiao_toubiaoinfo a inner join CG_ZhongBiaoDetail b on a.danweiguid=b.danweiguid  and a.biaoduanguid=b.biaoduanguid  where "
                                    + where;
                            List<Record> dataList = instance.findList(sql, Record.class, record.getStr("biaoduanguid"));
                            String strZBDW = "";
                            for (int i = 0; i < dataList.size(); i++) {
                                sendTZS_JJ(dataList.get(i), record, "1", i, "");
                                strZBDW += dataList.get(i).getStr("DanWeiName") + "/";
                                // 反填中标信息,投标人卡片上显示中标
                                instance.execute(
                                        "update cg_toubiaodanwei set iszhongbiao = '1' where biaoduanguid=? and danweiguid = ?",
                                        record.getStr("biaoduanguid"), dataList.get(i).getStr("danweiguid"));
                            }

                            sql = "select a.* from cg_pingbiao_toubiaoinfo a where a.isin = '1' and a.danweiguid not in (select DanWeiGuid from CG_ZhongBiaoDetail where BiaoDuanGuid=?) and a.BiaoDuanGuid=?";
                            dataList = instance.findList(sql, Record.class, record.getStr("biaoduanguid"),
                                    record.getStr("biaoduanguid"));
                            for (int i = 0; i < dataList.size(); i++) {
                                sendTZS_JJ(dataList.get(i), record, "2", i, strZBDW.substring(0, strZBDW.length() - 1));
                            }
                        }
                        else {
                            String sql = "select ct.* from cg_toubiaodanwei ct  inner join cg_biaoduaninfo cb on ct.BiaoDuanGuid = cb.biaoduanguid and ct.danweiguid = cb.zhongbiaodanweiguid and cb.biaoduanguid = ? ";
                            List<Record> dataList = instance.findList(sql, Record.class, record.getStr("biaoduanguid"));
                            String strZBDW = "";
                            for (int i = 0; i < dataList.size(); i++) {
                                sendTZS_JJ(dataList.get(i), record, "1", i, "");
                                insertPingBiao_JJ(dataList.get(i), "1");
                                instance.execute(
                                        "update cg_toubiaodanwei set iszhongbiao = '1' where biaoduanguid=? and danweiguid = ?",
                                        record.getStr("biaoduanguid"), dataList.get(i).getStr("danweiguid"));
                                strZBDW += dataList.get(i).getStr("DanWeiName") + "/";
                            }

                            sql = "select ct.* from cg_toubiaodanwei ct  inner join cg_biaoduaninfo cb on ct.BiaoDuanGuid = cb.biaoduanguid and ct.danweiguid != cb.zhongbiaodanweiguid and cb.biaoduanguid = ?";
                            dataList = instance.findList(sql, Record.class, record.getStr("biaoduanguid"));
                            for (int i = 0; i < dataList.size(); i++) {
                                sendTZS_JJ(dataList.get(i), record, "2", i, strZBDW.substring(0, strZBDW.length() - 1));
                                insertPingBiao_JJ(dataList.get(i), "2");
                            }
                        }

                        if ("1".equals(record.getStr("PublishType"))) {
                            // 更新中标公示状态
                            instance.execute(
                                    "update CG_ZhongBiaoGS set auditstatus = '3' where gonggaoguid in (select gonggaoguid from CG_ZBGSAndBD where biaoduanguid = ? )",
                                    record.getStr("biaoduanguid"));

                            List<Record> detaillist = instance.findList(
                                    "select * from cg_zhongbiaodetail where biaoduanguid = ?", Record.class,
                                    record.getStr("biaoduanguid"));

                            if (!EpointCollectionUtils.isEmpty(detaillist)) {
                                BdExcuteDTO bdExcuteDTO = new BdExcuteDTO();
                                bdExcuteDTO.setTaskcode("ZhongBiaoGongShi");
                                bdExcuteDTO.setBiaoduanguid(record.getStr("biaoduanguid"));
                                Record recdetail = (Record) detaillist.get(0);
                                bdExcuteDTO.setZhongbiaopmname(recdetail.getStr("pmname"));
                                bdExcuteDTO.setZhongbiaopmguid(recdetail.getStr("PMGUID"));
                                bdExcuteDTO.setZhongbiaodanweiname(recdetail.getStr("DANWEINAME"));
                                bdExcuteDTO.setZhongbiaodanweiguid(recdetail.getStr("DANWEIGUID"));
                                bdExcuteDTO.setZhongbiaomoney(recdetail.getDouble("ZHONGBIAOMONEY") != null
                                        ? recdetail.getDouble("ZHONGBIAOMONEY")
                                        : 0);
                                bdExcuteDTO.setZhongbiaoprice(recdetail.getDouble("ZHONGBIAOPRICE") != null
                                        ? recdetail.getDouble("ZHONGBIAOPRICE")
                                        : 0);
                                bdExcuteDTO.setZhongbiaopricedw(recdetail.getStr("ZHONGBIAOPRICEDW"));
                                bdExcuteDTO.setIszbrtuibzj("1");
                                bdExcuteDTO.setZbrtuibzjdate(new Date());
                                bdExcuteDTO.setDingbiaodate(new Date());
                                bdExcuteDTO.setIsyuxujiaofei("1");
                                IBiaoDuanAPI biaoduanApi = ContainerFactory.getContainInfo()
                                        .getComponent(IBiaoDuanAPI.class);
                                biaoduanApi.updateForBusiness(bdExcuteDTO);
                            }
                        }
                        else {
                            instance.execute(" delete from CG_ZBGSAndBD where  biaoduanguid = ?",
                                    record.getStr("biaoduanguid"));
                        }

                        // 发送完通知书反填下issendtzs字段 方便会员段展示卡片中中标人信息
                        instance.execute("update cg_biaoduaninfo set issendtzs = '1'", record.getStr("biaoduanguid"));

                        // 清除代办事宜
                        try {
                            IMessagesCenterService iMessagesCenterService = ContainerFactory.getContainInfo()
                                    .getComponent(IMessagesCenterService.class);

                            List<MessagesCenter> messagesCenterList = iMessagesCenterService
                                    .listWaitHandleByPviguid(record.getStr("pvi_guid"));
                            for (MessagesCenter message : messagesCenterList) {
                                iMessagesCenterService.deleteMessageForEver(message.getMessageItemGuid(), message.getTargetUser());
                            }

                        }
                        catch (Exception ex) {
                            ex.printStackTrace();
                        }

                        // 更新工作流
                        try {
                            IWFEngineAPI9 operationApi = ContainerFactory.getContainInfo().getComponent(IWFEngineAPI9.class);
                            WorkflowParameter9 workflowParameter9 = new WorkflowParameter9();
                            workflowParameter9.setOperateType(45);
                            workflowParameter9.setSendGuid("");
                            workflowParameter9.setOpinion("Erp审核通过自动同步");
                            workflowParameter9.setProcessVersionInstanceGuid(record.getStr("pvi_guid"));
                            operationApi.operate(JSON.toJSONString(workflowParameter9));

                        }
                        catch (Exception ex) {
                            ex.printStackTrace();
                        }

                        // 记录日志在系统中方便后续查询
                        DB_OperationLog.addOperationLog(record.getStr("BiaoDuanNO"), SysEnums.SubSysName.数据交换, SysEnums.OperName.修改,
                                "Erp审核通过更新文件状态，文件备案唯一标志@" + record.getStr("BiaoDuanNO") + "审核时间@"
                                        + EpointDateUtil.convertDate2String(new Date(), "yyyy-MM-dd HH:mm:ss"),
                                "中标结果备案审核");

                    } catch (Exception ex) {
                        ex.printStackTrace();
                    }
                }

            }
            else {
                if("0".equals(requestJsonObject.getString("tradestatus"))){//审核不通过
                    Record zbrecord = instance.find("select * from CG_ZhongBiaoGS where rowguid = ? ",Record.class,record.getStr("GongGaoGuid"));
                    // 重置工作台数据(进行中)
                    bidflowHandleService.resetMenuNodeitemByRowguid(record.getStr("GongGaoGuid"));
                    instance.execute("update CG_ZhongBiaoGS set auditstatus=?,currentstepname=?,operatedate = ? where rowguid=? ",
                            TradeEnums.AuditStatus.审核不通过.getValue(), "初始信息录入",new Date(), record.getStr("GongGaoGuid"));
                    workflowApi.resetWorkflow(zbrecord.getStr("pvi_guid"), zbrecord.getStr("sbr_code"), requestJsonObject.getString("tradereason"));
                    DB_OperationLog.addOperationLog(record.getStr("GongGaoGuid"), SysEnums.SubSysName.企业招采,
                            SysEnums.OperName.回第一步, "Erp审核不通过更新中标公示状态，中标公示唯一标志@" + record.getStr("GongGaoGuid") + "审核不通过时间@"
                                    + EpointDateUtil.convertDate2String(new Date(), "yyyy-MM-dd HH:mm:ss"),
                            "中标公示撤回修改");
                }
                else {
                    // 招标项目推送外网操作
                    String sqlUpdateZbjg = "update CG_ZhongBiaoGS set auditstatus = '3' where rowguid = ? ";
                    try {
                        push_zbgg_cnbm(record);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                    instance.execute(sqlUpdateZbjg, record.getStr("GongGaoGuid"));

                    // 清除代办事宜
                    try {
                        IMessagesCenterService iMessagesCenterService = ContainerFactory.getContainInfo()
                                .getComponent(IMessagesCenterService.class);

                        List<MessagesCenter> messagesCenterList = iMessagesCenterService
                                .listWaitHandleByPviguid(record.getStr("pvi_guid"));
                        for (MessagesCenter message : messagesCenterList) {
                            iMessagesCenterService.deleteMessageForEver(message.getMessageItemGuid(), message.getTargetUser());
                        }

                    } catch (Exception ex) {
                        ex.printStackTrace();
                    }

                    // 更新工作流
                    try {
                        IWFEngineAPI9 operationApi = ContainerFactory.getContainInfo().getComponent(IWFEngineAPI9.class);
                        WorkflowParameter9 workflowParameter9 = new WorkflowParameter9();
                        workflowParameter9.setOperateType(45);
                        workflowParameter9.setSendGuid("");
                        workflowParameter9.setOpinion("Erp审核通过自动同步");
                        workflowParameter9.setProcessVersionInstanceGuid(record.getStr("pvi_guid"));
                        operationApi.operate(JSON.toJSONString(workflowParameter9));

                    } catch (Exception ex) {
                        ex.printStackTrace();
                    }

                    // 记录日志在系统中方便后续查询
                    DB_OperationLog.addOperationLog(record.getStr("BiaoDuanNO"), SysEnums.SubSysName.数据交换, SysEnums.OperName.修改,
                            "Erp审核通过更新文件状态，文件备案唯一标志@" + record.getStr("BiaoDuanNO") + "审核时间@"
                                    + EpointDateUtil.convertDate2String(new Date(), "yyyy-MM-dd HH:mm:ss"),
                            "中标结果备案审核");
                }
            }
        }
        finally {
            if (instance != null) {
                instance.close();
            }
        }

    }

    /**
     * 根据标段标编号查询候选人公示数据
     *
     * @return ：
     */
    protected Record queryBidsectioncAndidateByBdno(String biaoduanno, boolean isfz) {
        ZtbCommonDao instance = ZtbCommonDao.getInstance();
        try {
            if (isfz) {
                String sqlByBdGuid = "select * from cg_shoubiaotuijian cz  where biaoduanguid in (select biaoduanguid from cg_biaoduaninfo cb where biaoduanno = ? and statuscode  = '3')";
                return instance.find(sqlByBdGuid, Record.class, biaoduanno);
            }
            else {
                String sqlByBdGuid = "select * from CG_CandidatePub cz  where bidsectionguid in (select biaoduanguid from cg_biaoduaninfo cb where biaoduanno = ? and statuscode  = '3')";
                return instance.find(sqlByBdGuid, Record.class, biaoduanno);
            }
        }
        finally {
            if (instance != null) {
                instance.close();
            }
        }
    }

    /**
     * 中标候选人公示修改为审核通过
     *
     * @param record
     *            ：
     */
    protected void updateBidsectioncAndidate(Record record,JSONObject requestJsonObject, boolean isfz) {
        ZtbCommonDao instance = ZtbCommonDao.getInstance();
        try {
            if (isfz) {
                if("0".equals(requestJsonObject.getString("tradestatus"))){//审核不通过
                    // 重置工作台数据(进行中)
                    bidflowHandleService.resetMenuNodeitemByRowguid(record.getStr("rowguid"));
                    instance.execute("update cg_shoubiaotuijian set auditstatus=?,currentstepname=?,operatedate = ? where rowguid=? ",
                            TradeEnums.AuditStatus.审核不通过.getValue(), "初始信息录入",new Date(), record.getStr("rowguid"));
                    workflowApi.resetWorkflow(record.getStr("pvi_guid"), record.getStr("sbr_code"), requestJsonObject.getString("tradereason"));
                    DB_OperationLog.addOperationLog(record.getStr("rowguid"), SysEnums.SubSysName.企业招采,
                            SysEnums.OperName.回第一步, "Erp审核不通过更新推荐成交状态，推荐成交唯一标志@" + record.getStr("rowguid") + "审核不通过时间@"
                                    + EpointDateUtil.convertDate2String(new Date(), "yyyy-MM-dd HH:mm:ss"),
                            "推荐成交撤回修改");
                }
                else {
                    // 非招使用的推荐成交表
                    String sqlUpdateZbjg = "update cg_shoubiaotuijian set auditstatus = '3',operatedate = ?,shr_date = ? where rowguid = ? ";
                    instance.execute(sqlUpdateZbjg, new Date() ,new Date() , record.getStr("RowGuid"));

                    // 非招的推荐成交的相关工作流完成后动作
                    // 先删除多余的授标数据
                    String sql = "delete from CG_ShouBiaoAndBDItem where 1=1 and BiaoDuanGuid= '" + record.getStr("BiaoDuanGuid") +"' ";
                    if ("1".equals(record.getStr("AWARDTYPE"))) {
                        sql += " and (awardtype in ('2','3') or isnull(awardquantity,0)=0) ";
                    } else {
                        sql += " and awardtype = '1'  ";
                    }

                    instance.execute(sql);
                    String gongshiguid = UUID.randomUUID().toString();

                    // 更新各单位的实际中标金额，只对具有排名的单位才计算
                    sql = "select * from cg_pingbiao_toubiaoinfo where biaoduanguid= ? and ifnull('Isfeibiao','0') !='1'  order by isnull(paiming,1000) asc ,row_id asc";
                    List<Record> danweilst = instance.findList(sql, Record.class, record.getStr("BiaoDuanGuid"));
                    BiaoDuanVO biaoDuanVO =  biaoduanApi.findByBiaoDuanGuid(record.getStr("BiaoDuanGuid"));
                    for (Record danwei : danweilst) {
                        Double totalprice = null ;
                        if(1 != biaoDuanVO.getIsusewebztb()) {
                            if ("1".equals(record.getStr("AWARDTYPE")) && StringUtil.isNotBlank(danwei.getStr("Paiming"))
                                    && !"0".equals(danwei.getStr("Paiming"))) {
                                if(StringUtil.isNotBlank(danwei.getStr("Pingbiaoprice"))) {
                                    totalprice =  new BigDecimal(danwei.getStr("Pingbiaoprice")).doubleValue();
                                }
                            }
                            else if (!"1".equals(record.getStr("AWARDTYPE"))) {
                                String sum = instance.queryString("select sum(QuasiAwardTotalPrice) from CG_ShouBiaoAndBDItem where biaoduanguid=? and danweiguid=? and awardtype in ('2','3')", new Object[]{record.getStr("BiaoDuanGuid"), danwei.getStr("DanWeiGuid")});
                                if(StringUtil.isNotBlank(sum)){
                                    totalprice = new BigDecimal(sum).doubleValue();
                                }
                            }
                        }
                        instance.execute("update cg_pingbiao_toubiaoinfo set Toubiaoprice = ? where rowguid = ? ",totalprice, danwei.getStr("RowGuid"));
                    }

                    BidflowExtVO extvo = null;
                    try {
                        sql = "select auditstatus,shr_date,rowguid,pvi_guid from cg_shoubiaotuijian where rowguid = ? ";
                        Record bean = ZtbCommonDao.getInstance().find(sql, Record.class, record.getStr("RowGuid"));
                        extvo.setAuditstatus(bean.getStr("auditstatus"));
                        extvo.setShrdate(bean.getDate("shr_date"));
                        extvo.setAuditstatus(bean.getStr("pvi_guid"));
                        extvo.setShrdate(bean.getDate("rowguid"));

                        WbBidflowLinkconfigs wbbidflowLinkconfigs = initWorkbenchByLinkguid(BidflowKeyNames.Bid_TradeProcessType.yewutype.getValue(),biaoDuanVO);

                        logger.info("wbbidflowLinkconfigs:" + wbbidflowLinkconfigs);
                        logger.info("wbbidflowLinkconfigs.menu:" + wbbidflowLinkconfigs.menu);
                        logger.info("wbbidflowLinkconfigs.pv:" + wbbidflowLinkconfigs.pv);
                        logger.info("wbbidflowLinkconfigs.pvi:" + wbbidflowLinkconfigs.pvi);

                        bidflowHandleService.recoveryMenuNodeitem(wbbidflowLinkconfigs.menu, wbbidflowLinkconfigs.pv, wbbidflowLinkconfigs.pvi, extvo);
                        bidflowHandleService.operateProcess(wbbidflowLinkconfigs.pv, biaoDuanVO.getBiaoduanguid(), wbbidflowLinkconfigs.menu, BidflowKeyNames.Bid_ProcessMenuItemStatus.MenuItemStatus_Completed, extvo);

                    }catch(Exception ex){
                        logger.info("非招推荐成交审核通过报错：" + ex);
                    }
                    biaoduanApi.updateProcessCode(record.getStr("BiaoDuanGuid"), TradeEnums.ProcessCodeType.已推荐成交.getIntValue());


                    String subpvi = instance.queryString("select processversioninstanceguid from workflow_pvi where mainpviguid  = ? and  status = '10' order by STARTDATE desc limit 1",record.getStr("pvi_guid"));
                    if(StringUtil.isBlank(subpvi)){
                        subpvi = record.getStr("pvi_guid") ;
                    }

                    WorkflowPvi wfp = instanceDataApi.getProcessVersionInstance(subpvi).getPvi();

                    // 清除代办事宜
                    try {
                        IMessagesCenterService iMessagesCenterService = ContainerFactory.getContainInfo()
                                .getComponent(IMessagesCenterService.class);

                        List<MessagesCenter> messagesCenterList = iMessagesCenterService
                                .listWaitHandleByPviguid(wfp.getProcessVersionInstanceGuid());
                        for (MessagesCenter message : messagesCenterList) {
                            iMessagesCenterService.deleteMessageForEver(message.getMessageItemGuid(), message.getTargetUser());
                        }

                    }
                    catch (Exception ex) {
                        logger.info("非招推荐成交审核通过清除待办报错：" + ex);
                    }

                    // 更新工作流
                    try {
                        IWFEngineAPI9 operationApi = ContainerFactory.getContainInfo().getComponent(IWFEngineAPI9.class);

                        WorkflowParameter9 workflowParameter9 = new WorkflowParameter9();
                        workflowParameter9.setOperateType(45);
                        workflowParameter9.setSendGuid("");
                        workflowParameter9.setOpinion("Erp审核通过自动同步");
                        workflowParameter9.setProcessVersionInstanceGuid(wfp.getProcessVersionInstanceGuid());
                        operationApi.operate(JSON.toJSONString(workflowParameter9));
                    } catch (Exception ex) {
                        logger.info("非招推荐成交审核通过工作流推进报错：" + ex);
                    }
                }
            }
            else {
                if("0".equals(requestJsonObject.getString("tradestatus"))){//审核不通过
                    // 重置工作台数据(进行中)
                    bidflowHandleService.resetMenuNodeitemByRowguid(record.getStr("rowguid"));
                    instance.execute("update CG_CandidatePub set auditstatus=?,currentstepname=?,operatedate = ? where rowguid=? ",
                            TradeEnums.AuditStatus.审核不通过.getValue(), "初始信息录入",new Date(), record.getStr("rowguid"));
                    workflowApi.resetWorkflow(record.getStr("pvi_guid"), record.getStr("sbr_code"), requestJsonObject.getString("tradereason"));
                    DB_OperationLog.addOperationLog(record.getStr("rowguid"), SysEnums.SubSysName.企业招采,
                            SysEnums.OperName.回第一步, "Erp审核不通过更新候选人公示状态，候选人公示唯一标志@" + record.getStr("rowguid") + "审核不通过时间@"
                                    + EpointDateUtil.convertDate2String(new Date(), "yyyy-MM-dd HH:mm:ss"),
                            "候选人公示撤回修改");
                }
                else {
                    // 招标项目推送外网操作
                    String sqlUpdateZbjg = "update CG_CandidatePub set auditstatus = ?,Gonggaostatuscode='9',Pubtype = '1',operatedate = ?,shr_date = ? where rowguid = ? ";
                    instance.execute(sqlUpdateZbjg, record.getStr("tradestatus"),new Date(),new Date(), record.getStr("RowGuid"));

                    //京东推送公告由job服务推送，暂时不写了

                    //短信推送以后有要求在编写

                    BidflowExtVO extvo = null;
                    try {
                        String sql = "select auditstatus,shr_date,rowguid,pvi_guid from CG_CandidatePub where rowguid = ? ";
                        Record bean = ZtbCommonDao.getInstance().find(sql, Record.class, record.getStr("RowGuid"));
                        BiaoDuanVO biaoDuanVO =  biaoduanApi.findByBiaoDuanGuid(record.getStr("bidsectionguid"));
                        extvo.setAuditstatus(bean.getStr("auditstatus"));
                        extvo.setShrdate(bean.getDate("shr_date"));
                        extvo.setAuditstatus(bean.getStr("pvi_guid"));
                        extvo.setShrdate(bean.getDate("rowguid"));

                        WbBidflowLinkconfigs wbbidflowLinkconfigs = initWorkbenchByLinkguid(BidflowKeyNames.Bid_TradeProcessType.yewutype.getValue(),biaoDuanVO);

                        logger.info("wbbidflowLinkconfigs:" + wbbidflowLinkconfigs);
                        logger.info("wbbidflowLinkconfigs.menu:" + wbbidflowLinkconfigs.menu);
                        logger.info("wbbidflowLinkconfigs.pv:" + wbbidflowLinkconfigs.pv);
                        logger.info("wbbidflowLinkconfigs.pvi:" + wbbidflowLinkconfigs.pvi);

                        bidflowHandleService.recoveryMenuNodeitem(wbbidflowLinkconfigs.menu, wbbidflowLinkconfigs.pv, wbbidflowLinkconfigs.pvi, extvo);
                        bidflowHandleService.operateProcess(wbbidflowLinkconfigs.pv, biaoDuanVO.getBiaoduanguid(), wbbidflowLinkconfigs.menu, BidflowKeyNames.Bid_ProcessMenuItemStatus.MenuItemStatus_Completed, extvo);

                    }catch(Exception ex){
                        logger.info("候选人公示审核通过报错：" + ex);
                    }

                    String subpvi = instance.queryString("select processversioninstanceguid from workflow_pvi where mainpviguid  = ? and  status = '10' order by STARTDATE desc limit 1",record.getStr("pvi_guid"));
                    if(StringUtil.isBlank(subpvi)){
                        subpvi = record.getStr("pvi_guid") ;
                    }

                    WorkflowPvi wfp = instanceDataApi.getProcessVersionInstance(subpvi).getPvi();

                    // 清除代办事宜
                    try {
                        IMessagesCenterService iMessagesCenterService = ContainerFactory.getContainInfo()
                                .getComponent(IMessagesCenterService.class);

                        List<MessagesCenter> messagesCenterList = iMessagesCenterService
                                .listWaitHandleByPviguid(wfp.getProcessVersionInstanceGuid());
                        for (MessagesCenter message : messagesCenterList) {
                            iMessagesCenterService.deleteMessageForEver(message.getMessageItemGuid(), message.getTargetUser());
                        }

                    } catch (Exception ex) {
                        logger.info("候选人公示审核通过清除待办报错：" + ex);
                    }

                    // 更新工作流
                    try {
                        IWFEngineAPI9 operationApi = ContainerFactory.getContainInfo().getComponent(IWFEngineAPI9.class);

                        WorkflowParameter9 workflowParameter9 = new WorkflowParameter9();
                        workflowParameter9.setOperateType(45);
                        workflowParameter9.setSendGuid("");
                        workflowParameter9.setOpinion("Erp审核通过自动同步");
                        workflowParameter9.setProcessVersionInstanceGuid(wfp.getProcessVersionInstanceGuid());
                        operationApi.operate(JSON.toJSONString(workflowParameter9));
                    } catch (Exception ex) {
                        logger.info("候选人公示审核通过工作流推进报错：" + ex);
                    }
                }
            }
        }
        finally {
            if (instance != null) {
                instance.close();
            }
        }
    }

    /**
     * 获取标段信息
     *
     * @param biaoduanguid：
     * @return ：
     */
    protected Record queryBidInformation(String biaoduanguid) {
        ZtbCommonDao instance = ZtbCommonDao.getInstance();
        try {
            String bdSql = "select * from cg_biaoduaninfo where biaoduanguid = ?";
            return instance.find(bdSql, Record.class, biaoduanguid);
        }
        finally {
            if (instance != null) {
                instance.close();
            }
        }
    }

    /**
     * 根据标段编号查询招标文件信息
     *
     * @param bidsectionno
     *            ：
     * @return ：
     */
    protected Record queryBiddingDocumentInformationByBdno(String bidsectionno) {
        ZtbCommonDao instance = ZtbCommonDao.getInstance();
        try {
            // 获取最新有效的标段编号的数据进行处理
            String sqlByBdGuid = "select * from view_cg_zhaobiaofileandbd  where biaoduanno = ?  and bdauditstatus ='3' ";
            return instance.find(sqlByBdGuid, Record.class, bidsectionno);
        }
        finally {
            if (instance != null) {
                instance.close();
            }
        }
    }

    /**
     * 根据项目编号查询招标文件信息
     *
     * @param projectno
     *            ：
     * @return ：
     */
    protected List<Record> queryBiddingDocumentInformationByProno(String projectno) {
        ZtbCommonDao instance = ZtbCommonDao.getInstance();
        try {
            String sqlByBdGuid = "select * from view_cg_zhaobiaofileandbd  where projectno = ?  and bdauditstatus ='3' ";
            return instance.findList(sqlByBdGuid, Record.class, projectno);
        }
        finally {
            if (instance != null) {
                instance.close();
            }
        }
    }

    /**
     * 招标文件数据修改为审核通过
     *
     * @param recordFile
     *            ：
     */
    public void updateBiddingDocumentsToApproved(Record recordFile,JSONObject requestJsonObject) {
        ZtbCommonDao instance = ZtbCommonDao.getInstance();
        try {
            // 新疆本次上线只处理招标项目，对于其他方式不做处理
            if ("G".equals(recordFile.getStr("zhaobiaofangshi")) || "Q".equals(recordFile.getStr("zhaobiaofangshi"))) {
                if("0".equals(requestJsonObject.getString("tradestatus"))){//审核不通过
                    Record zbrecord = instance.find("select * from cg_zhaobiaofilebeian where rowguid = ? ",Record.class,recordFile.getStr("BeiAnGuid"));
                    // 重置工作台数据(进行中)
                    bidflowHandleService.resetMenuNodeitemByRowguid(recordFile.getStr("BeiAnGuid"));
                    instance.execute("update cg_zhaobiaofilebeian set auditstatus=?,currentstepname=?,operatedate = ? where rowguid=? ",
                            TradeEnums.AuditStatus.审核不通过.getValue(), "初始信息录入",new Date(), recordFile.getStr("BeiAnGuid"));
                    workflowApi.resetWorkflow(zbrecord.getStr("pvi_guid"), zbrecord.getStr("sbr_code"), requestJsonObject.getString("tradereason"));
                    DB_OperationLog.addOperationLog(recordFile.getStr("BeiAnGuid"), SysEnums.SubSysName.企业招采,
                            SysEnums.OperName.回第一步, "Erp审核不通过更新文件状态，文件备案唯一标志@" + recordFile.getStr("BeiAnGuid") + "审核不通过时间@"
                                    + EpointDateUtil.convertDate2String(new Date(), "yyyy-MM-dd HH:mm:ss"),
                            "招标文件撤回修改");
                    System.out.println();
                }
                else {
                    // 只有在待审核的状态下进行数据处理A
                    if ("2".equals(recordFile.getStr("AuditStatus"))) {
                        // 如果是网招项目还要反写评标办法
                        // 更新资审评标办法和是否确认评标委员会
                        StringBuilder bdguids = new StringBuilder();
                        String sqlByBeiGuid = "select * from view_cg_zhaobiaofileandbd  where beianguid = ? ";
                        List<Record> lstBD = instance.findList(sqlByBeiGuid, Record.class, recordFile.getStr("BeiAnGuid"));
                        for (Record rec : lstBD) {
                            bdguids.append(rec.getStr("BiaoDuanGuid") + ",");
                        }
                        String biaoduanguids = StringUtil.trimEnd(bdguids.toString(), ",");
                        BdExcuteDTO bdExcuteDTO = new BdExcuteDTO();
                        bdExcuteDTO.setPbbf(recordFile.getStr("pbbf")); // 视图新增pbbf
                        bdExcuteDTO.setBiaoshuprice(recordFile.getDouble("Zbfilegbfmoney")); // 视图新增工本费
                        if (StringUtil.isNotBlank(recordFile.getStr("Bzjmoney"))) {
                            // 视图新增保证金
                            bdExcuteDTO.setBzjmoney(recordFile.getDouble("Bzjmoney"));
                        }
                        bdExcuteDTO.setBiaoduanguids(biaoduanguids);
                        bdExcuteDTO.setTaskcode(TaskCodes.ZBFILEREG);
                        bdExcuteDTO.setFileguid(recordFile.getStr("BeiAnGuid")); // 视图新增保证金
                        if (StringUtil.isNotBlank(recordFile.getStr("Biaoshupricetype"))) {
                            // 视图新增保证金
                            bdExcuteDTO.setBiaoshupricetype(Integer.valueOf(recordFile.getStr("Biaoshupricetype")));
                        }
                        // 这边调用标段的API反填保证金缴纳截止时间、文件领取时间
                        Date bzjEnd = null;
                        if (StringUtil.isNotBlank(recordFile.getStr("Bzjenddate"))
                                && StringUtil.isNotBlank(recordFile.getStr("Bzjendpmto"))) {
                            bzjEnd = EpointDateUtil.convertString2Date(
                                    EpointDateUtil.convertDate2String(recordFile.getDate("Bzjenddate"), "yyyy-MM-dd") + " "
                                            + recordFile.getStr("Bzjendpmto") + ":00",
                                    "yyyy-MM-dd HH:mm:ss");
                        } else if (StringUtil.isNotBlank(recordFile.getStr("Bzjenddate"))
                                && StringUtil.isBlank(recordFile.getStr("Bzjendpmto"))) {
                            bzjEnd = recordFile.getDate("Bzjenddate");
                        }
                        bdExcuteDTO.setBzjshoudate(bzjEnd);
                        Date zbfilelqfrom = recordFile.getDate("Zbfiledate") != null ? recordFile.getDate("Zbfiledate")
                                : new Date();
                        // 招标文件领取时间，招标公告、投标邀请书、招标文件、资审结果通知书完成后统一都更新到标段表 lsm 2022-11-25
                        bdExcuteDTO.setZbfilelqfromdate(zbfilelqfrom);
                        bdExcuteDTO.setZbfilelqtodate(recordFile.getDate("Zbfileenddate"));
                        bdExcuteDTO.setIspingbiaowyhsure(recordFile.getStr("IsPingBiaoWYHSure"));

                        IBiaoDuanAPI biaoduanApi = ContainerFactory.getContainInfo().getComponent(IBiaoDuanAPI.class);
                        biaoduanApi.updateForBusiness(bdExcuteDTO);

                        // 如果是招标项目则招标文件备案通过，消除代办事宜
                        String sqlUpdateZbFile = "update cg_zhaobiaofilebeian set auditstatus = '3' where rowguid = ? ";
                        instance.execute(sqlUpdateZbFile, recordFile.getStr("BeiAnGuid"));

                        // 清除代办事宜
                        try {
                            IMessagesCenterService iMessagesCenterService = ContainerFactory.getContainInfo()
                                    .getComponent(IMessagesCenterService.class);

                            List<MessagesCenter> messagesCenterList = iMessagesCenterService
                                    .listWaitHandleByPviguid(recordFile.getStr("pvi_guid"));
                            for (MessagesCenter message : messagesCenterList) {
                                iMessagesCenterService.deleteMessageForEver(message.getMessageItemGuid(),
                                        message.getTargetUser());
                            }

                        } catch (Exception ex) {
                            ex.printStackTrace();
                        }

                        // 更新工作流
                        try {
                            IWFEngineAPI9 operationApi = ContainerFactory.getContainInfo()
                                    .getComponent(IWFEngineAPI9.class);
                            WorkflowParameter9 workflowParameter9 = new WorkflowParameter9();
                            workflowParameter9.setOperateType(45);
                            workflowParameter9.setSendGuid("");
                            workflowParameter9.setOpinion("Erp审核通过自动同步");
                            workflowParameter9.setProcessVersionInstanceGuid(recordFile.getStr("pvi_guid"));
                            operationApi.operate(JSON.toJSONString(workflowParameter9));

                        } catch (Exception ex) {
                            ex.printStackTrace();
                        }

                        // 记录日志在系统中方便后续查询
                        DB_OperationLog.addOperationLog(recordFile.getStr("BeiAnGuid"), SysEnums.SubSysName.数据交换,
                                SysEnums.OperName.修改, "Erp审核通过更新文件状态，文件备案唯一标志@" + recordFile.getStr("BeiAnGuid") + "审核时间@"
                                        + EpointDateUtil.convertDate2String(new Date(), "yyyy-MM-dd HH:mm:ss"),
                                "公招文件备案审核");
                    }
                }
            }
        }
        finally {
            if (instance != null) {
                instance.close();
            }
        }

    }

    /**
     * 根据标段guid，查询查询项目立项
     *
     * @param biaoduanguid
     *            ：
     * @return ：
     */
    protected Record queryProjectInitiation(String biaoduanguid) {
        ZtbCommonDao instance = ZtbCommonDao.getInstance();
        try {
            String faBaoSql = "select * from cg_chubufabao where FaBaoGuid in (select fabaoguid from cg_biaoduaninfo where biaoduanguid = ?)";
            return instance.find(faBaoSql, Record.class, biaoduanguid);
        }
        finally {
            if (instance != null) {
                instance.close();
            }
        }
    }

    /**
     * 查询行项目信息
     *
     * @param biaoduanguid
     *            ：
     * @return ：
     */
    protected List<Record> queryLineItemInformation(String biaoduanguid) {
        String itemSql = "select a.*  from PR_PRLineItem a join CG_CaiGouItem b on a.RowGuid = b.ItemGuid where b.biaoduanguid = ? ";
        return ZtbCommonDao.getInstance().findList(itemSql, Record.class, biaoduanguid);
    }

    /**
     * 查询行项目信息
     *
     * @param biaoduanguid
     *            ：
     * @return ：
     */
    protected List<Record> queryLineItemInformation2(String biaoduanguid) {
        String itemSql = "select a.reqguid  ,b.esproductguid, a.sbr_unitguid,a.productcode, a.productcategory,a.taxrate,a.controlprice,b.FenJieUserCode, b.JingBanUser, b.JingBanUserCode, b.WeiTuoDate, b.CaiGouFSCode, b.IsNeedTwicePrice, b.IsNeedXiangXiPrice, b.IsWeiTuo, b.IsXiuGaiCZFS, b.LastCaoZuoFSCode, b.StatusCode, b.BiaoDuanBH, b.ItemGuid, b.YuSuanGuid, b.AuditStatus, b.BackReason, b.BeiAnShiJian, b.BeiAnZhuangTai, b.BelongXiaQuCode, b.BiaoDuanGuid, b.CaiGouType, b.CaiZhengBH, b.CaiZhengDanWeiPrice, b.CaiZhengGuid, b.CaiZhengYuSuanNeiPrice, b.CaiZhengYuSuanPrice, b.CaiZhengYuSuanWaiPrice, b.CGTiJiaoYJ, b.DanWeiPrice, b.GongYingShangGuid, b.GongYingShangName, b.IsZuJian, b.JieSuanDanWeiPrice, b.JieSuanJiaGe, b.JieSuanShiJian, b.JieSuanYiJian, b.JieSuanYuSuanNeiPrice, b.JieSuanYuSuanWaiPrice, b.JieSuanZhuangTai, b.OperateDate, b.OperateUserName, b.OrgProjectGuid, b.ProjectGuid, b.ProjectJiaoYiType, b.RowGuid, b.SBR_Code, b.SBR_Date, b.SBR_Name, b.SBR_UnitName, b.ShangChuanBeiZhu, b.ShangChuanGuid, b.ShangPinDW, b.ShangPinGuid, b.ShangXianPrice, b.ShiChangPrice, b.ShouLiGuid, b.SHR_Code, b.SHR_Date, b.SHR_Name, b.SingPrice, b.XiaFuLv, b.XiangMuFZR, b.XIangMuFZRGuid, b.XiangMuXZ, b.XiangMuXZGuid, b.XiaQuCode, b.YanShouShiJian, b.YanShouZhuangTai, b.YearFlag, b.YuSuanNeiPrice, b.YuSuanPrice, b.YuSuanWaiPrice, b.ParentItemGuid, b.XieYiDJ, b.XieYiJG, b.ZhongBiaoDW, b.ZhongBiaoDWGUID, b.OrderNum, b.BeiZhu, b.CaiGouSL, b.JiShuZBGuid, b.ShiYongDate, b.ZiJinLY, b.ZiJinShDate, b.ZiJinShYj, b.ItemName, b.LeiXingCode, b.LeiXingName, b.MuLuCode, b.MuLuName, b.PinPaiCode, b.PinPaiName, b.XiangXiPZ, b.XingHaoCode, b.XingHaoName, b.ItemHuiZongGuid, b.JiaGeFW, b.JiaGeFWCode, b.PinPaiCode_Multi, b.PinPaiName_Multi, b.PinPaiType, b.ZhongBiaoDJ, b.ZhongBiaoJG, b.ZhongBiaoZKJ, b.ROW_ID, b.itemno, b.needQuantity, b.fabaoguid, b.lineitemno, b.currency, b.isimportgoods, b.specifications, b.quality, b.AwardStatus, b.AwardQuantity, b.tenantguid, b.isdelete, b.logtime, b.ishavedatachanged, b.datarowguid, b.isextract, b.dataversion, b.islatest, b.isfabaoadd, b.contractid, b.sysl, b.isgl, b.deliverdate, b.deliverfangshi, b.essourceid, b.esmaterialcode  from PR_PRLineItem a join CG_CaiGouItem b on a.RowGuid = b.ItemGuid where b.biaoduanguid = ? ";
        return ZtbCommonDao.getInstance().findList(itemSql, Record.class, biaoduanguid);
    }

    /**
     * 查询行项目报价信息
     *
     * @param biaoduanguid
     *            ：
     * @param danweiguid
     *            ：
     * @return ：
     */
    protected List<Record> queryLineItemQuotationInformation(String biaoduanguid, String danweiguid, String itemGuid) {
        ZtbCommonDao instance = ZtbCommonDao.getInstance();
        try {
            String bjSql = "select * from cg_toubiaobaojia where BiaoDuanGuid = ? and  DanWeiGuid = ? and ItemGuid = ? and ItemGuid is not null order by bjlunci";
            return instance.findList(bjSql, Record.class, biaoduanguid, danweiguid, itemGuid);
        }
        finally {
            if (instance != null) {
                instance.close();
            }
        }
    }

    /**
     * 获取结束时间
     *
     * @return
     */
    protected String getEndDate(String fromdateStr) {
        Date fromdate = null;
        try {
            fromdate = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(fromdateStr);
        }
        catch (ParseException e) {
            logger.info(
                    "com.epoint.cnbm.ztb.erpandprocurementplatform.service.ErpAndProcurementPlatformService#getEndTime方法，转换时间报错");
        }
        Date enddate = EpointDateUtil.addMinute(fromdate, 60 * 8);
        return new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(enddate);
    }

    /**
     * 获取开始时间
     *
     * @return
     */
    protected String getFromDate(String enddateStr) {
        Date enddate = null;
        try {
            enddate = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(enddateStr);
        }
        catch (ParseException e) {
            logger.info(
                    "com.epoint.cnbm.ztb.erpandprocurementplatform.service.ErpAndProcurementPlatformService#getEndTime方法，转换时间报错");
        }
        Date fromdate = EpointDateUtil.addMinute(enddate, -(60 * 8));
        return new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(fromdate);
    }

    /**
     * 获取采购行项目信息
     *
     * @return
     */
    protected Record obtainProcurementItemInformation(String itemGuid) {
        String proItemSql = "select *  from cg_caigouitem where  ItemGuid = ?";
        return ZtbCommonDao.getInstance().find(proItemSql, Record.class, itemGuid);
    }

    /**
     * 获取拥有推荐成交的单位
     *
     * @return
     */
    protected Set<String> obtainProcurementMethodsWithRecommendedTransactions() {
        HashSet<String> nonTenderSet = new HashSet<>();
        nonTenderSet.add("X");
        nonTenderSet.add("J");
        nonTenderSet.add("BX");
        return nonTenderSet;
    }

    /**
     * 获取报价方案信息
     *
     * @param bjfanganguid
     * @return
     */
    protected List<Record> getBaoJiaoFangAnItem(String bjfanganguid) {
        return ZtbCommonDao.getInstance().findList("select * from cg_baojiafangan_item where bjfanganguid=?",
                Record.class, bjfanganguid);
    }

    /**
     * 获取最后轮次的报价信息
     *
     * @param biaoduanGuid
     * @param zbdanweiGuid
     * @param itemGuid
     * @param bjlunci
     * @param dataBean_BJFangAnlst
     * @return
     */
    protected JSONObject gettouBiaoBaoJia(String biaoduanGuid, String zbdanweiGuid, String itemGuid, String bjlunci,
                                          List<Record> dataBean_BJFangAnlst) {
        SqlBuilder sqlBuilder = new SqlBuilder();
        String result = "";
        sqlBuilder.append("select * from cg_toubiaobaojia where biaoduanguid=?", biaoduanGuid);
        sqlBuilder.append(" and bjlunci=?", bjlunci);
        sqlBuilder.append(" and danweiguid=?", zbdanweiGuid);
        if (StringUtil.isNotBlank(itemGuid)) {
            sqlBuilder.append(" and itemguid=?", itemGuid);
        }
        Record toubaobaojia = ZtbCommonDao.getInstance().find(sqlBuilder.getSql(), Record.class,
                sqlBuilder.getParams());
        if (toubaobaojia != null) {
            result = toubaobaojia.getStr("baojiaitemdata");
        }else{
            //到这里是报名的单位最后一轮没有报价
            JSONObject objBJItemData = new JSONObject();
            for (Record objBJFangAnItem : dataBean_BJFangAnlst) {
                objBJItemData.put(objBJFangAnItem.getStr("bjitemno"),"");
            }
            return objBJItemData;
        }
        if (StringUtil.isBlank(result)) {
            JSONObject objBJItemData = new JSONObject();
            for (Record objBJFangAnItem : dataBean_BJFangAnlst) {
                objBJItemData.put(objBJFangAnItem.getStr("bjitemno"),
                        toubaobaojia.get(objBJFangAnItem.getStr("bjitemno")));
            }
            return objBJItemData;
        }
        else {
            return JSONObject.parseObject(result);
        }
    }

    // 法招项目公告推送
    public String push_zbgg_cnbm(Record record) throws Exception {
        IFrameConfigService frameConfigService = ContainerFactory.getContainInfo()
                .getComponent(IFrameConfigService.class);
        if ("1".equals(frameConfigService.getFrameConfigValue("announcementPush"))) {
            {
                String str_access_reqforward = "";
                // 公告推送
                JSONObject jsonResponse = new JSONObject();
                JSONObject jsonBody = new JSONObject();
                JSONObject jsonRequestEntity = new JSONObject();
                JSONObject jsonNoticeInfo = new JSONObject();

                jsonBody.put("AuthorizationInfo", getAuthInfo());
                jsonBody.put("clientId", frameConfigService.getFrameConfigValue("cnbm_ecg_clientid"));
                jsonBody.put("clientIdReq", frameConfigService.getFrameConfigValue("cnbm_ecg_clientidreq"));
                jsonBody.put("requestMethodName", "");
                jsonBody.put("requestMethodType", "POST");

                jsonNoticeInfo.put("noticeTitle", record.getStr("Gonggaotitle"));
                jsonNoticeInfo.put("projectCode", record.getStr("Showbiaoduanno"));
                jsonNoticeInfo.put("createTime", EpointDateUtil.convertDate2String(new Date(), "yyyy-MM-dd HH:mm:ss"));
                jsonNoticeInfo.put("quoteStopTime", "");
                jsonNoticeInfo.put("companyName", record.getStr("Jianshedanwei"));
                jsonNoticeInfo.put("tenderName", record.getStr("Jianshedanwei"));
                jsonNoticeInfo.put("contentText", record.getStr("Gonggaocontent"));
                jsonNoticeInfo.put("quoteStartTime",
                        EpointDateUtil.convertDate2String(record.getDate("Gsfromdate"), "yyyy-MM-dd HH:mm:ss"));
                jsonNoticeInfo.put("sourceUrl", frameConfigService.getFrameConfigValue("cnbm_ecg_bidderurl"));
                // 公告类型（0 初始公告 1 变更公告 2 结果公告 3 公示公告)
                jsonNoticeInfo.put("noticeType", "2");
                // 项目类型(1 公开招标 2 邀请招标 3 单一来源 4 竞争性谈判 5 其他 6 询比价 8 竞争性磋商 9 竞价)
                // 目前项目而言只有公开又变更公告方式转码,默认公开
                String str_projecttype = "1";
                switch (record.getStr("Zhaobiaofangshi")) {
                    case "G":
                        str_projecttype = "1";
                        break;
                    case "Q":
                        str_projecttype = "2";
                        break;
                    default:
                        str_projecttype = "5";
                        break;
                }

                jsonNoticeInfo.put("projectType", str_projecttype);
                jsonNoticeInfo.put("outPlatformCode", "1");

                jsonRequestEntity.put("name", "longdaoyun.ecg.withoutnotice.synchronizationNotice");
                jsonRequestEntity.put("noticeInfo", jsonNoticeInfo);
                jsonBody.put("requestEntity", jsonRequestEntity);

                logger.info("@@@@@@@@@@@@@@@@@@@@@@@@@@@@");
                logger.info(jsonBody.toString());

                jsonResponse = JSON.parseObject(HttpUtil.doPostJson(
                        frameConfigService.getFrameConfigValue("cnbm_ecg_reqforwardurl"), jsonBody.toString()));
                logger.info(jsonBody.toString());
                logger.info("@@@@@@@@@@@@@@@@@@@@@@@@@@@@");

                if (!jsonResponse.getBoolean("success")) {
                    try {
                        Thread.sleep(1000);
                        str_access_reqforward = push_zbgg_cnbm(record);
                    }
                    catch (InterruptedException e) {
                        e.printStackTrace();
                        str_access_reqforward = "三方对接网站推送网站环节出现异常！";
                    }
                }
            }
        }
        return "";
    }

    // 非招项目公告推送
    public String push_fz_zbgg_cnbm(Record record) throws Exception {
        IFrameConfigService frameConfigService = ContainerFactory.getContainInfo()
                .getComponent(IFrameConfigService.class);
        if ("1".equals(frameConfigService.getFrameConfigValue("announcementPush"))) {
            {
                // 非直接通知书项目，要发布公告
                if ("2".equals(record.getStr("publishtype"))) {
                    return "";
                }

                String str_access_reqforward = "";
                // 公告推送
                JSONObject jsonResponse = new JSONObject();
                JSONObject jsonBody = new JSONObject();
                JSONObject jsonRequestEntity = new JSONObject();
                JSONObject jsonNoticeInfo = new JSONObject();

                jsonBody.put("AuthorizationInfo", getAuthInfo());
                jsonBody.put("clientId", frameConfigService.getFrameConfigValue("cnbm_ecg_clientid"));
                jsonBody.put("clientIdReq", frameConfigService.getFrameConfigValue("cnbm_ecg_clientidreq"));
                jsonBody.put("requestMethodName", "");
                jsonBody.put("requestMethodType", "POST");

                jsonNoticeInfo.put("noticeTitle", record.getStr("biaoduanname") + "成交公告");
                jsonNoticeInfo.put("projectCode", record.getStr("biaoduanno"));
                jsonNoticeInfo.put("createTime", EpointDateUtil.convertDate2String(new Date(), "yyyy-MM-dd HH:mm:ss"));
                jsonNoticeInfo.put("quoteStopTime", "");

                String jsdw = service.queryString("select Jianshedanwei from cg_projectinfo where projectno = ? ",
                        record.getStr("projectno"));
                jsonNoticeInfo.put("companyName", jsdw);
                jsonNoticeInfo.put("tenderName", jsdw);

                Record gsbean = service.find(
                        "select * from CG_ZhongBiaoGS where gonggaoguid in (select gonggaoguid from CG_ZBGSAndBD where biaoduanguid = ?)",
                        Record.class, record.getStr("biaoduanguid"));

                jsonNoticeInfo.put("contentText", gsbean.getStr("gonggaocontent"));
                jsonNoticeInfo.put("quoteStartTime",
                        EpointDateUtil.convertDate2String(record.getDate("gsfromdate"), "yyyy-MM-dd HH:mm:ss"));
                jsonNoticeInfo.put("sourceUrl", frameConfigService.getFrameConfigValue("cnbm_ecg_bidderurl"));
                // 公告类型（0 初始公告 1 变更公告 2 结果公告 3 公示公告)
                jsonNoticeInfo.put("noticeType", "2");
                // 项目类型(1 公开招标 2 邀请招标 3 单一来源 4 竞争性谈判 5 其他 6 询比价 8 竞争性磋商 9 竞价)
                // 目前项目而言只有公开又变更公告方式转码,默认公开
                String str_projecttype = "1";
                switch (record.getStr("Zhaobiaofangshi")) {
                    case "DA":
                    case "DanY":
                    case "GYJZ":
                        str_projecttype = "3";
                        break;
                    case "J":
                        str_projecttype = "4";
                        break;
                    case "X":
                    case "BX":
                    case "GYXJ":
                        str_projecttype = "6";
                        break;
                    case "B":
                        str_projecttype = "9";
                        break;
                    default:
                        str_projecttype = "5";
                        break;
                }

                jsonNoticeInfo.put("projectType", str_projecttype);
                jsonNoticeInfo.put("outPlatformCode", "1");

                jsonRequestEntity.put("name", "longdaoyun.ecg.withoutnotice.synchronizationNotice");
                jsonRequestEntity.put("noticeInfo", jsonNoticeInfo);
                jsonBody.put("requestEntity", jsonRequestEntity);

                logger.info("@@@@@@@@@@@@@@@@@@@@@@@@@@@@");
                logger.info(jsonBody.toString());

                jsonResponse = JSON.parseObject(HttpUtil.doPostJson(
                        frameConfigService.getFrameConfigValue("cnbm_ecg_reqforwardurl"), jsonBody.toString()));

                logger.info(jsonResponse.toString());
                logger.info("@@@@@@@@@@@@@@@@@@@@@@@@@@@@");

                if (!jsonResponse.getBoolean("success")) {
                    try {
                        Thread.sleep(1000);
                        str_access_reqforward = push_fz_zbgg_cnbm(record);
                    }
                    catch (InterruptedException e) {
                        e.printStackTrace();
                        str_access_reqforward = "三方对接网站推送网站环节出现异常！";
                    }
                }
            }
        }
        return "";
    }

    // 授权
    public String getAuthInfo() {
        IFrameConfigService frameConfigService = ContainerFactory.getContainInfo()
                .getComponent(IFrameConfigService.class);
        // 授权
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("grantType", "authorization_code");
        jsonObject.put("clientId", frameConfigService.getFrameConfigValue("cnbm_ecg_clientid"));
        jsonObject.put("clientSecret", frameConfigService.getFrameConfigValue("cnbm_ecg_clientsecret"));

        JSONObject jsonResponseAuth = new JSONObject();
        JSONObject jsonResponseDataAuth = new JSONObject();
        String str_access_token = "";
        jsonResponseAuth = JSON.parseObject(HttpUtil
                .doPostJson(frameConfigService.getFrameConfigValue("cnbm_ecg_authenticateurl"), jsonObject.toString()));
        if (jsonResponseAuth.getBoolean("success")) {
            jsonResponseDataAuth = jsonResponseAuth.getJSONObject("data");
            str_access_token = jsonResponseDataAuth.getString("access_token");
        }
        else {
            try {
                Thread.sleep(1000);
                str_access_token = getAuthInfo();
            }
            catch (InterruptedException e) {
                e.printStackTrace();
                str_access_token = "三方对接网站授权环节出现异常！";
            }
        }
        return str_access_token;
    }

    // 竞价插入通知书
    public void sendTZS_JJ(Record oRow_TBDW, Record oRow_ZBJG, String tzstype, int index, String zbdw) {
        // 如果投标单位表，已经发送过，不再重复推送数据
        if (oRow_TBDW != null && !"1".equals(oRow_TBDW.getStr("Issendtzs"))) {

            String strMessage = "";
            Record detailBean = new Record();
            detailBean.setSql_TableName("CG_ZhongBiaoTZS");
            detailBean.setPrimaryKeys("RowGuid");

            if ("1".equals(tzstype)) {
                // 发送中标通知书，201094表TZSType=1
                detailBean.set("Tongzhishuno", "ZBTZS" + oRow_ZBJG.getStr("Biaoduanno"));
                detailBean.set("Tzstype", "1");
                detailBean.set("Tzscontent",
                        getZBTZSContent_JJ(oRow_TBDW.getStr("Danweiname"), oRow_ZBJG.getStr("Biaoduanname")));
                strMessage = "【成交通知书】标段(包)名称:" + oRow_ZBJG.getStr("Biaoduanname");
            }
            else {
                // 发送招标结果通知书，201094表TZSType=2
                detailBean.set("Tongzhishuno", "JGTZS" + oRow_ZBJG.getStr("Biaoduanno") + "to" + (index + 1));
                detailBean.set("Tzstype", "2");
                detailBean.set("Tzscontent",
                        getJGTZSContent_JJ(oRow_TBDW.getStr("Danweiname"), oRow_ZBJG.getStr("Biaoduanname"), zbdw));
                strMessage = "【结果通知书】标段(包)名称:" + oRow_ZBJG.getStr("Biaoduanname");
            }

            // 保存中标人/未中标人代码，数据交换用 张佩龙 2014-07-15
            detailBean.set("Zhongbiaorencode", oRow_TBDW.getStr("Unitorgnum"));
            detailBean.set("Biaoduanguid", oRow_ZBJG.getStr("Biaoduanguid"));
            detailBean.set("Biaoduanno", oRow_ZBJG.getStr("Biaoduanno"));
            detailBean.set("Biaoduanname", oRow_ZBJG.getStr("Biaoduanname"));
            detailBean.set("Xiaqucode", "");
            detailBean.set("Danweiguid", oRow_TBDW.getStr("Danweiguid"));
            detailBean.set("Senddanweiguid", oRow_TBDW.getStr("Danweiguid"));
            detailBean.set("Danweiname", oRow_TBDW.getStr("Danweiname"));
            detailBean.set("Senddanweiname", oRow_TBDW.getStr("Danweiname"));
            // 代码项“金额币种代码”，人民币 156
            detailBean.set("Chengjiaoremark", 156);
            detailBean.set("Senddate", new Date());
            detailBean.set("Rowguid", UUID.randomUUID().toString());
            service.insert(detailBean);
            // 向该单位发送中标通知 向HuiYuan_AlertInfo表中插入数据
            DB_HuiYuan_AlertInfo.HuiYuan_AlertInfo_Add(oRow_TBDW.getStr("Danweiguid"), oRow_TBDW.getStr("Danweiname"),
                    "", strMessage, "", oRow_ZBJG.getStr("Sbr_code"), oRow_ZBJG.getStr("Sbr_name"), new Date(), "",
                    DB_HuiYuan_AlertInfo.MESSAGE_TYPE_TZS, detailBean.getStr("Tzscontent"), "13",
                    oRow_ZBJG.getStr("biaoduanguid"), oRow_ZBJG.getStr("Projectjiaoyitype"));
        }
    }

    public void insertPingBiao_JJ(Record oRow_TBDW, String type) {
        // type 1 中标； 2未中标
        oRow_TBDW.setSql_TableName("cg_pingbiao_toubiaoinfo");
        oRow_TBDW.setPrimaryKeys("RowGuid");
        if ("1".equals(type)) {
            oRow_TBDW.set("Iszhongbiao", "1");
        }
        oRow_TBDW.set("Issendtzs", "1");
        service.insert(oRow_TBDW);
    }


    private String getZBTZSContent_JJ(String DanWeiName, String BiaoDuanName) {
        return DanWeiName + "：我方已接受贵单位为" + BiaoDuanName + "项目的中标人。到成交通知书环节查看详细信息";
    }

    private String getJGTZSContent_JJ(String DanWeiName, String BiaoDuanName, String zbdw) {
        return DanWeiName + "：我方已接受" + zbdw + "所递交的" + BiaoDuanName + "项目的投标文件，确定" + zbdw + "为中标人。感谢你单位对我们工作的大力支持";
    }

    /**
     * 12获取推荐成交信息校验处理
     *
     * @param requestJsonObject
     *            ：
     * @return ：
     */
    public String bidseHandle(JSONObject requestJsonObject) {
        String projectno = requestJsonObject.getString("projectno");
        String biaoduanno = requestJsonObject.getString("biaoduanno");

        // 描述：校验项目编号
        if (StringUtil.isBlank(projectno)) {
            return "项目编号不能为空";
        }
        if (StringUtil.isBlank(biaoduanno)) {
            return "标段编号不能为空";
        }

        return "";
    }

    /**
     * 12处理Erp系统从招采系统获取的推荐成交信息
     *
     * @param requestJsonObject
     *            ：
     * @return ：
     */
    public List<JSONObject> dealBidsectioncAndIdateInfo(JSONObject requestJsonObject) {
        ArrayList<JSONObject> returnResultList = new ArrayList<>();

        // 查询 公招 -> 中标候选人公示(CG_CandidatePub)
        List<Record> gzresoultList = getCGCandidatePub(requestJsonObject);
        gzresoultList = EpointCollectionUtils.isEmpty(gzresoultList) ? new ArrayList<>() : gzresoultList;

        // 描述：进行数据处理
        for (Record gzrecord : gzresoultList) {
            // 描述：数据获取
            JSONObject gzbidjsonObject = gzbidDataHandle(gzrecord,requestJsonObject);
            returnResultList.add(gzbidjsonObject);
        }

        // 非招(询价、比选、竞谈) -> 推荐成交(CG_ShouBiaoTuiJian)
        List<Record> fzresoultList = getCGShouBiaoTuiJian(requestJsonObject);
        fzresoultList = EpointCollectionUtils.isEmpty(fzresoultList) ? new ArrayList<>() : fzresoultList;

        // 描述：进行数据处理
        for (Record fzrecord : fzresoultList) {
            // 描述：数据获取
            JSONObject fzjsonObject = fzbidDataHandle(fzrecord,requestJsonObject);
            returnResultList.add(fzjsonObject);
        }

        return returnResultList;
    }

    /**
     * 12公招获取数据处理
     *
     * @param jibeninfo
     *            ：
     * @return ：
     */
    private JSONObject gzbidDataHandle(Record jibeninfo,JSONObject requestJsonObject) {
        // 描述：数据分装项目基本信息
        JSONObject projectData = bidProjectInformation(jibeninfo,requestJsonObject);

        // 描述：数据封装候选人公示单位信息
        List<JSONObject> bidderDataList = gzDanweiBidder(jibeninfo);

        projectData.put("bidder", bidderDataList);

        return projectData;
    }

    /**
     * 12非招获取数据处理
     *
     * @param jibeninfo
     *            ：
     * @return ：
     */
    private JSONObject fzbidDataHandle(Record jibeninfo,JSONObject requestJsonObject) {
        // 描述：数据分装项目基本信息
        JSONObject projectData = bidProjectInformation(jibeninfo,requestJsonObject);

        // 描述：数据封装授标单位信息
        List<JSONObject> bidderDataList = fzDanweiBidder(jibeninfo);

        projectData.put("bidder", bidderDataList);

        return projectData;
    }

    /**
     * 12封装项目的基本信息
     *
     * @param jibeninfo
     *            ：
     * @return ：
     */
    private JSONObject bidProjectInformation(Record jibeninfo,JSONObject requestJsonObject) {
        // 描述：进行数据准备
        JSONObject projectData = new JSONObject();
        // 描述：项目信息组装
        projectData.put("projectname", jibeninfo.getStr("projectname"));
        projectData.put("projectno", jibeninfo.getStr("projectno"));
        projectData.put("jsdwlxr", jibeninfo.getStr("jsdwlxr"));
        projectData.put("jsdwlxrtel", jibeninfo.getStr("jsdwlxrtel"));
        projectData.put("ppexecute", jibeninfo.getStr("ppexecute"));// 采购执行模式
        // 描述：查询操作人信息
        HashMap<String, String> frameUserInfo = new HashMap<>();
        getFrameUserInfo(frameUserInfo, jibeninfo.getStr("sbr_code"));
        projectData.put("operateUserName", frameUserInfo.get("operateUserName"));
        projectData.put("operateUserTelephone", frameUserInfo.get("operateUserTelephone"));
        projectData.put("operateOuName", frameUserInfo.get("operateOuName"));
        // 描述：组装操作人所在企业的企业名称和企业的社会统一信用代码
        HashMap<String, String> operateDanweiInfo = new HashMap<>();
        getParentOuInfo(operateDanweiInfo, jibeninfo.getStr("sbr_unitguid"));
        projectData.put("operateDanweiName", operateDanweiInfo.get("operateDanweiName"));
        projectData.put("operateDanweiCode", operateDanweiInfo.get("operateDanweiCode"));
        // 描述：组装组织人员映射关系信息
        HashMap<String, String> memberinfo = new HashMap<>();
        getSysMemberInfo(memberinfo, jibeninfo.getStr("memberUserCode"), jibeninfo.getStr("memberOrgCode"),requestJsonObject.getString("sysCode"));
        projectData.put("memberUserCode", memberinfo.get("memberUserCode"));
        projectData.put("memberOrgCode", memberinfo.get("memberOrgCode"));
        projectData.put("sysCode", memberinfo.get("sysCode"));
        projectData.put("extinfo", jibeninfo.getStr("extinfo"));
        projectData.put("biaoduanname", jibeninfo.getStr("biaoduanname"));
        projectData.put("biaoduanno", jibeninfo.getStr("biaoduanno"));
        projectData.put("fabaocontent", jibeninfo.getStr("fabaocontent"));
        projectData.put("zhaobiaofangshi", jibeninfo.getStr("zhaobiaofangshi"));
        projectData.put("isusewebztb", jibeninfo.getStr("isusewebztb"));
        //解决科学计数法
        DecimalFormat decimalFormat = new DecimalFormat("###0.0000");
        projectData.put("touzigusuan",StringUtil.isNotBlank(jibeninfo.getStr("touzigusuan")) ? decimalFormat.format(new BigDecimal(jibeninfo.getStr("touzigusuan"))):"0");
        return projectData;
    }

    /**
     * 12公招封装单位信息
     *
     * @param jibeninfo
     *            ：
     * @return ：
     */
    private List<JSONObject> gzDanweiBidder(Record jibeninfo) {
        // 描述：进行数据准备
        List<JSONObject> bidderDataList = new ArrayList<>();
        String biaoduanguid = jibeninfo.getStr("biaoduanguid");
        String bjlunci = StringUtil.isBlank(jibeninfo.getStr("bjlunci")) ? "1" : jibeninfo.getStr("bjlunci");

        // 描述：获取所有单位信息
        List<Record> allUnitInformationList = obtainAllBiddingUnitInformation(biaoduanguid);

        // 描述：获取候选人信息
        List<Record> allPingbiaoToubiaoList = obtainAllPingbiaoToubiaoInfo(biaoduanguid);

        List<Record> dataBean_BJFangAnlst = getBaoJiaoFangAnItem(jibeninfo.getStr("bjfanganguid"));

        if (allUnitInformationList != null) {
            for (Record record : allUnitInformationList) {
                // 描述：进行数据准备
                String danweiguid = record.getStr("danweiguid");
                String danweicode = record.getStr("unitorgnum");
                String danweiname = record.getStr("danweiname");
                String unitorgnum = record.getStr("standardgyscode");

                // 描述：是否推荐单位
                String iscandidatedw = "0";
                String rankno = "";

                // 公招从候选人公示中获取
                if(EpointCollectionUtils.isNotEmpty(allPingbiaoToubiaoList)){
                    for (Record ptlist : allPingbiaoToubiaoList) {
                        if(danweiguid.equals(ptlist.getStr("danweiguid"))){
                            iscandidatedw = "1";
                            rankno = ptlist.getStr("paiming");
                            break;
                        }
                    }
                }

                // 描述：在评标情况里边查询汇总分和投标报价
                Record pbRecord = queryEvaluationData(biaoduanguid, danweiguid);
                // 描述：进行变量准备
                String huizongmark = "0";
                String toubiaoprice = "0";
                // 描述：进行判空处理
                huizongmark = CommonHandleUtils.getStringValueDefault(pbRecord, "huizongmark", "0");
                toubiaoprice = CommonHandleUtils.getStringValueDefault(pbRecord, "toubiaoprice", "0");

                // 描述：封装行项目信息
                List<JSONObject> lineiteminfoList = gzbidLineiteminfoList(jibeninfo, record, iscandidatedw);

                // 增加报价项
                JSONObject tempbjdata = gettouBiaoBaoJia(biaoduanguid,danweiguid,"", bjlunci, dataBean_BJFangAnlst);

                // 描述：数据封装
                JSONObject bidder = basicInformationBidsection(danweiguid, danweicode,danweiname,unitorgnum,iscandidatedw,huizongmark,toubiaoprice,rankno,EpointCollectionUtils.isEmpty(lineiteminfoList) ? tempbjdata : new JSONObject());

                if (EpointCollectionUtils.isNotEmpty(lineiteminfoList)) {
                    bidder.put("lineiteminfo", lineiteminfoList);
                }
                else {
                    bidder.put("lineiteminfo", new ArrayList<>());
                }

                bidderDataList.add(bidder);
            }
        }
        return bidderDataList;
    }

    /**
     * 12非招封装单位信息
     *
     * @param jibeninfo
     *            ：
     * @return ：
     */
    private List<JSONObject> fzDanweiBidder(Record jibeninfo) {
        // 描述：进行数据准备
        List<JSONObject> bidderDataList = new ArrayList<>();
        String biaoduanguid = jibeninfo.getStr("biaoduanguid");
        String bjlunci = StringUtil.isBlank(jibeninfo.getStr("bjlunci")) ? "1" : jibeninfo.getStr("bjlunci");

        List<Record> dataBean_BJFangAnlst = getBaoJiaoFangAnItem(jibeninfo.getStr("bjfanganguid"));

        // 描述：获取所有单位信息
        List<Record> allUnitInformationList = obtainAllBiddingUnitInformation(biaoduanguid);
        if (EpointCollectionUtils.isEmpty(allUnitInformationList)) {
            allUnitInformationList = new ArrayList<>();
        }

        // 描述：按标段授标获取所有候选人的信息
        List<Record> allBiaoDuanShouBiaoList = obtainBiaoDuanShouBiaoInfo(biaoduanguid);

        // 描述：按行项目标段授标获取所有单位信息
        List<Record> allItemShouBiaoList = obtainItemShouBiaoInfo(biaoduanguid);

        // 查询最后一轮报价总信息
        List<Record> lastbjinfoList = ZtbCommonDao.getInstance().findList(
                "select * from CG_TouBiaoBaoJia a where  ifnull(ItemGuid,'')='' and BIAODUANGUID =?  and BJLunCi= ? ",
                Record.class, biaoduanguid, bjlunci);
        if (EpointCollectionUtils.isEmpty(lastbjinfoList)) {
            lastbjinfoList = new ArrayList<>();
        }

        if (EpointCollectionUtils.isNotEmpty(allUnitInformationList)) {
            for (Record record : allUnitInformationList) {
                // 描述：进行数据准备
                String danweiguid = record.getStr("danweiguid");
                String danweicode = record.getStr("unitorgnum");
                String danweiname = record.getStr("danweiname");
                String unitorgnum = record.getStr("standardgyscode");
                String awardtype = jibeninfo.getStr("awardtype");

                // 描述：是否推荐单位
                String iscandidatedw = "0";
                String rankno = "";
                // 按标段授标，取CgPingbiaotoubiaoinfo里的数据
                if(EpointCollectionUtils.isNotEmpty(allItemShouBiaoList)){
                    awardtype = allItemShouBiaoList.get(0).getStr("awardtype");
                    if("1".equals(allItemShouBiaoList.get(0).getStr("awardtype"))){
                        if(EpointCollectionUtils.isNotEmpty(allBiaoDuanShouBiaoList)){
                            for (Record ptlist : allBiaoDuanShouBiaoList) {
                                if(danweiguid.equals(ptlist.getStr("danweiguid"))){
                                    iscandidatedw = "1";
                                    rankno = ptlist.getStr("paiming");
                                    break;
                                }
                            }
                        }
                    }
                    else if("2".equals(allItemShouBiaoList.get(0).getStr("awardtype"))){//按行项目授标
                        if(EpointCollectionUtils.isNotEmpty(allItemShouBiaoList)){
                            for (Record ptlist : allItemShouBiaoList) {
                                if(danweiguid.equals(ptlist.getStr("danweiguid"))){
                                    iscandidatedw = "1";
                                    break;
                                }
                            }
                        }
                    }
                    else if("3".equals(allItemShouBiaoList.get(0).getStr("awardtype"))){//按行项目分段授标
                        if(EpointCollectionUtils.isNotEmpty(allItemShouBiaoList)){
                            for (Record ptlist : allItemShouBiaoList) {
                                if(danweiguid.equals(ptlist.getStr("danweiguid"))){
                                    iscandidatedw = "1";
                                    break;
                                }
                            }
                        }
                    }
                }
                else{
                    if("1".equals(awardtype)){
                        if(EpointCollectionUtils.isNotEmpty(allBiaoDuanShouBiaoList)){
                            for (Record ptlist : allBiaoDuanShouBiaoList) {
                                if(danweiguid.equals(ptlist.getStr("danweiguid"))){
                                    iscandidatedw = "1";
                                    rankno = ptlist.getStr("paiming");
                                    break;
                                }
                            }
                        }
                    }
                    else if("2".equals(awardtype)){//按行项目授标
                        if(EpointCollectionUtils.isNotEmpty(allItemShouBiaoList)){
                            for (Record ptlist : allItemShouBiaoList) {
                                if(danweiguid.equals(ptlist.getStr("danweiguid"))){
                                    iscandidatedw = "1";
                                    break;
                                }
                            }
                        }
                    }
                }

                // 描述：进行变量准备
                String huizongmark = "0";
                String toubiaoprice = "0";
                // 投标金额赋值
                for (Record rec : lastbjinfoList) {
                    if (danweiguid.equals(rec.getStr("danweiguid"))) {
                        toubiaoprice = rec.getStr("itemtotalprice");
                        break;
                    }
                }

                // 描述：封装行项目信息
                List<JSONObject> lineiteminfoList = fzbidLineiteminfoList(jibeninfo, record, iscandidatedw,awardtype);

                // 增加报价项
                JSONObject tempbjdata = gettouBiaoBaoJia(biaoduanguid,danweiguid,"", bjlunci, dataBean_BJFangAnlst);

                // 描述：数据封装
                JSONObject bidder = basicInformationBidsection(danweiguid, danweicode,
                        danweiname,unitorgnum,iscandidatedw,huizongmark,toubiaoprice,rankno,EpointCollectionUtils.isEmpty(lineiteminfoList) ? tempbjdata : new JSONObject());

                if (EpointCollectionUtils.isNotEmpty(lineiteminfoList)) {
                    bidder.put("lineiteminfo", lineiteminfoList);
                }
                else {
                    bidder.put("lineiteminfo", new ArrayList<>());
                }

                bidderDataList.add(bidder);
            }
        }
        return bidderDataList;
    }

    protected JSONObject basicInformationBidsection(String danweiguid,  String danweicode,String danweiname,
                                                    String unitorgnum, String iscandidatedw, String zhdf, String toubiaoprice, String rankno,JSONObject tempbjdata) {

        JSONObject bidder = new JSONObject();
        bidder.put("danweiguid", danweiguid);
        bidder.put("danweicode", danweicode);
        bidder.put("danweiname", danweiname);
        bidder.put("unitorgnum", unitorgnum);
        bidder.put("iscandidatedw", iscandidatedw);
        bidder.put("zhdf", zhdf);
        // 描述：科学计数法处理
        if (StringUtil.isNotBlank(toubiaoprice)) {
            toubiaoprice = new BigDecimal(toubiaoprice).toPlainString();
        }
        bidder.put("toubiaoprice", toubiaoprice);
        bidder.put("rankno", rankno);
        bidder.put("baojiadata", tempbjdata);
        return bidder;
    }

    /**
     * 12封装公招行项目信息
     *
     * @return ：
     */
    private List<JSONObject> gzbidLineiteminfoList(Record jibeninfo, Record record, String iscandidatedw) {
        // 描述：进行数据准备
        List<JSONObject> lineiteminfoList = new ArrayList<>();
        String biaoduanguid = record.getStr("biaoduanguid");

        // 描述：查询行项目信息
        List<Record> ItemList = queryLineItemInformation2(biaoduanguid);

        FrameOu ouinfo = iFrameOuService.getFrameOu(jibeninfo.getStr("jianshedanweiguid"));

        // 描述：进行行项目的封装
        if (EpointCollectionUtils.isNotEmpty(ItemList)) {
            for (Record itemRow : ItemList) {
                // 需求数量
                String needquantity = "";
                // 响应单价
                String itemunitprice = "";
                // 采购需求编号
                String requirementnum = "";
                //是授标单位，赋值授标数量和授标单价
                if("1".equals(iscandidatedw)){
                    needquantity = itemRow.getStr("needquantity");
                    itemunitprice = itemRow.getStr("singprice");
                }

                String loginId = "";
                // operateid取立项操作人的guid
                FrameUser zbfzruser = frameUserService.getUserByUserField("userguid", jibeninfo.getStr("SBR_Code"));
                // 描述：查询采购需求的数据
                Record purchaseReq = obtainProcurementDemandData(itemRow.getStr("reqguid"));
                if (zbfzruser != null) {
                    loginId = zbfzruser.getLoginId();
                }
                else {
                    String prcompany = CommonHandleUtils.getStringValueDefault(purchaseReq,"prcompany","");
                    String prfzr = CommonHandleUtils.getStringValueDefault(purchaseReq,"prfzr","");
                    // 描述：进行用户信息查询
                    List<FrameUser> frameUserList = frameUserService.listFrameUser(prcompany, null, null, null);
                    for (FrameUser frameUser : frameUserList) {
                        if (prfzr.equals(frameUser.getDisplayName())) {
                            loginId = frameUser.getLoginId();
                            break;
                        }
                    }
                }
                requirementnum = CommonHandleUtils.getStringValueDefault(purchaseReq,"PRNO","");
                String productcodefix = cnbmIProductDataService.getOuProductPrefix(itemRow.getStr("sbr_unitguid"));
                Record cgESProduct = null;
                if (StringUtil.isNotBlank(itemRow.getStr("esproductguid"))) {
                    cgESProduct = cnbmIProductDataService.findByRowguid(itemRow.getStr("esproductguid"));
                }
                else {
                    cgESProduct = cnbmIProductDataService
                            .findByProductcode(productcodefix + itemRow.getStr("productcode"));
                }

                // 描述：封装行项目信息
                JSONObject item = new JSONObject();

                // 描述：封装字段信息
                item.put("purchaseunitname", jibeninfo.getStr("jianshedanwei"));
                item.put("purchaseunitcode", jibeninfo.getStr("jianshedanweiguid"));
                item.put("operateid", loginId);
                item.put("requirementnum", requirementnum);// 采购需求编号
                item.put("lineitemno", itemRow.getStr("lineitemno"));

                if (cgESProduct != null) {
                    if(StringUtil.isNotBlank(productcodefix) && "X".equals(productcodefix)){
                        if("x".equals(itemRow.getStr("productcode").substring(0,1))){
                            item.put("itemno", itemRow.getStr("productcode").replace("x",""));// 叶片的物料编码不给前缀
                        }
                        else if("X".equals(itemRow.getStr("productcode").substring(0,1))){
                            item.put("itemno", itemRow.getStr("productcode").replace("X",""));// 叶片的物料编码不给前缀
                        }
                        else{
                            item.put("itemno", itemRow.getStr("productcode"));
                        }
                    }
                    else{
                        item.put("itemno",CommonHandleUtils.getStringValueDefault(cgESProduct,"productcode",""));// 物料编码-私库
                    }
                    item.put("erpitemno",CommonHandleUtils.getStringValueDefault(cgESProduct,"source_id",""));// 物料编码-ERP
                    item.put("zhtitemno",CommonHandleUtils.getStringValueDefault(cgESProduct,"rowguid",""));// 物料编码-中台
                    if(ouinfo != null) {
                        logger.info("ouinfo.getOuname():" + ouinfo.getOuname());
                        logger.info("ouinfo.getOucodeLevel():" + ouinfo.getOucodeLevel());
                        if (ouinfo.getOuname().contains("天山材料股份有限公司") || ouinfo.getOucodeLevel().contains("天山材料股份有限公司")) {
                            item.put("itemno", StringUtil.isNotBlank(cgESProduct.getStr("material_code")) ? cgESProduct.getStr("material_code") : itemRow.getStr("productcode"));// 物料编码-私库
                        }
                    }
                }
                else {
                    if(StringUtil.isNotBlank(productcodefix) && "X".equals(productcodefix)){
                        if("x".equals(itemRow.getStr("productcode").substring(0,1))){
                            item.put("itemno", itemRow.getStr("productcode").replace("x",""));// 叶片的物料编码不给前缀
                        }
                        else if("X".equals(itemRow.getStr("productcode").substring(0,1))){
                            item.put("itemno", itemRow.getStr("productcode").replace("X",""));// 叶片的物料编码不给前缀
                        }
                        else{
                            item.put("itemno", itemRow.getStr("productcode"));
                        }
                    }
                    else{
                        item.put("itemno", itemRow.getStr("productcode"));// 物料编码-私库
                    }
                    item.put("erpitemno", "");// 物料编码-ERP
                    item.put("zhtitemno", "");// 物料编码-中台
                }
                item.put("productcategory", itemRow.getStr("productcategory"));
                item.put("productcommodity", itemRow.getStr("itemname"));
                item.put("productclass", itemRow.getStr("pinpaitype"));
                item.put("unit", itemRow.getStr("shangpindw"));
                item.put("issbdw", iscandidatedw);
                item.put("sbsl", needquantity);
                item.put("sbdj", itemunitprice);
                item.put("specifications", itemRow.getStr("specifications"));
                item.put("model", itemRow.getStr("xinghaoname"));
                item.put("quality", itemRow.getStr("quality"));
                item.put("brand", itemRow.getStr("pinpainame"));
                item.put("productdescription", itemRow.getStr("xiangxipz"));
                item.put("unitprice", itemRow.getStr("singprice"));
                item.put("num", itemRow.getStr("needquantity"));
                //解决科学计数法
                DecimalFormat decimalFormat = new DecimalFormat("###0.0000");
                item.put("totalprice", StringUtil.isNotBlank(itemRow.getStr("yusuanprice")) ? decimalFormat.format(new BigDecimal(itemRow.getStr("yusuanprice"))):"0");
                item.put("currency", itemRow.getStr("currency"));
                item.put("taxrate", StringUtil.isNotBlank(itemRow.getStr("taxrate")) ? itemRow.getStr("taxrate") : "0");
                item.put("controlprice", itemRow.getStr("controlprice"));
                // 公开邀请对于行项目的报价传空
                item.put("quotationinfos", new ArrayList<JSONObject>());

                // 描述：在单位里边增加行项目信息
                lineiteminfoList.add(item);
            }
        }
        return lineiteminfoList;
    }

    /**
     * 12封装非招行项目信息
     *
     * @return ：
     */
    private List<JSONObject> fzbidLineiteminfoList(Record jibeninfo, Record record, String iscandidatedw,String awardtype) {
        // 描述：进行数据准备
        List<JSONObject> lineiteminfoList = new ArrayList<>();
        String biaoduanguid = record.getStr("biaoduanguid");
        // 描述：查询行项目信息
        List<Record> ItemList = queryLineItemInformation2(biaoduanguid);
        // 获取报价方案
        List<Record> dataBean_BJFangAnlst = getBaoJiaoFangAnItem(jibeninfo.getStr("bjfanganguid"));

        FrameOu ouinfo = iFrameOuService.getFrameOu(jibeninfo.getStr("jianshedanweiguid"));
        // 描述：进行行项目的封装
        if (EpointCollectionUtils.isNotEmpty(ItemList)) {
            for (Record itemRow : ItemList) {
                // 需求数量
                String needquantity = "";
                // 响应单价
                String itemunitprice = "";
                String requirementnum = "";
                //是否授标单位
                String issbdw = "0";
                // 按标段授标是授标单位，赋值授标数量和授标单价
                List<Record> bjlist = ZtbCommonDao.getInstance().findList(
                        "select bjlunci,itemtotalprice,itemunitprice,maxquantity,itemunitpricenorate from CG_TouBiaoBaoJia where BiaoDuanGuid =? and DanWeiGuid =? and ItemGuid =?  order by bjlunci asc",
                        Record.class, record.getStr("biaoduanguid"), record.getStr("danweiguid"),
                        itemRow.getStr("itemguid"));

                //按行项目授标是授标单位，赋值授标数量
                Record sbitem = ZtbCommonDao.getInstance().find(
                        "select * from CG_ShouBiaoAndBDItem where BiaoDuanGuid =? and DanWeiGuid =?  and ItemGuid =? ",
                        Record.class, record.getStr("biaoduanguid"), record.getStr("danweiguid"),itemRow.getStr("itemguid"));

                logger.info("biaoduanguid："+record.getStr("biaoduanguid"));
                logger.info("danweiguid："+record.getStr("danweiguid"));
                logger.info("itemguid："+itemRow.getStr("itemguid"));
                if("1".equals(iscandidatedw)) {
                    if("1".equals(awardtype)){//按标段(包)授标
                        needquantity = itemRow.getStr("needquantity");
                        itemunitprice = bjlist.get(bjlist.size()-1).getStr("itemunitprice");
                    }
                    else if("2".equals(awardtype)){//按行项目授标
                        if(sbitem != null){
                            issbdw = "1";
                            needquantity = sbitem.getStr("awardquantity");
                            itemunitprice = StringUtil.isNotBlank(bjlist.get(bjlist.size()-1).getStr("itemunitprice")) ? bjlist.get(bjlist.size()-1).getStr("itemunitprice") : "";
                        }
                    }
                    else if("3".equals(awardtype)){//按行项目分段授标
                        if(sbitem != null){
                            issbdw = "1";
                            needquantity = sbitem.getStr("awardquantity");
                            itemunitprice = StringUtil.isNotBlank(bjlist.get(bjlist.size()-1).getStr("itemunitpricenorate")) ? bjlist.get(bjlist.size()-1).getStr("itemunitpricenorate") : "";
                        }
                    }
                }

                // 描述：进行用户信息查询
                String loginId = "";
                // operateid取立项操作人的guid
                FrameUser zbfzruser = frameUserService.getUserByUserField("userguid", jibeninfo.getStr("SBR_Code"));
                // 描述：查询采购需求的数据
                Record purchaseReq = obtainProcurementDemandData(itemRow.getStr("reqguid"));
                if (zbfzruser != null) {
                    loginId = zbfzruser.getLoginId();
                }
                else {
                    String prcompany = purchaseReq != null && StringUtil.isNotBlank(purchaseReq.getStr("prcompany")) ? purchaseReq.getStr("prcompany") : "";
                    String prfzr = purchaseReq != null && StringUtil.isNotBlank(purchaseReq.getStr("prfzr")) ? purchaseReq.getStr("prfzr") : "";
                    List<FrameUser> frameUserList = frameUserService.listFrameUser(prcompany, null, null, null);
                    for (FrameUser frameUser : frameUserList) {
                        if (prfzr.equals(frameUser.getDisplayName())) {
                            loginId = frameUser.getLoginId();
                            break;
                        }
                    }
                }
                requirementnum = CommonHandleUtils.getStringValueDefault(purchaseReq,"PRNO","");
                String productcodefix = cnbmIProductDataService.getOuProductPrefix(itemRow.getStr("sbr_unitguid"));
                Record cgESProduct = null;
                if (StringUtil.isNotBlank(itemRow.getStr("esproductguid"))) {
                    cgESProduct = cnbmIProductDataService.findByRowguid(itemRow.getStr("esproductguid"));
                }
                else {
                    cgESProduct = cnbmIProductDataService
                            .findByProductcode(productcodefix + itemRow.getStr("productcode"));
                }

                // 描述：封装行项目信息
                JSONObject item = new JSONObject();

                // 描述：封装字段信息
                item.put("purchaseunitname", jibeninfo.getStr("jianshedanwei"));
                item.put("purchaseunitcode", jibeninfo.getStr("jianshedanweiguid"));
                item.put("operateid", loginId);
                item.put("requirementnum", requirementnum);// 采购需求编号
                item.put("lineitemno", itemRow.getStr("lineitemno"));

                if (cgESProduct != null) {
                    if(StringUtil.isNotBlank(productcodefix) && "X".equals(productcodefix)){
                        if("x".equals(itemRow.getStr("productcode").substring(0,1))){
                            item.put("itemno", itemRow.getStr("productcode").replace("x",""));// 叶片的物料编码不给前缀
                        }
                        else if("X".equals(itemRow.getStr("productcode").substring(0,1))){
                            item.put("itemno", itemRow.getStr("productcode").replace("X",""));// 叶片的物料编码不给前缀
                        }
                        else{
                            item.put("itemno", itemRow.getStr("productcode"));
                        }
                    }
                    else{
                        item.put("itemno",CommonHandleUtils.getStringValueDefault(cgESProduct,"productcode",""));// 物料编码-私库
                    }
                    item.put("erpitemno",CommonHandleUtils.getStringValueDefault(cgESProduct,"source_id",""));// 物料编码-ERP
                    item.put("zhtitemno",CommonHandleUtils.getStringValueDefault(cgESProduct,"rowguid",""));// 物料编码-中台
                    if(ouinfo != null){
                        logger.info("ouinfo.getOuname():"+ouinfo.getOuname());
                        logger.info("ouinfo.getOucodeLevel():"+ouinfo.getOucodeLevel());
                        if(ouinfo.getOuname().contains("天山材料股份有限公司") || ouinfo.getOucodeLevel().contains("天山材料股份有限公司")){
                            item.put("itemno",StringUtil.isNotBlank(cgESProduct.getStr("material_code")) ? cgESProduct.getStr("material_code") : itemRow.getStr("productcode"));// 物料编码-私库
                        }
                    }
                }
                else {
                    if(StringUtil.isNotBlank(productcodefix) && "X".equals(productcodefix)){
                        if("x".equals(itemRow.getStr("productcode").substring(0,1))){
                            item.put("itemno", itemRow.getStr("productcode").replace("x",""));// 叶片的物料编码不给前缀
                        }
                        else if("X".equals(itemRow.getStr("productcode").substring(0,1))){
                            item.put("itemno", itemRow.getStr("productcode").replace("X",""));// 叶片的物料编码不给前缀
                        }
                        else{
                            item.put("itemno", itemRow.getStr("productcode"));
                        }
                    }
                    else{
                        item.put("itemno", itemRow.getStr("productcode"));// 物料编码-私库
                    }
                    item.put("erpitemno", "");// 物料编码-ERP
                    item.put("zhtitemno", "");// 物料编码-中台
                }
                item.put("productcategory", itemRow.getStr("productcategory"));
                item.put("productcommodity", itemRow.getStr("itemname"));
                item.put("productclass", itemRow.getStr("pinpaitype"));
                item.put("unit", itemRow.getStr("shangpindw"));
                item.put("issbdw", issbdw);
                item.put("sbsl", needquantity);
                item.put("sbdj", itemunitprice);
                item.put("specifications", itemRow.getStr("specifications"));
                item.put("model", itemRow.getStr("xinghaoname"));
                item.put("quality", itemRow.getStr("quality"));
                item.put("brand", itemRow.getStr("pinpainame"));
                item.put("productdescription", itemRow.getStr("xiangxipz"));
                item.put("unitprice", itemRow.getStr("singprice"));
                item.put("num", itemRow.getStr("needquantity"));
                //解决科学计数法
                DecimalFormat decimalFormat = new DecimalFormat("###0.0000");
                item.put("totalprice", StringUtil.isNotBlank(itemRow.getStr("yusuanprice")) ? decimalFormat.format(new BigDecimal(itemRow.getStr("yusuanprice"))) : "0" );
                item.put("currency", itemRow.getStr("currency"));
                JSONObject tempbjdata = gettouBiaoBaoJia(biaoduanguid, record.getStr("danweiguid"),
                        itemRow.getStr("itemguid"), jibeninfo.getStr("bjlunci"), dataBean_BJFangAnlst);
                if (StringUtil.isNotBlank(tempbjdata.getString("hshuilv"))) {
                    item.put("taxrate", tempbjdata.getString("hshuilv"));
                }
                else if (StringUtil.isNotBlank(tempbjdata.getString("hshuilvbaifenshu"))) {
                    item.put("taxrate", tempbjdata.getString("hshuilvbaifenshu"));
                }
                else {
                    item.put("taxrate",
                            StringUtil.isNotBlank(itemRow.getStr("taxrate")) ? itemRow.getStr("taxrate") : "0");
                }
                item.put("controlprice", itemRow.getStr("controlprice"));
                item.put("bjdata", tempbjdata);

                if (EpointCollectionUtils.isNotEmpty(bjlist)) {
                    List<JSONObject> quotationinfoList = new ArrayList<>();
                    for (Record bJRecord : bjlist) {
                        JSONObject quotationinfo = new JSONObject();
                        quotationinfo.put("lunci", bJRecord.getStr("bjlunci"));
                        quotationinfo.put("luncibj", bJRecord.getStr("itemtotalprice"));
                        quotationinfoList.add(quotationinfo);
                    }
                    item.put("quotationinfos", quotationinfoList);
                }
                else {
                    item.put("quotationinfos", new ArrayList<JSONObject>());
                }

                // 描述：在单位里边增加行项目信息
                lineiteminfoList.add(item);
            }
        }
        return lineiteminfoList;
    }

    protected List<Record> getCGCandidatePub(JSONObject requestJsonObject) {
        SqlBuilder gzBuilder = new SqlBuilder();
        gzBuilder.append(
                "select c.SBR_Code, a.auditstatus,d.companycode, b.projectname,b.projectno,c.jsdwlxr,c.jsdwlxrtel,b.biaoduanguid,b.biaoduanname,b.biaoduanno,b.fabaocontent  "
                        + ",b.zhaobiaofangshi,b.isusewebztb,b.touzigusuan,d.extinfo,d.jianshedanwei,d.jianshedanweiguid,b.sbr_name ,b.bjlunci,a.operateusername,a.sbr_code,c.ppexecute,a.sbr_unitguid,d.sbr_code as memberUserCode, c.sbr_unitguid as memberOrgCode"
                        + " from CG_CandidatePub a  join cg_biaoduaninfo b on a.fabaoguid = b.fabaoguid  "
                        + " left join cg_chubufabao c on b.fabaoguid = c.fabaoguid "
                        + " left join cg_projectinfo d on b.projectguid =d.projectguid " + "where b.statuscode ='3'  "
                        + " and b.zhaobiaofangshi in ('G','Q') ");
        // 标段编号,项目编号可以是多个，用逗号分开
        String biaoduanno = requestJsonObject.getString("biaoduanno");
        String projectno = requestJsonObject.getString("projectno");

        if (StringUtil.isNotBlank(biaoduanno)) {
            List<String> bdnoList = Arrays.stream(biaoduanno.split(",")).collect(Collectors.toList());
            if (EpointCollectionUtils.isNotEmpty(bdnoList)) {
                gzBuilder.appendIn(" and b.biaoduanno in ( ? )", bdnoList);
            }
        }
        if (StringUtil.isNotBlank(projectno)) {
            List<String> projectList = Arrays.stream(projectno.split(",")).collect(Collectors.toList());
            if (EpointCollectionUtils.isNotEmpty(projectList)) {
                gzBuilder.appendIn(" and b.projectno in ( ? )", projectList);
            }
        }
        // 描述：进行中标候选人公示数据查询
        return ZtbCommonDao.getInstance().findList(gzBuilder.getSql(), Record.class, gzBuilder.getParams());
    }

    protected List<Record> getCGShouBiaoTuiJian(JSONObject requestJsonObject) {
        SqlBuilder fzBuilder = new SqlBuilder();
        fzBuilder.append(
                "select c.SBR_Code, a.awardtype,a.auditstatus,d.companycode,b.projectname,b.projectno,c.jsdwlxr,c.jsdwlxrtel,b.biaoduanguid,b.biaoduanname,b.biaoduanno,b.fabaocontent "
                        + ",b.zhaobiaofangshi,b.isusewebztb,b.touzigusuan,d.extinfo,d.jianshedanwei,d.jianshedanweiguid,b.sbr_name ,b.bjlunci,a.operateusername,a.sbr_code,a.sbr_unitguid,c.ppexecute,d.sbr_code as memberUserCode, c.sbr_unitguid as memberOrgCode"
                        + " ,b.bjfanganguid from CG_ShouBiaoTuiJian a  join cg_biaoduaninfo b  "
                        + " on a.biaoduanguid = b.biaoduanguid  left join cg_chubufabao c on b.fabaoguid = c.fabaoguid "
                        + " left join cg_projectinfo d on b.projectguid =d.projectguid " + " where b.statuscode ='3'  "
                        + " and b.zhaobiaofangshi in ('X','BX','J')");
        // 标段编号,项目编号可以是多个，用逗号分开
        String biaoduanno = requestJsonObject.getString("biaoduanno");
        String projectno = requestJsonObject.getString("projectno");

        if (StringUtil.isNotBlank(biaoduanno)) {
            List<String> bdnoList = Arrays.stream(biaoduanno.split(",")).collect(Collectors.toList());
            if (EpointCollectionUtils.isNotEmpty(bdnoList)) {
                fzBuilder.appendIn(" and b.biaoduanno in ( ? )", bdnoList);
            }
        }
        else if (StringUtil.isNotBlank(projectno)) {
            List<String> projectList = Arrays.stream(projectno.split(",")).collect(Collectors.toList());
            if (EpointCollectionUtils.isNotEmpty(projectList)) {
                fzBuilder.appendIn(" and b.projectno in ( ? )", projectList);
            }
        }
        // 描述：进行推荐授标数据查询
        return ZtbCommonDao.getInstance().findList(fzBuilder.getSql(), Record.class, fzBuilder.getParams());
    }

    /**
     * 公招获取所有的候选单位信息
     *
     * @param biaoduanguid
     *            ：
     * @return ：
     */
    protected List<Record> obtainAllPingbiaoToubiaoInfo(String biaoduanguid) {
        String hxdwSql = "select * from cg_pingbiao_toubiaoinfo where biaoduanguid = ? and ifnull('Isfeibiao','0') !='1' ";
        return ZtbCommonDao.getInstance().findList(hxdwSql, Record.class, biaoduanguid);
    }

    /**
     * 非招按标段授标获取所有的候选单位信息
     *
     * @param biaoduanguid
     *            ：
     * @return ：
     */
    protected List<Record> obtainBiaoDuanShouBiaoInfo(String biaoduanguid) {
        String sql = "select * from cg_pingbiao_toubiaoinfo where biaoduanguid= ? and ifnull('Isfeibiao','0') !='1'  order by isnull(paiming,1000) asc ,row_id asc";
        return ZtbCommonDao.getInstance().findList(sql, Record.class, biaoduanguid);
    }

    /**
     * 非招按行项目授标获取所有的候选单位信息
     *
     * @param biaoduanguid
     *            ：
     * @return ：
     */
    protected List<Record> obtainItemShouBiaoInfo(String biaoduanguid) {
        String sql = "select * from CG_ShouBiaoAndBDItem where biaoduanguid=? ";
        return ZtbCommonDao.getInstance().findList(sql, Record.class, biaoduanguid);
    }

    private WbBidflowLinkconfigs initWorkbenchByLinkguid(String processType, BiaoDuanVO bdvo) {
        if (bdvo == null) {
            logger.info("基类WorkFlowPage:initWorkbenchByLinkguid.BiaoDuanVO是空！");
            return null;
        } else {
            WbBidflowLinkconfigs wbbidflowLinkconfigs = new WbBidflowLinkconfigs();
            wbbidflowLinkconfigs.wblinkguid = bdvo.getBiaoduanguid();
            if (StringUtil.isBlank(wbbidflowLinkconfigs.wblinkguid)) {
                logger.info("基类WorkFlowPage:wbbidflowLinkconfigs.wblinkguid是空！");
                return null;
            } else {
                wbbidflowLinkconfigs.pvi = this.bidflowHandleService.getCurProcessPNIByLinkguid(wbbidflowLinkconfigs.wblinkguid, processType, (BidflowProcessVersion)null);
                if (wbbidflowLinkconfigs.pvi == null) {
                    wbbidflowLinkconfigs.pv = this.bidflowHandleService.getBidflowProcessVersionByBD(processType, bdvo);
                    if (wbbidflowLinkconfigs.pv == null) {
                        logger.info("基类WorkFlowPage:initWorkbenchByLinkguid调用获取wbbidflowLinkconfigs.pv是空！无法找到流程版本！！！请确认此模块有没有配置流程！！！");
                        return null;
                    }

                    wbbidflowLinkconfigs.pvi = this.bidflowHandleService.getCurProcessPNIAndRecovery(wbbidflowLinkconfigs.wblinkguid, processType, wbbidflowLinkconfigs.pv);
                    if (wbbidflowLinkconfigs.pvi == null) {
                        logger.info("基类WorkFlowPage:initWorkbenchByLinkguid调用获取wbbidflowLinkconfigs.pvi是空！无法找到流程实例！！！");
                        return null;
                    }
                } else {
                    wbbidflowLinkconfigs.pv = this.bidflowHandleService.findBidFlowProcessVersion(wbbidflowLinkconfigs.pvi.getPvguid());
                    if (wbbidflowLinkconfigs.pv == null) {
                        logger.info("基类WorkFlowPage:initWorkbenchByLinkguid调用findBidFlowProcessVersion获取wbbidflowLinkconfigs.pv是空！无法找到流程版本！！！请确认此模块有没有配置流程！！！");
                        return null;
                    }
                }

                wbbidflowLinkconfigs.wbpvguid = wbbidflowLinkconfigs.pv.getProcessversionguid();
                wbbidflowLinkconfigs.wbpviguid = wbbidflowLinkconfigs.pvi.getPviguid();
                String taskcodereal;
                if (wbbidflowLinkconfigs.menu == null) {
                    taskcodereal = "TuiJianChengJiao_FZ";
                    if (StringUtil.isNotBlank(taskcodereal)) {
                        wbbidflowLinkconfigs.menu = this.bidflowHandleService.findBidFlowMenu(taskcodereal);
                        if (wbbidflowLinkconfigs.menu != null && !wbbidflowLinkconfigs.menu.getProcessversionguid().equals(wbbidflowLinkconfigs.pv.getProcessversionguid())) {
                            wbbidflowLinkconfigs.menu = null;
                        }
                    }
                }

                if (wbbidflowLinkconfigs.menu != null) {
                    wbbidflowLinkconfigs.module = this.bidflowHandleService.findBidFlowModule(wbbidflowLinkconfigs.menu.getModuleguid());
                }

                if (wbbidflowLinkconfigs.menu == null) {
                    logger.info("基类WorkFlowPage:initWorkbenchByLinkguid无法获取菜单menu,请检查模板配置中的taskcode和子系统类别，与页面的XXXXContext中头部注解的TaskCode、SystemClass是否一致！");
                }

                if (wbbidflowLinkconfigs.menu != null && !wbbidflowLinkconfigs.menu.getProcessversionguid().equals(wbbidflowLinkconfigs.pv.getProcessversionguid())) {
                    logger.info("基类WorkFlowPage:initWorkbenchByLinkguid菜单版本和流程版本不一致，请检查配置！");
                }
                return wbbidflowLinkconfigs;
            }
        }
    }


    public String push_noticeinfo_cnbm(String aggrbd,String aggrstep,String htguid,String aggrlc) throws Exception {
        logger.info("调用推送消息接口=====================================" + aggrbd+";"+ aggrstep+";"+ htguid+";"+ aggrlc+";");
        //入参为biaoduanguid。
        //先配置代码项,对于对应标段所属招标企业然后进行推送。如果没有配置则不推送三方接口。
        try {
            String appkey = "";
            String appsecret = "";
            IFrameConfigService frameConfigService = ContainerFactory.getContainInfo()
                    .getComponent(IFrameConfigService.class);
            String bdguid = aggrbd ;
            if (StringUtil.isBlank(bdguid)) {
                logger.info("调用推送三方接口异常");
            } else {
                String url = "";
                JSONObject jsonObject = new JSONObject();
                BiaoDuanVO biaoDuanVO = biaoduanApi.findByBiaoDuanGuid(bdguid);
                if (StringUtil.isNotBlank(biaoDuanVO.getJianshedanweiguid())) {
                    //调整推送可配置处理，仿照最开始的宁夏推送
                    FrameOu ouinfo = iFrameOuService.getFrameOu(biaoDuanVO.getJianshedanweiguid());
                    if (ouinfo == null) {
                        logger.info("推送三方数据时采购单位计算错误标段标志:" + biaoDuanVO.getBiaoduanguid());
                        return "";
                    }
                    List<CodeItems> listCodeItems = frameCodeItemsService.listCodeItemsByCodeName("CnbmPushNotice");
                    CodeItems codeItems = null ;
                    for (CodeItems items: listCodeItems) {
                        String ouname = ouinfo.getOuname();
                        String allouname = ouinfo.getOucodeLevel();
                        // 兼容新逻辑，中标消息推送url有值也是要推的
                        if (ouname.contains(items.getItemValue()) || allouname.contains(items.getItemValue())) {
                            if(ouname.contains("南京玻璃纤维") || allouname.contains("南京玻璃纤维")
                                    || ouname.contains("中材锂膜") || allouname.contains("中材锂膜")){
                                if(items.getDmAbr2().contains(aggrstep + "@" + aggrlc)){
                                    codeItems = items;
                                    break ;
                                }
                            }
                            else{
                                codeItems = items;
                                break ;
                            }
                        }
                    }

                    logger.info("codeItems:"+codeItems);

                    //配置增加
                    if(!codeItems.getDmAbr2().contains(aggrstep)){
                        //如果配置中包括改环节则给推送消息，否则直接返回
                        logger.info("环节推送不包含:" + biaoDuanVO.getBiaoduanguid() + "@" + aggrstep);
                        return "";
                    }

                    if (codeItems != null) {
                        logger.info("url:"+codeItems.getItemText());
                        url = codeItems.getItemText();
                        if(codeItems.getItemValue().contains("南京玻璃纤维")){
                            appkey = frameConfigService.getFrameConfigValue("njblxwcappkey");
                            appsecret = frameConfigService.getFrameConfigValue("njblxwcppsecret");
                        }
                        else{
                            appkey = frameConfigService.getFrameConfigValue("nxjcappkey");
                            appsecret = frameConfigService.getFrameConfigValue("nxjcappsecret");
                        }
                        if (url.contains("cnbm.com.cn")) {
                            if(codeItems.getDmAbr2().contains(aggrstep + "@" + aggrlc)){
                                //调用对应接口
                                String timeStamp = EpointDateUtil.convertDate2String(new Date(), "yyyyMMddHHmmss");
                                String strApi = url.replace("http://", "").replace("https://", "");
                                String uri = strApi.substring(strApi.indexOf('/'));
                                //"/open-api/innerRequest/NBYY120/WBYY22427/WBJK224017/1";
                                //从接口平台中的应用详情中获取，该值为应用的身份密钥，注意保密
                                String secret = codeItems.getDmAbr1();
                                //按照固定顺序拼接
                                String rowSign = timeStamp + uri + secret;
                                //生成Md5摘要签名sign
                                MessageDigest md = null;
                                try {
                                    md = MessageDigest.getInstance("MD5");
                                } catch (NoSuchAlgorithmException e) {
                                    e.printStackTrace();
                                }
                                byte[] digest = md.digest(rowSign.getBytes(StandardCharsets.UTF_8));
                                StringBuilder hs = new StringBuilder();
                                String stmp = "";
                                for (byte b : digest) {
                                    stmp = (Integer.toHexString(b & 0XFF));
                                    if (stmp.length() == 1)
                                        hs.append("0").append(stmp);
                                    else
                                        hs.append(stmp);
                                }
                                logger.info("sign值计算结果" + hs.toString().toUpperCase());

                                Map<String, String> map = new HashMap<>();
                                map.put("sign", hs.toString().toUpperCase());
                                map.put("timeStamp", timeStamp);
                                jsonObject.put("token", createToken(appkey,appsecret));
                                jsonObject.put("projectname", biaoDuanVO.getProjectname());
                                jsonObject.put("projectno", biaoDuanVO.getProjectno());
                                jsonObject.put("bidsectionname", biaoDuanVO.getBiaoduanname());
                                jsonObject.put("bidsectionno", biaoDuanVO.getBiaoduanno());
                                //消息通知2结果信息
                                jsonObject.put("tradestep", aggrstep);
                                //增加来源平台名称，以单位名作为划分
                                jsonObject.put("sysname",codeItems.getItemValue());
                                jsonObject.put("creditcode",ouinfo.getStr("creditcode"));
                                if("3".equals(aggrstep)){
                                    jsonObject.put("htguid", htguid);
                                    jsonObject.put("htstatus", "1");
                                }
                                logger.info("推送信息入参：" + jsonObject);
                                logger.info("调用结果：" + HttpClientUtil.postBody(url,
                                        map, jsonObject.toString()));
                            }
                            else{
                                logger.info("流程环节推送不包含:" + biaoDuanVO.getBiaoduanguid() + "@" + aggrstep+ "@" + aggrlc);
                            }
                        }
                    } else {
                        logger.info("未配置采购单位CnbmPushNotice的代码项目");
                    }
                } else {
                    logger.info("调用推送三方接口异常项目采购单位推送接口未配置,采购单位标志" + biaoDuanVO.getJianshedanweiguid() + "@标段标志" + bdguid);
                }
            }
        } catch (Exception ex) {
            logger.info("调用推送三方接口异常" + ex);
        }
        return "";
    }


    public String createToken(String appkey,String appsecret) {
        String token = "";
        try {
            String p1 = URLEncoder.encode(String.valueOf((System.currentTimeMillis()/1000L + 300L)),"UTF-8");

            BASE64Encoder base64Encoder = new BASE64Encoder();
            String p2 =  base64Encoder.encode(p1.getBytes());
            SecretKeySpec signingKey = new SecretKeySpec(appsecret.getBytes("UTF-8"), "HmacSHA1");
            Mac mac = Mac.getInstance("HmacSHA1");
            mac.init(signingKey);
            byte[] P3= mac.doFinal(p2.getBytes("UTF-8"));
            String P4 = Base64Util.encode(P3).replace("+", "-").replace("/", "_");
            token = appkey + "@" + P4 + "@" + p2;

        } catch (Exception e) {
            e.printStackTrace();
        }

        return token;
    }

}
