#!/usr/bin/env python3
"""
增强版 Python 环境验证 Demo
自动运行各种功能测试，无需用户交互
"""

import sys
import os
import json
import datetime
import math
import random
import platform
import subprocess


def print_header(title):
    """打印标题"""
    print("\n" + "=" * 60)
    print(f"  {title}")
    print("=" * 60)


def test_python_environment():
    """测试Python环境"""
    print_header("Python 环境信息")
    
    print(f"Python 版本: {sys.version}")
    print(f"Python 版本信息: {sys.version_info}")
    print(f"Python 可执行文件: {sys.executable}")
    print(f"Python 路径: {sys.path[0]}")
    print(f"当前工作目录: {os.getcwd()}")
    print(f"操作系统: {platform.system()} {platform.release()}")
    print(f"处理器架构: {platform.machine()}")
    print(f"主机名: {platform.node()}")


def test_pip_packages():
    """测试pip和已安装包"""
    print_header("包管理器信息")
    
    try:
        # 获取pip版本
        result = subprocess.run([sys.executable, '-m', 'pip', '--version'], 
                              capture_output=True, text=True)
        print(f"pip 版本: {result.stdout.strip()}")
        
        # 获取已安装包列表
        result = subprocess.run([sys.executable, '-m', 'pip', 'list'], 
                              capture_output=True, text=True)
        lines = result.stdout.strip().split('\n')
        print(f"已安装包数量: {len(lines) - 2}")  # 减去标题行
        print("前10个已安装包:")
        for line in lines[2:12]:  # 跳过标题行，显示前10个
            print(f"  {line}")
            
    except Exception as e:
        print(f"获取包信息时出错: {e}")


def test_basic_operations():
    """测试基本操作"""
    print_header("基本功能测试")
    
    # 数学运算
    print("数学运算测试:")
    a, b = 15, 4
    print(f"  {a} + {b} = {a + b}")
    print(f"  {a} - {b} = {a - b}")
    print(f"  {a} * {b} = {a * b}")
    print(f"  {a} / {b} = {a / b:.2f}")
    print(f"  {a} ** {b} = {a ** b}")
    print(f"  sqrt({a}) = {math.sqrt(a):.2f}")
    print(f"  sin(π/2) = {math.sin(math.pi/2):.2f}")
    
    # 字符串操作
    print("\n字符串操作测试:")
    text = "Hello, Python 3.9!"
    print(f"  原文: {text}")
    print(f"  大写: {text.upper()}")
    print(f"  小写: {text.lower()}")
    print(f"  长度: {len(text)}")
    print(f"  替换: {text.replace('Python', 'World')}")
    
    # 列表操作
    print("\n列表操作测试:")
    numbers = [1, 3, 5, 7, 9, 2, 4, 6, 8, 10]
    print(f"  原列表: {numbers}")
    print(f"  排序后: {sorted(numbers)}")
    print(f"  求和: {sum(numbers)}")
    print(f"  最大值: {max(numbers)}")
    print(f"  最小值: {min(numbers)}")
    print(f"  平均值: {sum(numbers)/len(numbers):.2f}")


def test_data_structures():
    """测试数据结构"""
    print_header("数据结构测试")
    
    # 字典测试
    print("字典操作测试:")
    student = {
        "name": "张三",
        "age": 20,
        "major": "计算机科学",
        "grades": [85, 92, 78, 96, 88]
    }
    print(f"  学生信息: {json.dumps(student, ensure_ascii=False, indent=2)}")
    print(f"  平均成绩: {sum(student['grades'])/len(student['grades']):.1f}")
    
    # 集合测试
    print("\n集合操作测试:")
    set1 = {1, 2, 3, 4, 5}
    set2 = {4, 5, 6, 7, 8}
    print(f"  集合1: {set1}")
    print(f"  集合2: {set2}")
    print(f"  并集: {set1 | set2}")
    print(f"  交集: {set1 & set2}")
    print(f"  差集: {set1 - set2}")
    
    # 元组测试
    print("\n元组操作测试:")
    coordinates = (10, 20, 30)
    print(f"  坐标: {coordinates}")
    print(f"  X坐标: {coordinates[0]}")
    print(f"  元组长度: {len(coordinates)}")


def test_file_operations():
    """测试文件操作"""
    print_header("文件操作测试")
    
    test_file = "test_environment.txt"
    test_data = [
        "Python 环境测试文件",
        f"创建时间: {datetime.datetime.now()}",
        "包含中文字符测试",
        "Special characters: !@#$%^&*()",
        "Numbers: 1234567890",
        "测试完成"
    ]
    
    try:
        # 写入文件
        with open(test_file, 'w', encoding='utf-8') as f:
            for line in test_data:
                f.write(line + '\n')
        print(f"✓ 成功创建文件: {test_file}")
        
        # 读取文件
        with open(test_file, 'r', encoding='utf-8') as f:
            content = f.read()
        print(f"✓ 成功读取文件，内容长度: {len(content)} 字符")
        
        # 文件信息
        file_size = os.path.getsize(test_file)
        print(f"✓ 文件大小: {file_size} 字节")
        
        # 删除文件
        os.remove(test_file)
        print(f"✓ 成功删除文件: {test_file}")
        
    except Exception as e:
        print(f"✗ 文件操作失败: {e}")


def test_datetime_operations():
    """测试日期时间操作"""
    print_header("日期时间操作测试")
    
    now = datetime.datetime.now()
    print(f"当前时间: {now}")
    print(f"格式化时间: {now.strftime('%Y年%m月%d日 %H:%M:%S')}")
    print(f"ISO格式: {now.isoformat()}")
    print(f"时间戳: {now.timestamp()}")
    
    # 时间计算
    tomorrow = now + datetime.timedelta(days=1)
    last_week = now - datetime.timedelta(weeks=1)
    print(f"明天: {tomorrow.strftime('%Y-%m-%d')}")
    print(f"一周前: {last_week.strftime('%Y-%m-%d')}")
    
    # 时区信息
    print(f"星期几: {now.strftime('%A')}")
    print(f"今年第几天: {now.timetuple().tm_yday}")


def test_random_operations():
    """测试随机数操作"""
    print_header("随机数操作测试")
    
    # 设置种子确保可重复性
    random.seed(42)
    
    print("随机数生成测试:")
    print(f"  随机整数(1-100): {random.randint(1, 100)}")
    print(f"  随机浮点数(0-1): {random.random():.4f}")
    print(f"  随机浮点数(1-10): {random.uniform(1, 10):.2f}")
    
    # 随机选择
    colors = ['红色', '蓝色', '绿色', '黄色', '紫色']
    print(f"  随机颜色: {random.choice(colors)}")
    print(f"  随机抽取3种颜色: {random.sample(colors, 3)}")
    
    # 随机打乱
    numbers = list(range(1, 11))
    original = numbers.copy()
    random.shuffle(numbers)
    print(f"  原始列表: {original}")
    print(f"  打乱后: {numbers}")


def test_error_handling():
    """测试错误处理"""
    print_header("错误处理测试")
    
    error_tests = [
        ("除零错误", lambda: 1/0),
        ("类型错误", lambda: "字符串" + 123),
        ("索引错误", lambda: [1,2,3][10]),
        ("键错误", lambda: {"a": 1}["b"]),
        ("属性错误", lambda: "字符串".不存在的方法())
    ]
    
    for test_name, test_func in error_tests:
        try:
            test_func()
            print(f"✗ {test_name}: 未捕获到预期错误")
        except Exception as e:
            print(f"✓ {test_name}: 成功捕获 {type(e).__name__}")


def run_performance_test():
    """运行性能测试"""
    print_header("性能测试")
    
    import time
    
    # 列表推导式性能测试
    start_time = time.time()
    squares = [x**2 for x in range(10000)]
    end_time = time.time()
    print(f"列表推导式(10000个平方数): {(end_time - start_time)*1000:.2f} 毫秒")
    
    # 字符串连接性能测试
    start_time = time.time()
    result = "".join([str(i) for i in range(1000)])
    end_time = time.time()
    print(f"字符串连接(1000个数字): {(end_time - start_time)*1000:.2f} 毫秒")
    
    # 排序性能测试
    test_list = [random.randint(1, 1000) for _ in range(5000)]
    start_time = time.time()
    sorted_list = sorted(test_list)
    end_time = time.time()
    print(f"排序(5000个随机数): {(end_time - start_time)*1000:.2f} 毫秒")


def main():
    """主函数"""
    print("🐍 Python 环境全面验证开始")
    print(f"测试时间: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 运行所有测试
    test_python_environment()
    test_pip_packages()
    test_basic_operations()
    test_data_structures()
    test_file_operations()
    test_datetime_operations()
    test_random_operations()
    test_error_handling()
    run_performance_test()
    
    print_header("测试完成")
    print("🎉 所有测试完成！Python 环境运行正常！")
    print("✓ 基本功能正常")
    print("✓ 数据结构操作正常")
    print("✓ 文件操作正常")
    print("✓ 错误处理正常")
    print("✓ 性能表现良好")


if __name__ == "__main__":
    main()
