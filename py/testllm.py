#!/usr/bin/env python
# -*- coding: utf-8 -*-

import requests
import json

# 纯大模型流式测试
def test_llmstream():
   
    url = "http://**************:8080/EpointFrame/rest/dynamicapi/danyuanceshi2"#千问
  
        
    usercontent = "请帮我生成杭州旅游攻略"
    headers = {'Authorization': 'bearer sk-6c3506eef3a97301a41d1c91af0ae647'}
    params = {"usercontent":usercontent}

    try:
        print("🚀 正在连接千问模型...")
        print(f"📡 服务地址: {url}")
        print(f"💬 提示词: {usercontent}")
        print("=" * 50)
        
        response = requests.request("POST", url, data=params, stream=True,headers=headers,  timeout=30)
        
        if response.status_code != 200:
            print(f"❌ 请求失败，状态码: {response.status_code}")
            print(f"错误信息: {response.text}")
            return
        
        print("🤖 AI回复: ", end="", flush=True)
        
        for chunk in response.iter_lines(): 
            if chunk:  # 确保chunk不为空
                data = chunk.decode("utf-8", "ignore")
                try:
                    # print(f"DEBUG: {data}")  # 调试用，取消注释查看原始数据
                    ana = eval(data)["result"]["content"]
                    print(ana, end="", flush=True)
                except Exception as e:
                    # 如果不是有效的数据块，忽略
                    if data.strip() and not data.startswith("data:"):
                        print(f"\n⚠️ 解析异常: {data}")
        
        print("\n" + "=" * 50)
        print("✅ 测试完成")
        
    except requests.exceptions.ConnectionError:
        print("❌ 连接失败！请检查：")
        print("   1. 服务地址是否正确")
        print("   2. 服务是否正在运行")
        print("   3. 网络连接是否正常")
    except requests.exceptions.Timeout:
        print("❌ 请求超时！请检查网络连接")
    except Exception as e:
        print(f"❌ 未知错误: {str(e)}")

def test_custom_prompt(prompt="你好，请介绍一下自己"):
    """测试自定义提示词"""
    url = "http://**************:8080/EpointFrame/rest/dynamicapi/danyuanceshi2"
    
    params = {"usercontent":prompt}
    headers = {'Authorization': 'bearer sk-6c3506eef3a97301a41d1c91af0ae647'}

    print(f"🤖 正在生成回复...\n")
    print(f"📝 用户: {prompt}\n")
    print(f"🤖 AI: ", end="", flush=True)

    try:
        response = requests.request("POST", url, data=params, stream=True, headers=headers, timeout=30)
        
        if response.status_code != 200:
            print(f"❌ 请求失败: {response.status_code}")
            return
        
        for chunk in response.iter_lines(): 
            if chunk:
                data = chunk.decode("utf-8", "ignore")
                try:
                    ana = eval(data)["result"]["content"]
                    print(ana, end="", flush=True)
                except Exception as e:
                    pass
                    
    except Exception as e:
        print(f"❌ 请求失败: {str(e)}")
    
    print("\n" + "="*50)

def interactive_chat():
    """交互式聊天模式"""
    url = "http://**************:8080/EpointFrame/rest/dynamicapi/danyuanceshi2"
    headers = {'Authorization': 'bearer sk-6c3506eef3a97301a41d1c91af0ae647'}
    
    print("👋 欢迎使用千问2.5-14B流式聊天！")
    print("👋 输入 'quit' 或 'exit' 退出聊天")
    print("=" * 50)
    
    while True:
        user_input = input("\n👤 你: ").strip()
        
        if user_input.lower() in ['quit', 'exit', '退出']:
            print("👋 再见！")
            break
            
        if not user_input:
            continue
            
        params = {"usercontent":user_input}
        

        print("🤖 AI: ", end="", flush=True)
        
        try:
            response = requests.request("POST", url, data=params, stream=True, headers=headers, timeout=30)
            
            if response.status_code != 200:
                print(f"❌ 请求失败: {response.status_code}")
                continue
            
            for chunk in response.iter_lines(): 
                if chunk:
                    data = chunk.decode("utf-8", "ignore")
                    try:
                        ana = eval(data)["result"]["content"]
                        print(ana, end="", flush=True)
                    except Exception as e:
                        pass
                        
        except Exception as e:
            print(f"❌ 请求失败: {str(e)}")
        
        print()  # 换行

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1:
        if sys.argv[1] == "--interactive":
            interactive_chat()
        elif sys.argv[1] == "--custom":
            prompt = " ".join(sys.argv[2:]) if len(sys.argv) > 2 else "你好，请介绍一下自己"
            test_custom_prompt(prompt)
        else:
            test_custom_prompt(" ".join(sys.argv[1:]))
    else:
        # 默认运行原始测试
        test_llmstream()