# -*- coding: utf-8 -*-

import requests
import json
import re
from pprint import pprint
import time

# 应用名称：交易-招标文件生成
def test_llmstream():
    #生成精简目录V2.0
    url = "http://172.29.255.220:8080/EpointFrame/rest/dynamicapi/sheng_cheng_mu_lu_easy"

    params = {
        "bid_requirement": "①项目概况：本项目投资估算 5865.2 万元，工程概算4969.5646万元，其中建安工程造价 4969.5646 万元，建设规模：主要对绍兴剧院进行改造，包括对剧院内部部分功能调整、拆除、新建、室内装修、外立面改造、安装改造、辅房改造、场地改造以及相关设施设备安装等，涉及总建筑面积3415.85平米，其中地上建筑建筑面积约3239.97平米，地下建筑面积约175.88平方，建设地点：越城区。\n②项目招标范围：主要包括基坑支护、地下室、主体、幕墙、室内精装修、安装、场外等工程（具体详见招标控制价及施工图纸），其中配电设备由甲方单独招标或自行采购，不列入本次招标范围。具体范围及内容详见精装修施工图纸及审定后工程量清单。本次招标建安工程造价：约4969.5646万元（以审定后标底价为准）。",
        "project_type": "1",
        "provided_outline": "一、总体施工部署；\n二、难点、重点分析；\n三、主要施工方案；\n四、工程质量保证措施；\n五、施工计划和保证措施；\n六、安全生产、文明施工、环境保护措施；\n七、管理人员配备；\n八、施工设备配备；\n九、其他。",
        "score_standard": "1总体施工部署\n1.01工程概况和承包范围\n1.02施工目标\n1.03施工现场总平面布置\n1.04临时用地、用电、用水布置\n1.05专业工程分包\n2主要施工方案\n2.01★主要分部分项工程施工方法\n2.02★招标人特殊要求的施工方法\n3工程质量保证措施\n3.01★关键工序、复杂环节及预防主要质量通病技术措施\n3.02★保证施工质量的管理措施及执行的质量标准\n4施工进度计划和保证措施\n4.01施工总进度计划\n4.02主要构配件及周转材料计划\n4.03各阶段劳动力配备计划\n4.04保证进度计划的管理与技术措施\n5安全生产、文明施工、环境保护措施\n5.01危大工程清单\n5.02★安全生产措施\n5.03文明施工措施\n5.04环境保护、绿色施工措施\n6管理人员配备\n6.01★主要管理人员配备及岗位设置\n7施工设备配备\n7.01★主要施工机械设备配备计划\n7.02测量、试验、检验设备器具配置计划"
    }


    headers = {"Authorization":"Bearer sk-a826cfa53afd4a5ea90ba20a7ed344b2","Content-Type":"application/json"}

    try:
        response = requests.request("POST", url, data=json.dumps(params), stream=True, headers=headers, timeout=30)

        if response.status_code != 200:
            print(f"请求失败，状态码: {response.status_code}")
            print(f"响应内容: {response.text}")
            return

        for chunk in response.iter_lines():
            data = chunk.decode("utf-8", "ignore")
            if data == "":
                continue

            if data.startswith("data:"):
                data = data[5:]
            data = data.replace("null", "\"\"")

            try:
                dt = json.loads(data)
                if "result" in dt and "content" in dt["result"]:
                    print(dt["result"]["content"], end="", flush=True)
            except json.JSONDecodeError as e:
                if data.strip().startswith("<!DOCTYPE html>"):
                    print(f"\n[ERROR] 服务器返回HTML错误页面")
                    break
            except Exception as e:
                pass  # 忽略其他异常

    except requests.exceptions.RequestException as e:
        print(f"[ERROR] 网络请求失败: {e}")
    except Exception as e:
        print(f"[ERROR] 未知错误: {e}")


if __name__ == '__main__':
    test_llmstream()
