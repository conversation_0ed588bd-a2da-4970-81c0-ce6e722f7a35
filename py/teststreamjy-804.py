# -*- coding: utf-8 -*-

import requests
import json
import re
from pprint import pprint
import time

# 应用名称：交易-招标文件生成
def test_llmstream():
    #生成精简目录V2.0

    url = "http://172.29.255.220:8080/EpointFrame/rest/dynamicapi/select_text_func"

    params = {
        "user_requirement": "示例_user_requirement_值",
        "func_type": 4,
        "text": "示例_text_值"
    }

    headers = {'Authorization': 'Bearer sk-a826cfa53afd4a5ea90ba20a7ed344b2','Content-Type':'application/json'}

    response = requests.request("POST", url, data=json.dumps(params), headers=headers)


    for chunk in response.iter_lines():
        data = chunk.decode("utf-8", "ignore")
        if data =="":
            continue
        if data.startswith("data:"):
            data = data[5:]
        data = data.replace("null","\"\"")
        try:
            dt = json.loads(data)
            print(dt["result"]["content"], end="",flush=True)
        except Exception as e:
            print(e,data)

if __name__ == '__main__':
    test_llmstream()
