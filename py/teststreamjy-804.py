# -*- coding: utf-8 -*-

import requests
import json
import re
from pprint import pprint
import time

# 应用名称：交易-招标文件生成
def test_llmstream():
    #生成精简目录V2.0

    url = "http://172.29.255.220:8080/EpointFrame/rest/dynamicapi/select_text_func"

    params = {
        "user_requirement": "示例_user_requirement_值",
        "func_type": 4,
        "text": "示例_text_值"
    }

    headers = {'Authorization': 'Bearer sk-a826cfa53afd4a5ea90ba20a7ed344b2','Content-Type':'application/json'}

    try:
        print(f"正在请求: {url}")
        print("发送请求中...")

        # 发送POST请求，普通响应模式
        response = requests.post(url, json=params, headers=headers, timeout=60)

        print(f"响应状态码: {response.status_code}")
        response.raise_for_status()  # 检查HTTP错误

        print("请求成功，处理响应数据...")

        # 获取响应内容
        response_text = response.text
        print(f"响应内容长度: {len(response_text)} 字符")

        try:
            # 尝试解析JSON响应
            response_data = response.json()
            print("JSON解析成功")

            # 美化打印响应数据
            print("\n=== 完整响应数据 ===")
            pprint(response_data)

            # 如果有特定的结果字段，单独显示
            if "result" in response_data:
                print("\n=== 结果内容 ===")
                result = response_data["result"]
                if isinstance(result, dict) and "content" in result:
                    print(result["content"])
                else:
                    pprint(result)
            elif "data" in response_data:
                print("\n=== 数据内容 ===")
                pprint(response_data["data"])

        except json.JSONDecodeError as e:
            print(f"JSON解析失败: {e}")
            print("\n=== 原始响应内容 ===")
            print(response_text)

        print("\n请求处理完成。")

    except requests.exceptions.HTTPError as e:
        print(f"[ERROR] HTTP错误: {e}")
        if hasattr(e, 'response') and e.response is not None:
            print(f"[ERROR] 响应状态码: {e.response.status_code}")
            print(f"[ERROR] 响应内容: {e.response.text}")
    except requests.exceptions.ConnectionError as e:
        print(f"[ERROR] 网络连接失败: {e}")
    except requests.exceptions.Timeout as e:
        print(f"[ERROR] 请求超时: {e}")
    except requests.exceptions.RequestException as e:
        print(f"[ERROR] 网络请求失败: {e}")
    except Exception as e:
        print(f"[ERROR] 未知错误: {e}")

if __name__ == '__main__':
    test_llmstream()
