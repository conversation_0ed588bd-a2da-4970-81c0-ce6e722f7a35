# -*- coding: utf-8 -*-

import requests
import json
import re
from pprint import pprint
import time

# 应用名称：交易-招标文件生成
def test_llmstream():
    #生成精简目录V2.0

    url = "http://172.29.255.220:8080/EpointFrame/rest/dynamicapi/sheng_cheng_mu_lu_easy"

    params = {
        "bid_requirement": "①项目概况：本项目投资估算 5865.2 万元，工程概算4969.5646万元，其中建安工程造价 4969.5646 万元，建设规模：主要对绍兴剧院进行改造，包括对剧院内部部分功能调整、拆除、新建、室内装修、外立面改造、安装改造、辅房改造、场地改造以及相关设施设备安装等，涉及总建筑面积3415.85平米，其中地上建筑建筑面积约3239.97平米，地下建筑面积约175.88平方，建设地点：越城区。\n②项目招标范围：主要包括基坑支护、地下室、主体、幕墙、室内精装修、安装、场外等工程（具体详见招标控制价及施工图纸），其中配电设备由甲方单独招标或自行采购，不列入本次招标范围。具体范围及内容详见精装修施工图纸及审定后工程量清单。本次招标建安工程造价：约4969.5646万元（以审定后标底价为准）。",
        "project_type": "1",
        "provided_outline": "一、总体施工部署；\n二、难点、重点分析；\n三、主要施工方案；\n四、工程质量保证措施；\n五、施工计划和保证措施；\n六、安全生产、文明施工、环境保护措施；\n七、管理人员配备；\n八、施工设备配备；\n九、其他。",
        "score_standard": "1总体施工部署\n1.01工程概况和承包范围\n1.02施工目标\n1.03施工现场总平面布置\n1.04临时用地、用电、用水布置\n1.05专业工程分包\n2主要施工方案\n2.01★主要分部分项工程施工方法\n2.02★招标人特殊要求的施工方法\n3工程质量保证措施\n3.01★关键工序、复杂环节及预防主要质量通病技术措施\n3.02★保证施工质量的管理措施及执行的质量标准\n4施工进度计划和保证措施\n4.01施工总进度计划\n4.02主要构配件及周转材料计划\n4.03各阶段劳动力配备计划\n4.04保证进度计划的管理与技术措施\n5安全生产、文明施工、环境保护措施\n5.01危大工程清单\n5.02★安全生产措施\n5.03文明施工措施\n5.04环境保护、绿色施工措施\n6管理人员配备\n6.01★主要管理人员配备及岗位设置\n7施工设备配备\n7.01★主要施工机械设备配备计划\n7.02测量、试验、检验设备器具配置计划"
    }

    # 尝试不同的认证方式
    headers_options = [
        {"Authorization":"Bearer sk-a826cfa53afd4a5ea90ba20a7ed344b2","Content-Type":"application/json"},
        {"Authorization":"sk-a826cfa53afd4a5ea90ba20a7ed344b2","Content-Type":"application/json"},
        {"token":"sk-a826cfa53afd4a5ea90ba20a7ed344b2","Content-Type":"application/json"},
        {"X-API-Key":"sk-a826cfa53afd4a5ea90ba20a7ed344b2","Content-Type":"application/json"}
    ]

    for i, headers in enumerate(headers_options):
        print(f"\n=== 尝试认证方式 {i+1} ===")
        print(f"Headers: {headers}")

        try:
            print(f"正在请求: {url}")
            print("发送请求中...")

            # 发送POST请求，普通响应模式
            response = requests.post(url, json=params, headers=headers, timeout=60)

            print(f"响应状态码: {response.status_code}")
            response.raise_for_status()  # 检查HTTP错误

            print("请求成功，处理响应数据...")

            # 获取响应内容
            response_text = response.text
            print(f"响应内容长度: {len(response_text)} 字符")

            try:
                # 尝试解析JSON响应
                response_data = response.json()
                print("JSON解析成功")

                # 美化打印响应数据
                print("\n=== 完整响应数据 ===")
                pprint(response_data)

                # 如果有特定的结果字段，单独显示
                if "result" in response_data:
                    print("\n=== 结果内容 ===")
                    result = response_data["result"]
                    if isinstance(result, dict) and "content" in result:
                        print(result["content"])
                    else:
                        pprint(result)
                elif "data" in response_data:
                    print("\n=== 数据内容 ===")
                    pprint(response_data["data"])

            except json.JSONDecodeError as e:
                print(f"JSON解析失败: {e}")
                print("\n=== 原始响应内容 ===")
                print(response_text)

            print("\n请求处理完成。")
            break  # 成功了就退出循环

        except requests.exceptions.HTTPError as e:
            print(f"[ERROR] HTTP错误: {e}")
            if hasattr(e, 'response') and e.response is not None:
                print(f"[ERROR] 响应状态码: {e.response.status_code}")
                print(f"[ERROR] 响应内容: {e.response.text}")
                if e.response.status_code != 403:
                    break  # 如果不是403错误，就不再尝试其他认证方式
        except requests.exceptions.ConnectionError as e:
            print(f"[ERROR] 网络连接失败: {e}")
            break  # 连接错误不需要尝试其他认证方式
        except requests.exceptions.Timeout as e:
            print(f"[ERROR] 请求超时: {e}")
            break  # 超时错误不需要尝试其他认证方式
        except requests.exceptions.RequestException as e:
            print(f"[ERROR] 网络请求失败: {e}")
            break
        except Exception as e:
            print(f"[ERROR] 未知错误: {e}")
            break

if __name__ == '__main__':
    test_llmstream()
