# -*- coding: utf-8 -*-

import requests
import json
import re
from pprint import pprint
import time

# 纯大模型流式测试
def test_llmstream():
    # url = "http://127.0.0.1:8080/EpointFrame/rest/dynamicapi/liushichuli"#千问
    url = "http://172.29.255.220:8080/EpointFrame/rest/dynamicapi/liushichuli"#千问

    params = {

        "text":"根据本工程的施工内容，主要噪音源包括：施工机械噪音：如挖掘机、装载机、混凝土搅拌机、振捣棒等设备运行时产生的噪音。模板工程噪音：模板的装卸、安装、拆除过程中产生的撞击声。钢筋加工与安装噪音：钢筋切割、弯曲、焊接等操作产生的噪音。脚手架工程噪音：脚手架的搭设与拆除过程中产生的金属碰撞声。混凝土浇筑与振捣噪音：混凝土浇筑时振捣棒的高频振动噪音。运输车辆噪音：运输建筑材料的车辆进出施工现场产生的噪音。"
    }
    headers = {'Authorization': 'Bearer sk-82c64ce73cd3462185352e293e033e0a','Content-Type':'application/json'}

    response = requests.request("POST", url, data=json.dumps(params), stream=True,headers=headers)

    # print(response.text)
    
    for chunk in response.iter_lines(): 
        data = chunk.decode("utf-8", "ignore")
        if data =="":
            continue
        if data.startswith("data:"):
            data = data[5:]
        data = data.replace("null","\"\"")
        try:
            dt = json.loads(data)
            print(dt["result"]["content"], end="",flush=True)
        except Exception as e:
            print(e,data)
            

if __name__ == '__main__':
    test_llmstream()
