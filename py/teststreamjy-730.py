# -*- coding: utf-8 -*-

import requests
import json
import re
from pprint import pprint
import time

# 纯大模型流式测试
def test_llmstream():
    # url = "http://127.0.0.1:8080/EpointFrame/rest/dynamicapi/liushichuli"#千问http://127.0.0.1:8080/EpointFrame/rest/dynamicapi/liushichuli
    url = "http://ai.ebpu.com/EbpuRobotUATTest/rest/dynamicapi/liushichuli"#千问

    params = {
        "stream": True,
        "text":"根据本工程的施工内容，主要噪音源包括：施工机械噪音：如挖掘机、装载机、混凝土搅拌机、振捣棒等设备运行时产生的噪音。模板工程噪音：模板的装卸、安装、拆除过程中产生的撞击声。钢筋加工与安装噪音：钢筋切割、弯曲、焊接等操作产生的噪音。脚手架工程噪音：脚手架的搭设与拆除过程中产生的金属碰撞声。混凝土浇筑与振捣噪音：混凝土浇筑时振捣棒的高频振动噪音。运输车辆噪音：运输建筑材料的车辆进出施工现场产生的噪音。"
    }
    headers = {"Authorization":"Bearer sk-33a833533f114ec9bd93ea22885d7e86","Content-Type":"application/json"}

    try:
        response = requests.request("POST", url, data=json.dumps(params), stream=True, headers=headers, timeout=30)

        if response.status_code != 200:
            print(f"请求失败，状态码: {response.status_code}")
            print(f"响应内容: {response.text}")
            return

        for chunk in response.iter_lines():
            data = chunk.decode("utf-8", "ignore")
            if data == "":
                continue

            if data.startswith("data:"):
                data = data[5:]
            data = data.replace("null", "\"\"")

            try:
                dt = json.loads(data)
                if "result" in dt and "content" in dt["result"]:
                    print(dt["result"]["content"], end="", flush=True)
            except json.JSONDecodeError as e:
                if data.strip().startswith("<!DOCTYPE html>"):
                    print(f"\n[ERROR] 服务器返回HTML错误页面")
                    break
            except Exception as e:
                pass  # 忽略其他异常

    except requests.exceptions.RequestException as e:
        print(f"[ERROR] 网络请求失败: {e}")
    except Exception as e:
        print(f"[ERROR] 未知错误: {e}")


if __name__ == '__main__':
    test_llmstream()
