import requests
import json
import sys

# 纯大模型流式测试
def test_llmstream():
    # url = "https://oa.epoint.com.cn/oaextend/rest/dynamicapi/epointllm"#千问
    # url = "http://192.168.186.18:8008/vllm/predict_async"#千问
    url = "http://172.29.255.220:8080/EpointFrame//rest/dynamicapi/ceshiliushishuchu"#千问
    # url = "https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions"#千问
        
    messages = [ {
        "role": "user",
        "content": "帮写写一首春天的诗，李白风格"
    }]

    params = {"temperature":0.1,"messages":messages,"stream":True,"model":"qwen2.5-14b-instruct"}
    headers = {'Authorization': 'bearer sk-c26b8237a2f128569bf65e31e37df8f8'}

    try:
        print(f"正在连接到 {url}...")
        response = requests.request("POST", url, data=json.dumps(params), stream=True, headers=headers, timeout=10)
        
        if response.status_code != 200:
            print(f"请求失败，状态码: {response.status_code}")
            print(f"响应内容: {response.text}")
            return
            
        print("连接成功，正在接收数据...")
        
        for chunk in response.iter_lines(): 
            data = chunk.decode("utf-8", "ignore")
            try:
                # print(data)
                ana = eval(data)["result"]["content"]
                print(ana, end="", flush=True)
            except Exception as e:
                print(f"\n解析数据出错: {e}")
                print(f"原始数据: {data}")
    except requests.exceptions.ConnectionError:
        print(f"连接错误: 无法连接到 {url}，请检查网络连接或URL是否正确")
    except requests.exceptions.Timeout:
        print(f"连接超时: 请求超时，服务器没有及时响应")
    except Exception as e:
        print(f"发生错误: {e}")

if __name__ == "__main__":
    test_llmstream()