#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
简单的Python Demo
包含基本功能演示
"""

import sys
import os
import time
from datetime import datetime

def print_system_info():
    """打印系统信息"""
    print("=" * 50)
    print("Python Demo - 系统信息")
    print("=" * 50)
    print(f"Python版本: {sys.version}")
    print(f"Python路径: {sys.executable}")
    print(f"当前时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"当前工作目录: {os.getcwd()}")
    print(f"操作系统: {os.name}")
    print("=" * 50)

def simple_calculator():
    """简单计算器演示"""
    print("\n简单计算器演示:")
    print("-" * 30)
    
    try:
        a = float(input("请输入第一个数字: "))
        b = float(input("请输入第二个数字: "))
        
        print(f"\n计算结果:")
        print(f"{a} + {b} = {a + b}")
        print(f"{a} - {b} = {a - b}")
        print(f"{a} * {b} = {a * b}")
        if b != 0:
            print(f"{a} / {b} = {a / b}")
        else:
            print(f"{a} / {b} = 除数不能为0")
            
    except ValueError:
        print("输入错误，请输入有效数字")
    except Exception as e:
        print(f"发生错误: {e}")

def list_demo():
    """列表操作演示"""
    print("\n列表操作演示:")
    print("-" * 30)
    
    # 创建列表
    fruits = ["苹果", "香蕉", "橙子", "葡萄", "草莓"]
    print(f"原始列表: {fruits}")
    
    # 添加元素
    fruits.append("芒果")
    print(f"添加芒果后: {fruits}")
    
    # 删除元素
    fruits.remove("香蕉")
    print(f"删除香蕉后: {fruits}")
    
    # 排序
    fruits.sort()
    print(f"排序后: {fruits}")
    
    # 遍历
    print("遍历列表:")
    for i, fruit in enumerate(fruits, 1):
        print(f"  {i}. {fruit}")

def file_demo():
    """文件操作演示"""
    print("\n文件操作演示:")
    print("-" * 30)
    
    filename = "demo_file.txt"
    
    try:
        # 写入文件
        with open(filename, 'w', encoding='utf-8') as f:
            f.write("这是一个演示文件\n")
            f.write(f"创建时间: {datetime.now()}\n")
            f.write("Python文件操作演示\n")
        
        print(f"已创建文件: {filename}")
        
        # 读取文件
        with open(filename, 'r', encoding='utf-8') as f:
            content = f.read()
            print("文件内容:")
            print(content)
        
        # 删除文件
        os.remove(filename)
        print(f"已删除文件: {filename}")
        
    except Exception as e:
        print(f"文件操作错误: {e}")

def countdown_demo():
    """倒计时演示"""
    print("\n倒计时演示 (5秒):")
    print("-" * 30)
    
    for i in range(5, 0, -1):
        print(f"倒计时: {i}", end="\r")
        time.sleep(1)
    print("倒计时结束!     ")

def main():
    """主函数"""
    print_system_info()
    
    while True:
        print("\n请选择要运行的演示:")
        print("1. 简单计算器")
        print("2. 列表操作演示")
        print("3. 文件操作演示")
        print("4. 倒计时演示")
        print("5. 退出")
        
        choice = input("\n请输入选择 (1-5): ").strip()
        
        if choice == "1":
            simple_calculator()
        elif choice == "2":
            list_demo()
        elif choice == "3":
            file_demo()
        elif choice == "4":
            countdown_demo()
        elif choice == "5":
            print("感谢使用Python Demo!")
            break
        else:
            print("无效选择，请重新输入")

if __name__ == "__main__":
    main()
