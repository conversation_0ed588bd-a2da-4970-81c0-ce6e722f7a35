package com.epoint.cnbm.ztb.erpandprocurementplatform.impl;

import com.alibaba.fastjson.JSONObject;
import com.epoint.cnbm.ztb.erpandprocurementplatform.api.IErpAndProcurementPlatformApi;
import com.epoint.cnbm.ztb.erpandprocurementplatform.entity.ReturnInfo;
import com.epoint.cnbm.ztb.erpandprocurementplatform.service.ErpAndProcurementPlatformService;
import com.epoint.cnbm.ztb.erpandprocurementplatform.service.TokenSecurityUtils;
import com.epoint.core.utils.date.EpointDateUtil;
import com.epoint.core.utils.security.TokenUtils;
import com.epoint.core.utils.string.StringUtil;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * @author: dfwang
 * @Description: Erp系统和中建材招采系统接口对接Api 实现类
 * @date: 2024-01-14
 */
@Service
public class ErpAndProcurementPlatformApiImpl implements IErpAndProcurementPlatformApi
{

    private static final Logger logger = Logger.getLogger(ErpAndProcurementPlatformApiImpl.class);

    @Autowired
    protected TokenSecurityUtils tokenSecurityUtils;

    @Autowired
    protected ErpAndProcurementPlatformService erpAndProcurementPlatformService;

    @Override
    public String test() {
        return "test IProcurementPlatformApi restful succ,the time now is "
                + EpointDateUtil.convertDate2String(new Date(), "yyyy-MM-dd HH:mm:ss");
    }

    @Override
    public String bidsectionResultInfo(String requestJson) {
        logger.info("bidsectionResultInfo(requestJson)-----" + requestJson);
        ReturnInfo returnInfo = new ReturnInfo();
        // 描述：进行token和数据校验
        JSONObject requestJsonObject = JSONObject.parseObject(requestJson);
        String token = requestJsonObject.getString("token");
        if (token == null || !TokenUtils.validateToken(token)) {
            return returnInfo.getFail("token is error").toJSONString();
        }

        // 描述：进行数据校验
        String verificationResult = getErpAndProcurementPlatformService().verificationHandle(requestJsonObject);
        if (StringUtil.isNotBlank(verificationResult)) {
            return returnInfo.getFail(verificationResult).toJSONString();
        }

        // 描述：进行数据获取
        List<JSONObject> list = getErpAndProcurementPlatformService().dealBidsectionResultData2(requestJsonObject);
        return returnInfo.getSuccess("success", list).toJSONString();
    }

    @Override
    public String erpToTradeplatInfo(String requestJson) {
        logger.info("erpToTradeplatInfo(requestJson)-----" + requestJson);
        ReturnInfo returnInfo = new ReturnInfo();

        // 描述：进行token校验
        JSONObject requestJsonObject = JSONObject.parseObject(requestJson);
        String token = requestJsonObject.getString("token");
        if (token == null || !TokenUtils.validateToken(token)) {
            return returnInfo.getFail("token is error").toJSONString();
        }

        // 描述：进行数据处理
        String dealresult = getErpAndProcurementPlatformService().dealErpToTradeplatData(requestJson);
        if (StringUtil.isBlank(dealresult)) {
            return returnInfo.getSuccess("success", new JSONObject()).toJSONString();
        }
        else {
            return returnInfo.getFail(dealresult).toJSONString();
        }
    }

    @Override
    public String getToken() {
        try {
            return TokenUtils.createToken("fzjczc_xjsn");
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 获取erpAndProcurementPlatformService实例对象
     * @return
     */
    public ErpAndProcurementPlatformService getErpAndProcurementPlatformService() {
        if (erpAndProcurementPlatformService == null) {
            erpAndProcurementPlatformService = new ErpAndProcurementPlatformService();
        }
        return erpAndProcurementPlatformService;
    }

    /**
     * 获取TokenSecurityUtils实例对象
     * @return
     */
    public TokenSecurityUtils getTokenSecurityUtils() {
        if(tokenSecurityUtils == null) {
            tokenSecurityUtils = new TokenSecurityUtils();
        }
        return tokenSecurityUtils;
    }

    @Override
    public String bidsectioncandidateinfo(String requestJson) {
        logger.info("bidsectioncandidateinfo(requestJson)-----" + requestJson);
        ReturnInfo returnInfo = new ReturnInfo();
        // 描述：进行token和数据校验
        JSONObject requestJsonObject = JSONObject.parseObject(requestJson);
        String token = requestJsonObject.getString("token");
        if (token == null || !TokenUtils.validateToken(token)) {
            return returnInfo.getFail("token is error").toJSONString();
        }

        // 描述：进行数据校验
        String verificationResult = getErpAndProcurementPlatformService().bidseHandle(requestJsonObject);
        if (StringUtil.isNotBlank(verificationResult)) {
            return returnInfo.getFail(verificationResult).toJSONString();
        }

        // 描述：进行数据获取
        List<JSONObject> list = getErpAndProcurementPlatformService().dealBidsectioncAndIdateInfo(requestJsonObject);
        return returnInfo.getSuccess("success", list).toJSONString();
    }

}
