#!/usr/bin/env python3
"""
测试urllib3警告是否解决
"""

import warnings
import requests

def test_urllib3_warning():
    print("🔍 测试urllib3警告是否解决...")
    print("=" * 50)
    
    # 捕获所有警告
    with warnings.catch_warnings(record=True) as w:
        warnings.simplefilter("always")
        
        # 导入requests会触发urllib3相关警告
        import urllib3
        print(f"urllib3 版本: {urllib3.__version__}")
        
        # 尝试一个简单的HTTP请求
        try:
            response = requests.get("https://httpbin.org/get", timeout=10)
            print(f"HTTP请求测试: 成功 (状态码: {response.status_code})")
        except Exception as e:
            print(f"HTTP请求测试: 失败 ({e})")
        
        # 检查是否有urllib3相关警告
        urllib3_warnings = [warning for warning in w if 'urllib3' in str(warning.message)]
        
        if urllib3_warnings:
            print(f"\n⚠️  仍有 {len(urllib3_warnings)} 个urllib3警告:")
            for warning in urllib3_warnings:
                print(f"   - {warning.message}")
        else:
            print("\n✅ 没有urllib3相关警告！")
    
    print("=" * 50)
    print("测试完成")

if __name__ == "__main__":
    test_urllib3_warning()
