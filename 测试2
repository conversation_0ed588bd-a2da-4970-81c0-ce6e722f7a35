import com.epoint.rule.action.StreamingIterator


def main(arg1) {

	def chgline = { line ->
    def charCount = 0
    def inTitleSequence = false
    line.eachWithIndex { ch, idx ->
        if ((ch >= '\u4e00' && ch <= '\u9fff') || '，。？！；：、'.contains(ch)) {
            charCount++
            inTitleSequence = false
        } else if (ch.isNumber() || ch == '.') {
            if (!inTitleSequence) {
                charCount++
                inTitleSequence = true
            }
        } else {
            inTitleSequence = false
        }
    }
    return charCount
} 

	def ret = new StreamingIterator(arg1,chgline).number


	return [
		result : ret
	]
}